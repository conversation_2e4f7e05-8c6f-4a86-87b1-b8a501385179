//STMMCG04 JOB STMMCG04,'STMMCG04',MSGCLASS=X,CLASS=7,
//     NOTIFY=&SYSUID
//STEP01   EXEC IGYWCL
//SYSPRINT DD   SYSOUT=X
//<PERSON><PERSON><PERSON><PERSON>    DD   *
      *------------------------*
       IDENTIFICATION DIVISION.
      *------------------------*
       PROGRAM-ID.   STMMCG04.
      *----------------------------------------------------------------*
      * PROGRAM NAME: STMMCG04
      * AUTHOR      : ITTICHOTE CH.
      * STARTED DATE: 31/01/2017
      *----------------------------------------------------------------*
      * DESCRIPTION : THIS PROGRAM WILL READ STMT FILE AND ACCT PROFILE
      *               AND DETAIL STMT FILE FOR FILLERING ONLY ACCOUNT
      *               THEN WRITE THE OUTPUT FILE INCLUDE DETAIL.
      *               GENERATE LCC DETAIL.
      * PB17-100560 : Change  Logic Check DR/CR Transaction from
      *               Amt. Balance to Amt. Transanction
      * OP60030006  : 09/05/17 - ICAS LOCAL COLLECT - POST BY PAY-IN
      *                          NOT GENERATE STMT FOR "POST????????"
      * CB60060003  : 08/08/17 - SCG CASH MANGEMENT CHANGE REQUEST
      *                          GENERATE CHQ RETURN FROM ATS (ATS-BCS.)
      *------------------------------------------------------------*
       ENVIRONMENT DIVISION.
      *---------------------*
       CONFIGURATION SECTION.
       SOURCE-COMPUTER. IBM-4341.
       OBJECT-COMPUTER. IBM-4341.
       SPECIAL-NAMES.   C01 IS  NEXT-PAGE.
      *----------------------*
       INPUT-OUTPUT SECTION.
      *----------------------*
       FILE-CONTROL.

      *** READ ACCOUNT PROFILE (PS INPUT)
            SELECT ACCT-IN-FL ASSIGN    TO  ACCPROF
                   FILE STATUS          IS  ACC-PROF-STAT.

      *** READ STMT FILE (VSAM INPUT)
            SELECT STMT-IN-FL  ASSIGN   TO  STMTINF
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  STMT-CNTL-KEY
                   FILE STATUS          IS  STMT-IN-STAT.

      *** READ DETAIL FILE (VSAM INPUT)
            SELECT DETL-IN-FL  ASSIGN   TO  DETLINF
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  DETL-CNTL-KEY
                   FILE STATUS          IS  DETL-IN-STAT.

      *** WRITE STMT FILE (PS OUTPUT)
            SELECT STMT-OUT-FL ASSIGN   TO  STMTOUTF
                   FILE STATUS          IS  STMT-OUT-STAT.

      *** WRITE PRTF FILE (PS OUTPUT)
            SELECT PRTF-OUT-FL ASSIGN   TO  PRTFOUTF
                   FILE STATUS          IS  PRTF-OUT-STAT.

      *--------------*
       DATA DIVISION.
      *--------------*
       FILE SECTION.

       FD ACCT-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   200 CHARACTERS
           DATA   RECORD     IS ACCT-IN-REC.
       01 ACCT-IN-REC.
          05 FILLER                 PIC  X(16).
          05 ACCT-NO                PIC  X(10).
          05 FILLER                 PIC  X(03).
          05 ACCT-NAME              PIC  X(50).
          05 FILLER                 PIC  X(71).
          05 ACCT-MCASH-FLG         PIC  X(01).
          05 ACCT-PAYMENT-FLG       PIC  X(01).
          05 ACCT-LCC-FLG           PIC  X(01).
          05 ACCT-BPAY-FLG          PIC  X(01).
          05 ACCT-EBPP-FLG          PIC  X(01).
          05 FILLER                 PIC  X(45).

       FD STMT-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   700 CHARACTERS
           DATA   RECORD     IS  STMT-IN-REC.
       01 STMT-IN-REC.
          05 STMT-CNTL-KEY.
             07 STMT-IN-ACCT-KEY   PIC X(10).
             07 STMT-IN-SEQ-KEY    PIC 9(06).
          05 STMT-IN-REC-TYP           PIC  X(01).
          05 FILLER                    PIC  X(05).
          05 STMT-IN-ACCT-NO           PIC  X(10).
          05 FILLER                    PIC  X(15).
          05 STMT-IN-BAL               PIC  9(14)V99.
          05 STMT-IN-BAL-SIGN          PIC  X(01).
          05 STMT-IN-AMT               PIC  9(13)V99.
          05 STMT-IN-DC-CODE           PIC  X(02).
          05 FILLER                    PIC  X(47).
          05 STMT-IN-SEQ-NO            PIC  9(06).
          05 STMT-IN-CHG-NO10          PIC  9(10).
          05 STMT-IN-DESC40            PIC  X(40).
          05 STMT-IN-SYMBOL            PIC  X(03).
          05 STMT-IN-CHANNEL           PIC  X(04).
      *   05 FILLER.                   PIC  X(350).
          05 STMT-STMTBPDT.
             07 STMT-BANK-CD             PIC X(02).
             07 FILLER                   PIC X(01).
             07 STMT-CURR-CD             PIC X(03).
             07 FILLER                   PIC X(01).
             07 STMT-ACCT                PIC 9(11).
             07 FILLER                   PIC X(01).
             07 STMT-R-DATE              PIC X(10).
             07 FILLER                   PIC X(01).
             07 STMT-SEQ-NO              PIC 9(09).
             07 FILLER                   PIC X(01).
             07 STMT-D-TRAN              PIC X(10).
             07 FILLER                   PIC X(01).
             07 STMT-D-DATE              PIC X(10).
             07 FILLER                   PIC X(01).
             07 STMT-D-TIME              PIC X(05).
             07 FILLER                   PIC X(01).
             07 STMT-T-CODE              PIC X(03).
             07 FILLER                   PIC X(01).
             07 STMT-CHANNEL             PIC X(04).
             07 FILLER                   PIC X(01).
             07 STMT-TR                  PIC X(02).
             07 FILLER                   PIC X(01).
             07 STMT-BR                  PIC X(04).
             07 FILLER                   PIC X(01).
             07 STMT-TERMINAL            PIC X(05).
             07 FILLER                   PIC X(01).
             07 STMT-CHQNO               PIC X(08).
             07 FILLER                   PIC X(01).
             07 STMT-AMT-S               PIC X(01).
             07 FILLER                   PIC X(01).
             07 STMT-AMT                 PIC 9999999999999.99.
             07 FILLER                   PIC X(01).
             07 STMT-BAL                 PIC X(17).
             07 FILLER                   PIC X(01).
             07 STMT-DESC                PIC X(40).
             07 FILLER                   PIC X(23).
             07 FILLER                   PIC X(01).
             07 STMT-BR-OF-AC            PIC X(04).
             07 FILLER                   PIC X(01).
             07 STMT-ACC-TYP             PIC X(02).
             07 FILLER                   PIC X(01).
             07 STMT-CUST-NME            PIC X(50).
             07 FILLER                   PIC X(01).
             07 STMT-REF1                PIC X(20).
             07 FILLER                   PIC X(01).
             07 STMT-REF2                PIC X(20).
             07 FILLER                   PIC X(01).
             07 STMT-STATUS-MONEY        PIC X(03).
             07 FILLER                   PIC X(01).
             07 STMT-CH-CODE             PIC X(04).
             07 FILLER                   PIC X(01).
             07 STMT-AVL-BAL             PIC X(17).
             07 FILLER                   PIC X(01).
             07 FILLER                   PIC X(14).
             07 STMT-AC-SEQ-NO           PIC X(06).
             07 FILLER                   PIC X(01).
          05 FILLER                    PIC  X(09).
          05 EBPP-STMTDT               PIC  X(150).

       FD DETL-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   600 CHARACTERS
           DATA   RECORD     IS  DETL-IN-REC.
       01 DETL-IN-REC.
      *   05 BC-CNTL-REC.
          05 DETL-CNTL-KEY.
             07 BC-PJ-CODE             PIC X(03).
             07 BC-REPORT-NO           PIC X(03).
             07 BC-ACC-NO              PIC X(10).
             07 BC-INFO-DATE           PIC X(06).
             07 BC-SERIAL-NO           PIC X(07).
          05 BC-DETL-REC.
             07 BC-BTRDATE               PIC X(08).
             07 BC-BACNO                 PIC X(11).
             07 BC-BACNAME               PIC X(40).
             07 BC-BCHQBKNA              PIC X(20).
             07 BC-BCHQBRNA              PIC X(20).
             07 BC-BCHQRBK               PIC X(02).
             07 BC-BCHQRBR               PIC X(04).
      * BC-BCHQDATE FORMAT DD/MM/YY
             07 BC-BCHQDATE            PIC X(08).
             07 BC-BCHQNO              PIC X(07).
             07 BC-BCHQAMT             PIC 9999999999999.99.
             07 BC-BCHQCOM             PIC 9999999999999.99.
             07 BC-BCHQNET             PIC 9999999999999.99.
             07 BC-BREFNO              PIC X(10).
             07 BC-BINVOICE            PIC X(100).
             07 BC-BCODE               PIC X(40).
             07 BC-BOTHER              PIC X(35).
      * BC-BCHQTYPE
      * - CSH = CASH
      * - BC  = BILL FOR COLLECTION
      * - ABC = ADVANCE ON BILLS FOR COLLECTION
      * - CBD = CLEAN BILLS DISCOUNTED
      * - BR  = BILLS RECEIVE
      * - LCC = LOCAL CLEARING COLLECTION
             07 BC-BCHQTYPE            PIC X(03).
      * BC-BSTATUS (S, C, P, A, R, O)
             07 BC-BSTATUS             PIC X(01).
      * BC-BCSDATE FORMAT DD/MM/YY
             07 BC-BCSDATE             PIC X(08).
      * BC-BCNDATE FORMAT DD/MM/YY
             07 BC-BSNDATE             PIC X(08).
             07 BC-BCHQBK              PIC X(02).
             07 BC-BCHQBR              PIC X(04).
             07 BC-BSETBR              PIC X(04).
             07 BC-BSETBRNA            PIC X(20).
             07 BC-BRCVNA              PIC X(20).
             07 BC-BCHQCOR             PIC X(30).
             07 BC-BRETNO              PIC X(02).
             07 BC-BRETTIME            PIC X(03).
             07 BC-BCINDATE            PIC X(08).
             07 BC-BCEDATE             PIC X(02).
             07 BC-BCDDATE             PIC X(02).
             07 BC-BABCCOM             PIC X(05).
      * BC-BCHQSTAT (0, 1, 2)
             07 BC-BCHQSTAT            PIC X(01).
      * BC-BCHQSTAT (CL, BC)
             07 BC-BCOLTYPE            PIC X(02).
             07 BC-BSCBBKCD            PIC X(03).
             07 BC-BRCVBR              PIC X(04).
             07 BC-BRCVBRNA            PIC X(20).
             07 BC-BCHQRBK-3           PIC X(03).
             07 BC-BCHQNO-8            PIC X(08).
             07 BC-BCHQBK-3            PIC X(03).
             07 FILLER                 PIC X(36).
             07 BC-REF-KEY             PIC X(16).

       FD STMT-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS  1300 CHARACTERS
           DATA   RECORD     IS STMT-OUT-REC.
       01 STMT-OUT-REC.
          05 SOUT-CNTL-KEY.
             07 SOUT-ACCT-KEY   PIC X(10).
             07 SOUT-SEQ-KEY    PIC X(06).
          05 SOUT-IN-REC-TYP           PIC  X(01).
          05 FILLER                    PIC  X(05).
          05 SOUT-IN-ACCT-NO           PIC  X(10).
          05 FILLER                    PIC  X(15).
          05 SOUT-IN-BAL               PIC  9(14)V99.
          05 SOUT-IN-BAL-SIGN          PIC  X(01).
          05 SOUT-IN-AMT               PIC  9(13)V99.
          05 SOUT-IN-DC-CODE           PIC  X(02).
          05 FILLER                    PIC  X(47).
          05 SOUT-IN-SEQ-NO            PIC  9(06).
          05 SOUT-IN-CHG-NO10          PIC  9(10).
          05 SOUT-IN-DESC40            PIC  X(40).
          05 SOUT-IN-SYMBOL            PIC  X(03).
          05 SOUT-IN-CHANNEL           PIC  X(04).
      *   05 SOUT-STMTBPDT             PIC  X(350).
          05 SOUT-STMTBPDT.
             07 SOUT-BANK-CD             PIC X(02).
             07 FILLER                   PIC X(01).
             07 SOUT-CURR-CD             PIC X(03).
             07 FILLER                   PIC X(01).
             07 SOUT-ACCT                PIC 9(11).
             07 FILLER                   PIC X(01).
             07 SOUT-R-DATE              PIC X(10).
             07 FILLER                   PIC X(01).
             07 SOUT-SEQ-NO              PIC 9(09).
             07 FILLER                   PIC X(01).
             07 SOUT-D-TRAN              PIC X(10).
             07 FILLER                   PIC X(01).
             07 SOUT-D-DATE              PIC X(10).
             07 FILLER                   PIC X(01).
             07 SOUT-D-TIME              PIC X(05).
             07 FILLER                   PIC X(01).
             07 SOUT-T-CODE              PIC X(03).
             07 FILLER                   PIC X(01).
             07 SOUT-CHANNEL             PIC X(04).
             07 FILLER                   PIC X(01).
             07 SOUT-TR                  PIC X(02).
             07 FILLER                   PIC X(01).
             07 SOUT-BR                  PIC X(04).
             07 FILLER                   PIC X(01).
             07 SOUT-TERMINAL            PIC X(05).
             07 FILLER                   PIC X(01).
             07 SOUT-CHQNO               PIC X(08).
             07 FILLER                   PIC X(01).
             07 SOUT-AMT-S               PIC X(01).
             07 FILLER                   PIC X(01).
             07 SOUT-AMT                 PIC 9999999999999.99.
             07 FILLER                   PIC X(01).
             07 SOUT-BAL                 PIC -9999999999999.99.
             07 FILLER                   PIC X(01).
             07 SOUT-DESC                PIC X(40).
             07 FILLER                   PIC X(23).
             07 FILLER                   PIC X(01).
             07 SOUT-BR-OF-AC            PIC X(04).
             07 FILLER                   PIC X(01).
             07 SOUT-ACC-TYP             PIC X(02).
             07 FILLER                   PIC X(01).
             07 SOUT-CUST-NME            PIC X(50).
             07 FILLER                   PIC X(01).
             07 SOUT-REF1                PIC X(20).
             07 FILLER                   PIC X(01).
             07 SOUT-REF2                PIC X(20).
             07 FILLER                   PIC X(01).
             07 SOUT-STATUS-MONEY        PIC X(03).
             07 FILLER                   PIC X(01).
             07 SOUT-CH-CODE             PIC X(04).
             07 FILLER                   PIC X(01).
             07 SOUT-AVL-BAL             PIC X(17).
             07 FILLER                   PIC X(01).
             07 FILLER                   PIC X(14).
             07 SOUT-AC-SEQ-NO           PIC 9(06).
             07 FILLER                   PIC X(01).
          05 FILLER                    PIC  X(09).
          05 SOUT-EBPP-DETL            PIC  X(150).
          05 OUT-BC-DETL-REC.
             07 OUT-BC-BTRDATE               PIC X(08).
             07 OUT-BC-BACNO                 PIC X(11).
             07 OUT-BC-BACNAME               PIC X(40).
             07 OUT-BC-BCHQBKNA              PIC X(20).
             07 OUT-BC-BCHQBRNA              PIC X(20).
             07 OUT-BC-BCHQRBK               PIC X(02).
             07 OUT-BC-BCHQRBR               PIC X(04).
      * BC-BCHQDATE FORMAT DD/MM/YY
             07 OUT-BC-BCHQDATE            PIC X(08).
             07 OUT-BC-BCHQNO              PIC X(07).
             07 OUT-BC-BCHQAMT             PIC 9999999999999.99.
             07 OUT-BC-BCHQCOM             PIC 9999999999999.99.
             07 OUT-BC-BCHQNET             PIC 9999999999999.99.
             07 OUT-BC-BREFNO              PIC X(10).
             07 OUT-BC-BINVOICE            PIC X(100).
             07 OUT-BC-BCODE               PIC X(40).
             07 OUT-BC-BOTHER              PIC X(35).
      * BC-BCHQTYPE
      * - CSH = CASH
      * - BC  = BILL FOR COLLECTION
      * - ABC = ADVANCE ON BILLS FOR COLLECTION
      * - CBD = CLEAN BILLS DISCOUNTED
      * - BR  = BILLS RECEIVE
      * - LCC = LOCAL CLEARING COLLECTION
             07 OUT-BC-BCHQTYPE            PIC X(03).
      * BC-BSTATUS (S, C, P, A, R, O)
             07 OUT-BC-BSTATUS             PIC X(01).
      * BC-BCSDATE FORMAT DD/MM/YY
             07 OUT-BC-BCSDATE             PIC X(08).
      * BC-BCNDATE FORMAT DD/MM/YY
             07 OUT-BC-BSNDATE             PIC X(08).
             07 OUT-BC-BCHQBK              PIC X(02).
             07 OUT-BC-BCHQBR              PIC X(04).
             07 OUT-BC-BSETBR              PIC X(04).
             07 OUT-BC-BSETBRNA            PIC X(20).
             07 OUT-BC-BRCVNA              PIC X(20).
             07 OUT-BC-BCHQCOR             PIC X(30).
             07 OUT-BC-BRETNO              PIC X(02).
             07 OUT-BC-BRETTIME            PIC X(03).
             07 OUT-BC-BCINDATE            PIC X(08).
             07 OUT-BC-BCEDATE             PIC X(02).
             07 OUT-BC-BCDDATE             PIC X(02).
             07 OUT-BC-BABCCOM             PIC X(05).
      * BC-BCHQSTAT (0, 1, 2)
             07 OUT-BC-BCHQSTAT            PIC X(01).
      * BC-BCHQSTAT (CL, BC)
             07 OUT-BC-BCOLTYPE            PIC X(02).
             07 OUT-BC-BSCBBKCD            PIC X(03).
             07 OUT-BC-BRCVBR              PIC X(04).
             07 OUT-BC-BRCVBRNA            PIC X(20).
             07 OUT-BC-BCHQRBK-3           PIC X(03).
             07 OUT-BC-BCHQNO-8            PIC X(08).
             07 OUT-BC-BCHQBK-3            PIC X(03).
             07 FILLER                     PIC X(36).
             07 OUT-BC-REF-KEY             PIC X(16).
             07 FILLER                     PIC X(17).

       FD PRTF-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   132 CHARACTERS
           DATA   RECORD     IS PRTF-OUT-REC.
       01 PRTF-OUT-REC.
          05 FILLER             PIC X(132).

      *------------------------*
       WORKING-STORAGE SECTION.
      *------------------------*
       77 SW-OPENINFL                  PIC X(01)   VALUE 'Y'.
          88 ERR-OPENINFL                          VALUE 'N'.
          88 SUCCESS-OPENINFL                      VALUE 'Y'.

       77 SW-ACCTINF                   PIC X(01)   VALUE 'N'.
          88 EOF-ACCTINF                           VALUE 'Y'.
          88 NOT-EOF-ACCTINF                       VALUE 'N'.
       77 SW-STMTINF                   PIC X(01)   VALUE 'N'.
          88 EOF-STMTINF                           VALUE 'Y'.
          88 NOT-EOF-STMTINF                       VALUE 'N'.

       77 SW-ACCT-LCC-FLG             PIC X(01)    VALUE 'N'.
          88 GEN-LCC-STMTINF                       VALUE 'Y'.
          88 NOT-GEN-LCC-STMTINF                   VALUE 'N'.

       77 SW-STMTINF-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-STMTINF                         VALUE 'Y'.
          88 NOT-FOUND-STMTINF                     VALUE 'N'.
       77 SW-STMTINF-CHANGE            PIC X(01)   VALUE 'N'.
          88 CHANGE-STMTINF                        VALUE 'Y'.
          88 NOT-CHANGE-STMTINF                    VALUE 'N'.

       77 SW-DETLINF                   PIC X(01)   VALUE 'N'.
          88 EOF-DETLINF                           VALUE 'Y'.
          88 NOT-EOF-DETLINF                       VALUE 'N'.
       77 SW-DETLINF-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-DETLINF                         VALUE 'Y'.
          88 NOT-FOUND-DETLINF                     VALUE 'N'.
       77 SW-DETLINF-CHANGE            PIC X(01)   VALUE 'N'.
          88 CHANGE-DETLINF                        VALUE 'Y'.
          88 NOT-CHANGE-DETLINF                    VALUE 'N'.

       77 SW-FOUND-BC-STMT             PIC X(01)   VALUE 'N'.
          88 FOUND-BC-STMT                         VALUE 'Y'.
          88 NOT-FOUND-BC-STMT                     VALUE 'N'.

       77 SW-FOUND-OTCL-STMT           PIC X(01)   VALUE 'N'.
          88 FOUND-OTCL-STMT                       VALUE 'Y'.
          88 NOT-FOUND-OTCL-STMT                   VALUE 'N'.

       77 SW-FOUND-AIR-STMT            PIC X(01)   VALUE 'N'.
          88 FOUND-AIR-STMT                        VALUE 'Y'.
          88 NOT-FOUND-AIR-STMT                    VALUE 'N'.

      *>> OP60030006
       77 SW-GEN-DETAIL                PIC X(01)   VALUE 'N'.
          88 GEN-DETAIL                            VALUE 'Y'.
          88 NOT-GEN-DETAIL                        VALUE 'N'.
      *<< OP60030006

       01  WK-FILE-STATUS.
           03  ACC-PROF-STAT       PIC  X(02).
           03  STMT-IN-STAT        PIC  X(02).
           03  DETL-IN-STAT        PIC  X(02).
           03  STMT-OUT-STAT       PIC  X(02).
           03  PRTF-OUT-STAT       PIC  X(02).
       01  WK-DATE-X               PIC  9(08).
       01  WK-DATE-Y.
           03  WK-Y4               PIC  9(04).
           03  WK-MM               PIC  9(02).
           03  WK-DD               PIC  9(02).

       01  WK-INT-DATE-X           PIC  9(08).
       01  WK-INT-DATE-Y.
           03  FILLER              PIC  9(02) VALUE ZEROS.
           03  WK-INT-D6           PIC  9(06).

       01  WK-ORG-REC-IN-AMT       PIC  S9(13)V99 VALUE ZEROS.
       01  WK-NEW-SEQ-NO           PIC  9(06) VALUE ZEROS.
       01  WK-NEW-STMT-IN-BAL      PIC  S9(14)V99 VALUE ZEROS.
       01  WK-STMT-IN-AMT          PIC  S9(13)V99 VALUE ZEROS.
       01  CNV-STMT-IN-AMT         PIC  -99999999999999.99.

       01 TAIL-IN-REC.
          05 TAIL-CNTL-KEY.
             07 TAIL-IN-ACCT-KEY   PIC X(10).
             07 TAIL-IN-SEQ-KEY    PIC 9(06).
          05 TAIL-IN-REC-TYP           PIC  X(01).
          05 FILLER                    PIC  X(05).
          05 TAIL-IN-ACCT-NO           PIC  X(10).
          05 FILLER                    PIC  X(15).
          05 TAIL-IN-BAL               PIC  9(14)V99.
          05 TAIL-IN-BAL-SIGN          PIC  X(01).
          05 TAIL-IN-AMT               PIC  9(14)V99.
          05 TAIL-IN-DC-CODE           PIC  X(01).
          05 TAIL-ALL-SEQ-NO           PIC  9(06).
          05 FILLER                    PIC  X(41).
          05 TAIL-IN-SEQ-NO            PIC  9(06).
          05 TAIL-IN-CHG-NO10          PIC  9(10).
          05 TAIL-IN-DESC40            PIC  X(40).
          05 TAIL-IN-SYMBOL            PIC  X(03).
          05 TAIL-IN-CHANNEL           PIC  X(04).
          05 FILLER                    PIC  X(350).

      *---------------------------------------------------------*
       PROCEDURE DIVISION.
      *-------------------*
       0000-MAIN-PROCESS.

           PERFORM  1000-INITIAL-RTN   THRU  1000-EXIT.
           PERFORM  2000-MAIN-PROCESS  THRU  2000-EXIT.
           PERFORM  9999-CLOSE-RTN     THRU  9999-EXIT.

       0000-EXIT.    EXIT.

      *-------------------------------------------------------*
       1000-INITIAL-RTN.

           SET SUCCESS-OPENINFL TO TRUE.

           OPEN INPUT  ACCT-IN-FL.

           IF  ACC-PROF-STAT  NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'       UPON CONSOLE
               DISPLAY 'OPEN ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN INPUT  STMT-IN-FL.
           IF  STMT-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'       UPON CONSOLE
               DISPLAY 'OPEN STMTINF ERROR,CODE  = '    STMT-IN-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN STMTINF ERROR,CODE  = '    STMT-IN-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN INPUT  DETL-IN-FL.
           IF  DETL-IN-STAT NOT = '00' AND
               DETL-IN-STAT NOT = '35'
               DISPLAY '*** PROGRAM STMMCG04 ***'       UPON CONSOLE
               DISPLAY 'OPEN DETLINF ERROR,CODE  = '    DETL-IN-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN DETLINF ERROR,CODE  = '    DETL-IN-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN OUTPUT STMT-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'        UPON CONSOLE
               DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
                                                         UPON CONSOLE
               DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN OUTPUT PRTF-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'        UPON CONSOLE
               DISPLAY 'OPEN PRTFOUTF ERROR,CODE  = ' PRTF-OUT-STAT
                                                         UPON CONSOLE
               DISPLAY 'OPEN PRTFOUTF ERROR,CODE  = ' PRTF-OUT-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

       1000-EXIT.    EXIT.
      *-------------------------------------------------------*
       2000-MAIN-PROCESS.
           PERFORM 2100-READ-ACCTINF-RTN  THRU 2100-EXIT.
           PERFORM UNTIL EOF-ACCTINF
              PERFORM 3000-PROCESS-GEN-STMT-RTN  THRU 3000-EXIT
              PERFORM 2100-READ-ACCTINF-RTN  THRU 2100-EXIT
           END-PERFORM.
            GO TO  2000-EXIT.
       2000-EXIT.    EXIT.
      *-------------------------------------------------------*
       2100-READ-ACCTINF-RTN.
           MOVE ZERO   TO WK-NEW-SEQ-NO.
           SET NOT-GEN-LCC-STMTINF  TO TRUE.

           READ ACCT-IN-FL AT END
                SET EOF-ACCTINF TO TRUE.

           IF ACCT-LCC-FLG = 'Y'
              SET GEN-LCC-STMTINF  TO TRUE.

       2100-EXIT.    EXIT.
      *-------------------------------------------------------*
       2200-READ-STMTINF-RTN.

           SET NOT-EOF-STMTINF TO TRUE.
           READ STMT-IN-FL NEXT AT END
                SET EOF-STMTINF  TO TRUE
                GO TO 2200-EXIT.
           IF STMT-IN-STAT NOT = '00' AND
              STMT-IN-STAT NOT = '10'
              DISPLAY '*** PROGRAM STMMCG04 ***'  UPON CONSOLE
              DISPLAY 'READ STMTINF IN ERROR,CODE = ' STMT-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'READ STMTINF IN ERROR,CODE = ' STMT-IN-STAT
              SET EOF-STMTINF TO TRUE
           END-IF.

           IF ACCT-NO NOT = STMT-IN-ACCT-KEY
              SET EOF-STMTINF       TO TRUE.

       2200-EXIT.    EXIT.
      *-------------------------------------------------------*
       2300-READ-DETLINF-RTN.

           READ DETL-IN-FL NEXT AT END
                SET EOF-DETLINF  TO TRUE
                GO TO 2300-EXIT.
           IF DETL-IN-STAT NOT = '00' AND
              DETL-IN-STAT NOT = '10'
              DISPLAY '*** PROGRAM STMMCG04 ***'  UPON CONSOLE
              DISPLAY 'READ DETLINF IN ERROR,CODE = ' DETL-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'READ DETLINF IN ERROR,CODE = ' DETL-IN-STAT
              SET EOF-DETLINF TO TRUE
           END-IF.

           IF STMT-IN-ACCT-KEY NOT = BC-ACC-NO
              SET NOT-FOUND-DETLINF TO TRUE
              SET EOF-DETLINF       TO TRUE.

       2300-EXIT.    EXIT.
      *-------------------------------------------------------*
       3000-PROCESS-GEN-STMT-RTN.
           MOVE ACCT-NO   TO  STMT-IN-ACCT-KEY
           MOVE '000000'  TO  STMT-IN-SEQ-KEY
      **   DISPLAY 'START GEN-STMT: ' STMT-IN-ACCT-KEY,
      **                              STMT-IN-SEQ-KEY
           SET   NOT-EOF-STMTINF     TO TRUE
           START STMT-IN-FL KEY IS GREATER THAN STMT-CNTL-KEY
              INVALID KEY
              DISPLAY 'NO STATEMENT TRANSACTION FOR THIS A/C '
                       STMT-IN-ACCT-KEY
              SET NOT-FOUND-STMTINF TO TRUE
              GO TO 3000-EXIT.

           PERFORM 2200-READ-STMTINF-RTN  THRU 2200-EXIT
           PERFORM UNTIL EOF-STMTINF
      *       DISPLAY '              : ' STMT-IN-ACCT-KEY,
      *                                  STMT-IN-SEQ-KEY

              IF  STMT-IN-REC-TYP  =  '1'
                  IF STMT-IN-BAL-SIGN = 'C'
                     COMPUTE WK-NEW-STMT-IN-BAL  = STMT-IN-BAL * 1
                  ELSE
                     COMPUTE WK-NEW-STMT-IN-BAL  = STMT-IN-BAL * -1
                  END-IF
              END-IF

      *>> OP60030006
              IF (STMT-IN-DESC40(1:4) = 'POST') OR
                 (STMT-IN-SYMBOL      = 'FE ')
                      SET NOT-GEN-DETAIL TO TRUE
              ELSE
                      SET GEN-DETAIL     TO TRUE
              END-IF
      *<< OP60030006
      *>> CB60060003
              IF (STMT-IN-CHANNEL = 'ATS ' AND
                   STMT-DESC(1:8) = 'ATS-BCS.')
                      SET GEN-DETAIL     TO TRUE
              END-IF
      *<< CB60060003

              IF ( STMT-IN-CHANNEL = 'BC  ' OR 'OTCL' OR 'AIR ' OR
                                     'ATS ' )
                 AND GEN-LCC-STMTINF
      *>> OP60030006
                 AND GEN-DETAIL
      *<< OP60030006

                 DISPLAY ' STEP 1 : ' ACCT-NO ' ' STMT-IN-CHANNEL
                                      ' ' STMT-DESC(1:16)
                 PERFORM 4000-PROCESS-GEN-DETL-STMT-RTN  THRU 4000-EXIT
              ELSE
                 ADD     1           TO  WK-NEW-SEQ-NO
                 PERFORM 5500-GEN-DATA-FOR-OUTPUT  THRU 5500-EXIT
                 PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
              END-IF
              PERFORM 2200-READ-STMTINF-RTN  THRU 2200-EXIT
           END-PERFORM.
       3000-EXIT.    EXIT.
      *-------------------------------------------------------*
       4000-PROCESS-GEN-DETL-STMT-RTN.

           IF STMT-IN-DC-CODE  = 'C '
              COMPUTE WK-ORG-REC-IN-AMT  = STMT-IN-AMT * 1
           ELSE
              COMPUTE WK-ORG-REC-IN-AMT  = STMT-IN-AMT * -1
           END-IF
      *    DISPLAY 'WK-ORG-REC-IN-AMT : ' WK-ORG-REC-IN-AMT
           MOVE '003'             TO BC-PJ-CODE
           MOVE '000'             TO BC-REPORT-NO
           MOVE STMT-IN-ACCT-KEY  TO BC-ACC-NO
           MOVE '000000'          TO BC-INFO-DATE
           MOVE '0000000'         TO BC-SERIAL-NO

           SET   NOT-EOF-DETLINF  TO TRUE
           START DETL-IN-FL KEY IS GREATER THAN DETL-CNTL-KEY
              INVALID KEY
              DISPLAY 'NO DETAIL STATEMENT TRANSACTION FOR THIS A/C '
                       BC-ACC-NO
              SET NOT-FOUND-DETLINF TO TRUE
              GO TO 4000-EXIT.

           PERFORM 2300-READ-DETLINF-RTN  THRU 2300-EXIT
           PERFORM UNTIL EOF-DETLINF
      *       DISPLAY '    GEN-DETL  : ' BC-ACC-NO,
      *                                  BC-REF-KEY
              PERFORM 6000-CHK-DETL-DATA-FOR-OUTPUT  THRU 6000-EXIT
              IF FOUND-BC-STMT OR FOUND-OTCL-STMT OR FOUND-AIR-STMT
                 ADD     1           TO  WK-NEW-SEQ-NO
                 PERFORM 5250-GEN-DETL-DATA-FOR-OUTPUT  THRU 5250-EXIT
                 PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
              END-IF
              PERFORM 2300-READ-DETLINF-RTN  THRU 2300-EXIT
           END-PERFORM.
           IF EOF-DETLINF AND
              ( WK-ORG-REC-IN-AMT NOT = ZERO )
      *       DISPLAY 'EOF-WK-ORG-REC-IN-AMT : ' WK-ORG-REC-IN-AMT
              PERFORM 5750-GEN-DIFF-DETL-DATA-OUTPUT  THRU 5750-EXIT
              PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
           END-IF.
       4000-EXIT.    EXIT.
      *-------------------------------------------------------*
       5000-WRITE-OUTPUT-STMT-RTN.

      *    DISPLAY 'WK-NEW-SEQ-NO : ' WK-NEW-SEQ-NO
           WRITE STMT-OUT-REC.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'         UPON CONSOLE
               DISPLAY 'WRITE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'WRITE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT.

       5000-EXIT.    EXIT.
      *-------------------------------------------------------*
       5250-GEN-DETL-DATA-FOR-OUTPUT.

           MOVE  STMT-IN-REC     TO STMT-OUT-REC
           MOVE  BC-DETL-REC     TO OUT-BC-DETL-REC
           MOVE  WK-NEW-SEQ-NO   TO SOUT-SEQ-KEY
                                    SOUT-IN-SEQ-NO
           COMPUTE  SOUT-SEQ-NO    = WK-NEW-SEQ-NO - 1
           COMPUTE  SOUT-AC-SEQ-NO = SOUT-SEQ-NO
      **   MOVE  BC-BCHQAMT      TO SOUT-IN-AMT
      **                            SOUT-AMT
           MOVE  BC-BCHQNET      TO SOUT-IN-AMT
                                    SOUT-AMT

           IF STMT-IN-DC-CODE     = 'C '
              COMPUTE WK-STMT-IN-AMT  = FUNCTION NUMVAL(BC-BCHQAMT)  * 1
           ELSE
              COMPUTE WK-STMT-IN-AMT  = FUNCTION NUMVAL(BC-BCHQAMT) * -1
           END-IF
           COMPUTE WK-NEW-STMT-IN-BAL = WK-NEW-STMT-IN-BAL +
                                        WK-STMT-IN-AMT

           COMPUTE WK-ORG-REC-IN-AMT  = WK-ORG-REC-IN-AMT -
                                        WK-STMT-IN-AMT
      *    DISPLAY 'COMP WK-ORG-REC-IN-AMT : ' WK-ORG-REC-IN-AMT
           IF WK-NEW-STMT-IN-BAL > 0
              MOVE 'C'      TO   SOUT-IN-BAL-SIGN
           ELSE
              MOVE 'D'      TO   SOUT-IN-BAL-SIGN
           END-IF
           MOVE  WK-NEW-STMT-IN-BAL TO  SOUT-IN-BAL
                                        SOUT-BAL
      *    MOVE  SPACE           TO SOUT-IN-DC-CODE
      *    MOVE  SPACE           TO SOUT-IN-DC-CODE(1:1)
      *                             SOUT-AMT-S.
      *>> PB17-100560 : Fix BUG
      **   IF WK-NEW-STMT-IN-BAL > 0
           IF WK-STMT-IN-AMT     > 0
      *<< PB17-100560
              MOVE 'C '      TO   SOUT-IN-DC-CODE
              MOVE 'C'       TO   SOUT-AMT-S
           ELSE
              MOVE 'D '      TO   SOUT-IN-DC-CODE
              MOVE 'D'       TO   SOUT-AMT-S
           END-IF.

       5250-EXIT.    EXIT.
      *-------------------------------------------------------*
       5500-GEN-DATA-FOR-OUTPUT.

           MOVE  STMT-IN-REC     TO STMT-OUT-REC.
           MOVE  WK-NEW-SEQ-NO   TO SOUT-SEQ-KEY
                                    SOUT-IN-SEQ-NO

           IF STMT-IN-REC-TYP  =  '2'
              IF STMT-IN-DC-CODE  = 'C '
                 COMPUTE WK-STMT-IN-AMT  = STMT-IN-AMT * 1
              ELSE
                 COMPUTE WK-STMT-IN-AMT  = STMT-IN-AMT * -1
              END-IF
              COMPUTE WK-NEW-STMT-IN-BAL = WK-NEW-STMT-IN-BAL +
                                           WK-STMT-IN-AMT
              IF WK-NEW-STMT-IN-BAL > 0
                 MOVE 'C'      TO   SOUT-IN-BAL-SIGN
              ELSE
                 MOVE 'D'      TO   SOUT-IN-BAL-SIGN
              END-IF
              MOVE  WK-NEW-STMT-IN-BAL TO  SOUT-IN-BAL
                                           SOUT-BAL
              COMPUTE  SOUT-SEQ-NO  =  WK-NEW-SEQ-NO - 1
              COMPUTE  SOUT-AC-SEQ-NO = SOUT-SEQ-NO
           END-IF.

           IF NOT-GEN-LCC-STMTINF
               MOVE  SPACE           TO SOUT-AVL-BAL
           END-IF.

           IF    STMT-IN-REC-TYP  =  '3'
               MOVE  STMT-OUT-REC    TO TAIL-IN-REC
               MOVE  SOUT-SEQ-KEY    TO TAIL-ALL-SEQ-NO
               MOVE  TAIL-IN-REC     TO STMT-OUT-REC
           END-IF.

       5500-EXIT.    EXIT.
      *-------------------------------------------------------*
       5750-GEN-DIFF-DETL-DATA-OUTPUT.

           MOVE  STMT-IN-REC     TO STMT-OUT-REC
           ADD   1               TO WK-NEW-SEQ-NO
           MOVE  WK-NEW-SEQ-NO   TO SOUT-SEQ-KEY
                                    SOUT-IN-SEQ-NO
           COMPUTE  SOUT-SEQ-NO  =  WK-NEW-SEQ-NO - 1
           COMPUTE  SOUT-AC-SEQ-NO = SOUT-SEQ-NO
           MOVE  WK-ORG-REC-IN-AMT TO SOUT-IN-AMT
                                      SOUT-AMT
                                      OUT-BC-BCHQAMT
           IF WK-ORG-REC-IN-AMT  > 0
              MOVE 'C '      TO   SOUT-IN-DC-CODE
              MOVE 'C'       TO   SOUT-AMT-S
              MOVE  'ERP022:LCC Credit Adjust Txn ' TO OUT-BC-BCHQCOR
           ELSE
              MOVE 'D '      TO   SOUT-IN-DC-CODE
              MOVE 'D'       TO   SOUT-AMT-S
              MOVE  'ERP021:Lcc Debit Adjust Txn  '  TO OUT-BC-BCHQCOR
           END-IF
           IF SOUT-IN-DC-CODE  = 'C '
              COMPUTE WK-STMT-IN-AMT  = SOUT-IN-AMT * 1
           ELSE
              COMPUTE WK-STMT-IN-AMT  = SOUT-IN-AMT * -1
           END-IF
           COMPUTE WK-NEW-STMT-IN-BAL = WK-NEW-STMT-IN-BAL +
                                        WK-STMT-IN-AMT

           COMPUTE WK-ORG-REC-IN-AMT  = WK-ORG-REC-IN-AMT -
                                        WK-STMT-IN-AMT
           MOVE  ZEROES             TO  OUT-BC-BCHQCOM
                                        OUT-BC-BCHQNET
      *                                 OUT-BC-BCHQAMT
           MOVE  '000'              TO  OUT-BC-BRETTIME
           MOVE  '0.0000'           TO  OUT-BC-BABCCOM
           MOVE  '00/00/00'         TO  OUT-BC-BTRDATE
                                        OUT-BC-BCHQDATE
                                        OUT-BC-BCSDATE
                                        OUT-BC-BSNDATE
                                        OUT-BC-BCINDATE
           MOVE  '0000000'          TO  OUT-BC-BCHQNO
           MOVE  '00000000'         TO  OUT-BC-BCHQNO-8
           IF WK-NEW-STMT-IN-BAL > 0
              MOVE 'C'      TO   SOUT-IN-BAL-SIGN
           ELSE
              MOVE 'D'      TO   SOUT-IN-BAL-SIGN
           END-IF
           MOVE  WK-NEW-STMT-IN-BAL TO  SOUT-IN-BAL
                                        SOUT-BAL.

       5750-EXIT.    EXIT.
      *-------------------------------------------------------*
       6000-CHK-DETL-DATA-FOR-OUTPUT.

           SET  NOT-FOUND-BC-STMT    TO TRUE
           SET  NOT-FOUND-OTCL-STMT  TO TRUE
           SET  NOT-FOUND-AIR-STMT   TO TRUE

           IF STMT-IN-CHANNEL = 'BC  '
              IF (( BC-BCHQTYPE = 'BC ' ) AND (BC-BSTATUS = 'P')) OR
                 (( BC-BCHQTYPE = 'CSH' ) AND (BC-BSTATUS = 'P')) OR
                 (( BC-BCHQTYPE = 'ABC' ) AND (BC-BSTATUS = 'O')) OR
                 (( BC-BCHQTYPE = 'ABC' ) AND (BC-BSTATUS = 'P')) OR
                 (( BC-BCHQTYPE = 'ABC' ) AND (BC-BSTATUS = 'R'))
                 IF BC-REF-KEY = STMT-IN-DESC40(1:16)
                    SET  FOUND-BC-STMT  TO TRUE
                 END-IF
              END-IF
           END-IF.

           IF STMT-IN-CHANNEL = 'OTCL'
              IF ( BC-BSTATUS = 'O' OR 'P' )
                 IF BC-REF-KEY = STMT-IN-DESC40(1:16)
                    SET  FOUND-OTCL-STMT  TO TRUE
                 END-IF
              END-IF
           END-IF.

           IF STMT-IN-CHANNEL = 'AIR '
              IF ( BC-BSTATUS = 'R' )
                IF BC-BCHQAMT = STMT-AMT
                   SET  FOUND-AIR-STMT  TO TRUE
                END-IF
              END-IF
           END-IF.

      *>> CB60060003
           IF STMT-IN-CHANNEL = 'ATS ' AND
      **      STMT-IN-DESC40(1:8) = 'ATS-BCS.'
              STMT-DESC(1:8) = 'ATS-BCS.'
              IF ( BC-BCHQTYPE = 'BC ' ) AND (BC-BSTATUS = 'R')
                IF BC-BCHQCOM = STMT-AMT
                   SET  FOUND-AIR-STMT  TO TRUE
                END-IF
              END-IF
              IF ( BC-BCHQTYPE = 'ABC' ) AND (BC-BSTATUS = 'R')
                IF BC-BCHQAMT = STMT-AMT
                   SET  FOUND-AIR-STMT  TO TRUE
                END-IF
              END-IF
           END-IF.
      *<< CB60060003

       6000-EXIT.    EXIT.

       9999-CLOSE-RTN.
      *** CLOSE FILE
           CLOSE  ACCT-IN-FL.
           IF  ACC-PROF-STAT  NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'        UPON CONSOLE
               DISPLAY 'CLOSE ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE ACCPROF ERROR,CODE  = '   ACC-PROF-STAT.

           CLOSE  STMT-IN-FL.
           IF  STMT-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'        UPON CONSOLE
               DISPLAY 'CLOSE STMTINF ERROR,CODE  = '    STMT-IN-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE STMTINF ERROR,CODE  = '    STMT-IN-STAT.

           CLOSE  DETL-IN-FL.
           IF  DETL-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'        UPON CONSOLE
               DISPLAY 'CLOSE DETLINF ERROR,CODE  = '    DETL-IN-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE DETLINF ERROR,CODE  = '    DETL-IN-STAT.

           CLOSE  STMT-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'         UPON CONSOLE
               DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT.

           CLOSE  PRTF-OUT-FL.
           IF  PRTF-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG04 ***'         UPON CONSOLE
               DISPLAY 'CLOSE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'CLOSE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT.

           STOP   RUN.

       9999-EXIT.    EXIT.

      *-------------------- END PROGRAM -------------------*
/*
//LKED.SYSLIB   DD  DSN=&LIBPRFX..SCEELKED,DISP=SHR
//              DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD,DISP=SHR
//LKED.SYSLMOD  DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD(STMMCG04),DISP=SHR
//LKED.SYSPRINT DD  SYSOUT=X
/*
/*