//EBCMMC07 JOB EBCMMC07,'EBCMMC07',MSGCLASS=I,CLASS=7,                  00010010
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)                         00020000
//*--------------------------------------------------------------------*00023011
//*--  MERGE STMT. & LCC DETAIL                                       -*00031009
//*    RUN AFTER JOB : EBCMBC01                                       -*00031109
//*    WAIT FOR FILE                                                  -*00031209
//*             PSPBCM.BC01.BCM.BCBCMFIO.&DAYDATE.V1.TM2              -*00031309
//*           ( BACKUP FILE : PSPBCM.BC.BCM.P140.BCBCMFIO.DAILY )     -*00031409
//*--------------------------------------------------------------------*00032009
//         EXEC BCMDATE1                                                00034015
//*--------------------------------------------------------------------*00035009
//STEP01  EXEC PGM=IDCAMS,REGION=8M                                     00040004
//SYSPRINT  DD SYSOUT=*                                                 00050002
//SYSIN     DD *                                                        00060002
   DELETE  PVBBCM.BCM.BCM.PS700.GENSTMT.DETL          -                 00070020
           PURGE                                                        00080002
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.PS700.GENSTMT.DETL) -           00090020
           CYL(100 50)                                -                 00100002
           RECSZ(700 700)                             -                 00110002
           VOL(JP2165 JP2166)                         -                 00120002
           INDEXED                                    -                 00130002
           KEYS(16 0))                                -                 00140003
   DATA    (NAME(PVBBCM.BCM.BCM.PS700.GENSTMT.DETL.DATA)) -             00150020
   INDEX   (NAME(PVBBCM.BCM.BCM.PS700.GENSTMT.DETL.INDEX))              00160020
/*                                                                      00170002
//STEP02   EXEC PGM=IDCAMS                                              00180004
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS700.GENSTMT.DETL,DISP=SHR            00190020
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS700.GENSTMT.DETL,DISP=SHR            00200020
//SYSOUT   DD SYSOUT=X                                                  00210002
//SYSPRINT DD SYSOUT=X                                                  00220002
//SYSIN    DD *                                                         00230002
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 00240002
/*                                                                      00250002
//STEP03   EXEC PGM=IEFBR14                                             01496304
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.BCDATINF.MCASH,                   01496420
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01496500
/*                                                                      01497000
//STEP04   EXEC PGM=IEFBR14                                             01500004
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.BCDATINF.MCASH,                   01510020
//           DISP=(NEW,CATLG,DELETE),                                   01520000
//           DCB=(RECFM=FB,LRECL=600,BLKSIZE=0,DSORG=PS),               01530001
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01540000
/*                                                                      01550000
//STEP05  EXEC PGM=SORT                                                 01551904
//SORTIN   DD DSN=PSPBCM.BC01.BCM.BCBCMFIO.&DAYDATE.V1.TM2,DISP=SHR     01552320
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.P140.BCDATINF.MCASH,DISP=SHR           01552420
//SYSOUT   DD SYSOUT=X                                                  01552511
//SYSIN    DD *                                                         01552611
    SORT    FIELDS=(1,29,A),                                            01552711
            FORMAT=CH,EQUALS                                            01552811
    OUTREC  FIELDS=(1,600)                                              01552911
    RECORD  TYPE=F,LENGTH=600                                           01553011
    END                                                                 01553111
/*                                                                      01553211
//STEP06  EXEC PGM=IDCAMS,REGION=8M                                     01553311
//SYSPRINT  DD SYSOUT=*                                                 01553411
//SYSIN     DD *                                                        01553511
   DELETE  PVBBCM.BCM.BCM.P140.BCDATINF.MCASH         -                 01553620
           PURGE                                                        01553711
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.P140.BCDATINF.MCASH) -          01553820
           CYL(100 50)                                -                 01553911
           RECSZ(600 600)                             -                 01554011
           VOL(JP2165 JP2166)                         -                 01554111
           INDEXED                                    -                 01554211
           KEYS(29 0))                                -                 01554311
   DATA    (NAME(PVBBCM.BCM.BCM.P140.BCDATINF.MCASH.DATA)) -            01554420
   INDEX   (NAME(PVBBCM.BCM.BCM.P140.BCDATINF.MCASH.INDEX))             01554520
/*                                                                      01554611
//STEP07   EXEC PGM=IDCAMS                                              01554711
//INPUT    DD DSN=PSPBCM.BCM.BCM.P140.BCDATINF.MCASH,DISP=SHR           01554820
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.P140.BCDATINF.MCASH,DISP=SHR           01554920
//SYSOUT   DD SYSOUT=X                                                  01555011
//SYSPRINT DD SYSOUT=X                                                  01555111
//SYSIN    DD *                                                         01555211
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 01555311
/*                                                                      01555411
//STEP08   EXEC PGM=IEFBR14                                             01555704
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL,                   01555820
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01555900
/*                                                                      01556000
//STEP09   EXEC PGM=IEFBR14                                             01556104
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL,                   01556220
//           DISP=(NEW,CATLG,DELETE),                                   01557000
//           DCB=(RECFM=FB,LRECL=1300,BLKSIZE=0,DSORG=PS),              01558001
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01559000
/*                                                                      01559100
//STEP10   EXEC PGM=IEFBR14                                             01560004
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.LCC,              01570020
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01580000
/*                                                                      01590000
//STEP11   EXEC PGM=IEFBR14                                             01600004
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.LCC,              01610020
//           DISP=(NEW,CATLG,DELETE),                                   01620000
//           DCB=(RECFM=FB,LRECL=132,BLKSIZE=0,DSORG=PS),               01630000
//           SPACE=(CYL,(1,5),RLSE),UNIT=3390                           01640000
/*                                                                      01650000
//STEP12   EXEC PGM=STMMCG04                                            01840004
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  01850020
//SYSCOUNT DD SYSOUT=X                                                  01860000
//SYSOUT   DD SYSOUT=X                                                  01870000
//SYSPRINT DD SYSOUT=X                                                  01880000
//*==================================================                   02030000
//ACCPROF  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       02031020
//STMTINF  DD DSN=PVBBCM.BCM.BCM.PS700.GENSTMT.DETL,DISP=SHR            02040020
//DETLINF  DD DSN=PVBBCM.BCM.BCM.P140.BCDATINF.MCASH,DISP=SHR           02050020
//STMTOUTF DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL,DISP=SHR           02070020
//PRTFOUTF DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.LCC,DISP=SHR      02080020
/*                                                                      02100000
//STEPBK   EXEC PGM=IDCAMS                                              02100119
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL,DISP=SHR           02100220
//OUTPUT   DD DSN=PSPBCM.BCM.BCM.PS1300.GSTMT.&DAYDATE.V1.TM2,          02100320
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(CYL,(50,100),RLSE),        02100416
//         DCB=(RECFM=FB,LRECL=1300,BLKSIZE=0)                          02100518
//SYSOUT   DD SYSOUT=X                                                  02100616
//SYSPRINT DD SYSOUT=X                                                  02100716
//SYSIN    DD *                                                         02100816
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 02100916
/*                                                                      02101016
//                                                                      02101100
//                                                                      02102000
//                                                                      02103000
//                                                                      02104000
//                                                                      02105000
//                                                                      02106000