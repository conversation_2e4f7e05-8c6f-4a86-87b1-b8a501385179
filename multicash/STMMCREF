//STMM<PERSON><PERSON> JOB STMMCREF,'STMM<PERSON><PERSON>',MSGCLASS=X,CLASS=A,
//     NOTIFY=&SYSUID
//STEP01   EXEC IGYWCL
//SYSPRINT DD   SYSOUT=X
//SYSIN    DD   *
      *------------------------*
       IDENTIFICATION DIVISION.
      *------------------------*
       PROGRAM-ID.   STMMCREF.
      *----------------------------------------------------------------*
      * PROGRAM NAME: STMMCREF
      * AUTHOR      : ITTICHOTE CH.
      * STARTED DATE: 31/01/2017
      *----------------------------------------------------------------*
      * DESCRIPTION : THIS PROGRAM WILL READ STMT FILE AND ACCT PROFILE
      *               AND DETAIL STMT FILE FOR F<PERSON>LERING ONLY ACCOUNT
      *               THEN WRITE THE OUTPUT FILE INCLUDE DETAIL.
      *               GENERATE BILL PAYMENT DETAIL.
      *               GENERATE BILL PAYMENT DETAIL.
      * CB64010022  : 01/08/21 - GEN STATEMENT FOR PRODUCT CODE "EWT"
      *----------------------------------------------------------------*
       ENVIRONMENT DIVISION.
      *---------------------*
       CONFIGURATION SECTION.
       SOURCE-COMPUTER. IBM-4341.
       OBJECT-COMPUTER. IBM-4341.
       SPECIAL-NAMES.   C01 IS  NEXT-PAGE.
      *----------------------*
       INPUT-OUTPUT SECTION.
      *----------------------*
       FILE-CONTROL.

      *** READ ACCOUNT PROFILE (PS INPUT)
            SELECT STMT-IN-FL  ASSIGN   TO  STMTINF
                   FILE STATUS          IS  STMT-IN-STAT.

      *** READ IM NAME LOOK UP FILE (VSAM INPUT)
            SELECT IMNLKUP-IN-FL ASSIGN  TO  IMNLKUP
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  IM-LKUP-KEY
                   FILE STATUS          IS  IMNLKUP-IN-STAT.

      *** READ ST NAME LOOK UP FILE (VSAM INPUT)
            SELECT STNLKUP-IN-FL ASSIGN  TO  STNLKUP
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  ST-LKUP-KEY
                   FILE STATUS          IS  STNLKUP-IN-STAT.

      *** WRITE STMT FILE (PS OUTPUT)
            SELECT STMT-OUT-FL ASSIGN   TO  STMTOUTF
                   FILE STATUS          IS  STMT-OUT-STAT.

      *** WRITE PRTF FILE (PS OUTPUT)
            SELECT PRTF-OUT-FL ASSIGN   TO  PRTFOUTF
                   FILE STATUS          IS  PRTF-OUT-STAT.

      *--------------*
       DATA DIVISION.
      *--------------*
       FILE SECTION.

       FD STMT-IN-FL
           LABEL  RECORDS    ARE STANDARD
      *    RECORD CONTAINS   2400 CHARACTERS
           RECORD CONTAINS   3200 CHARACTERS
           DATA   RECORD     IS  STMT-IN-REC.
       01 STMT-IN-REC.
      *   05 FILLER             PIC  X(2400).
          05 I-INTEBANK         PIC  X(175).
      *   05 I-PAYMENT          PIC  X(870).
          05 I-PAYMENT.
             07 FILLER          PIC  X(341).
             07 I-PMT-TRN-DESC  PIC  X(100).
             07 FILLER          PIC  X(415).
             07 I-PMT-EWT-FLG   PIC  X(3).
             07 FILLER          PIC  X(11).
          05 FILLER             PIC  X(155).
          05 I-BPAY.
             07  I-STMT-BANK-CD      PIC X(02).
             07  FILLER              PIC X(01).
             07  I-STMT-CURR-CD      PIC X(03).
             07  FILLER              PIC X(01).
             07  I-STMT-ACCT         PIC 9(11).
             07  FILLER              PIC X(01).
             07  I-STMT-R-DATE       PIC X(10).
             07  FILLER              PIC X(01).
             07  I-STMT-SEQ-NO       PIC 9(09).
             07  FILLER              PIC X(01).
             07  I-STMT-D-TRAN       PIC X(10).
             07  FILLER              PIC X(01).
             07  I-STMT-D-DATE       PIC X(10).
             07  FILLER              PIC X(01).
             07  I-STMT-D-TIME       PIC X(05).
             07  FILLER              PIC X(01).
             07  I-STMT-T-CODE       PIC X(03).
             07  FILLER              PIC X(01).
             07  I-STMT-CHANNEL      PIC X(04).
             07  FILLER              PIC X(01).
             07  I-STMT-TR           PIC X(02).
             07  FILLER              PIC X(01).
             07  I-STMT-BR           PIC X(04).
             07  FILLER              PIC X(01).
             07  I-STMT-TERMINAL     PIC X(05).
             07  FILLER              PIC X(01).
             07  I-STMT-CHQNO        PIC X(08).
             07  FILLER              PIC X(01).
             07  I-STMT-AMT-S        PIC X(01).
             07  FILLER              PIC X(01).
             07  I-STMT-AMT          PIC X(16).
             07  FILLER              PIC X(01).
             07  I-STMT-BAL          PIC X(17).
             07  FILLER              PIC X(01).
             07  I-STMT-DESC         PIC X(40).
             07  FILLER              PIC X(23).
             07  FILLER              PIC X(01).
             07  I-STMT-BR-OF-AC     PIC X(04).
             07  FILLER              PIC X(01).
             07  I-STMT-ACC-TYP      PIC X(02).
             07  FILLER              PIC X(01).
             07  I-STMT-CUST-NME     PIC X(50).
             07  FILLER              PIC X(01).
             07  I-STMT-REF1         PIC X(20).
             07  FILLER              PIC X(01).
             07  I-STMT-REF2         PIC X(20).
             07  FILLER              PIC X(01).
             07  I-STMT-STATUS-MONEY PIC X(03).
             07  FILLER              PIC X(01).
             07  I-STMT-CH-CODE      PIC X(04).
             07  FILLER              PIC X(01).
             07  I-STMT-AVL-BAL      PIC X(17).
             07  FILLER              PIC X(01).
             07  FILLER              PIC X(21).
             07  FILLER              PIC X(09).
          05 I-EPP              PIC  X(133).
          05 FILLER             PIC  X(17).
          05 I-LCC.
             07  FILLER         PIC  X(423).
             07  I-BCHQCOR      PIC  X(30).
             07  FILLER         PIC  X(66).
          05 FILLER             PIC  X(81).
      *   05 I-RFT              PIC  X(800).
          05 I-RFT.
             07   I-RFT-KEY-REF      PIC X(40).
             07   I-RFT-COMP-ID      PIC X(15).
             07   I-RFT-COMP-NAME    PIC X(35).
             07   I-RFT-FILE-REF     PIC X(32).
             07   I-RFT-BATCH-REF    PIC X(35).
             07   I-RFT-PROD-CODE    PIC X(03).
             07   I-RFT-SUB-PROD     PIC X(08).
             07   I-RFT-TRANS-REF    PIC X(32).
             07   I-RFT-DR-AC        PIC X(25).
             07   I-RFT-VALUE-DATE   PIC X(08).
             07   I-RFT-PAY-AMT      PIC X(15).
             07   FILLER REDEFINES I-RFT-PAY-AMT.
                10 N-RFT-PAY-AMT     PIC 9(13)V99.
             07   I-RFT-BENE-AC      PIC X(25).
             07   I-RFT-BENE-NME     PIC X(100).
             07   I-RFT-BENE-BNK-CD  PIC X(03).
             07   I-RFT-BENE-BNK-NME PIC X(35).
             07   I-RFT-BENE-BR-CD   PIC X(04).
             07   I-RFT-BENE-BR-NME  PIC X(35).
             07   I-RFT-PP-CYCLE     PIC X(01).
             07   I-RFT-PROXY-TYP    PIC X(03).
             07   I-RFT-TAX-ID       PIC X(13).
             07   I-RFT-MOBILE-NO    PIC X(10).
             07   I-RFT-CUST-REF-NO  PIC X(20).
             07   I-RFT-TRANS-REMARK PIC X(32).
             07   I-RFT-PROC-STAT    PIC X(01).
             07   I-RFT-PROC-REMARK  PIC X(100).
             07   I-RFT-NET-PAY-AMT  PIC X(15).
             07   FILLER REDEFINES I-RFT-NET-PAY-AMT.
                10 N-RFT-NET-PAY-AMT     PIC 9(13)V99.
             07   I-RFT-WHT-SEQ-NO   PIC X(14).
             07   I-RFT-PAYEE-TAX-ID.
                10   I-WHT-FLG       PIC X(03).
                10   FILLER          PIC X(07).
             07   I-RFT-BENE-TAX-ID  PIC X(15).
             07   I-RFT-REMARK       PIC X(50).
             07   I-RFT-PP-ON-OS     PIC X(01).
             07   I-RFT-BENE-CHG     PIC X(02).
             07   I-RFT-FE-DR-AC     PIC X(25).
             07   I-RFT-DR-CCY       PIC X(03).
             07   I-RFT-DR-AMT       PIC X(16).
             07   I-RFT-NO-OF-CR     PIC X(06).
             07   I-RFT-CR-CCY       PIC X(03).
             07   I-RFT-CR-SEQ-NO    PIC X(06).
             07   I-RFT-FILLER       PIC X(04).
      *   05 FILLER             PIC  X(172).
          05 FILLER             PIC  X(91).

       FD IMNLKUP-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   100 CHARACTERS
           DATA   RECORD     IS IMWS-LOOKUP-RECORD.
       01 IMWS-LOOKUP-RECORD.
          03 IM-LKUP-KEY.
             05 IM-LKUP-CONTROL.
                07 IM-LKUP-CTL1            PIC XX.
                07 IM-LKUP-CTL2            PIC XXX.
                07 IM-LKUP-CTL3            PIC XXX.
             05 IM-LKUP-LANGUAGE            PIC XX.
             05 IM-LKUP-FIELD               PIC XX.
                88 IM-LKUP-CNTL1           VALUE '01'.
                88 IM-LKUP-CNTL2           VALUE '02'.
                88 IM-LKUP-BR-TH           VALUE '03'.
                88 IM-LKUP-REG             VALUE '04'.
                88 IM-LKUP-BR-ENG          VALUE '05'.
                88 IM-LKUP-OFF             VALUE '06'.
                88 IM-LKUP-TYPE            VALUE '07'.
                88 IM-LKUP-USER1           VALUE '08'.
                88 IM-LKUP-USER2           VALUE '09'.
                88 IM-LKUP-BR-LMT          VALUE '10'.
                88 IM-LKUP-SYS2            VALUE '11'.
                88 IM-LKUP-SYS3            VALUE '12'.
                88 IM-LKUP-INVESTMENT      VALUE '13'.
                88 IM-LKUP-RATE-REGION     VALUE '14'.
                88 IM-LKUP-SVC-CHRG-REGION VALUE '15'.
                88 IM-LKUP-BANK-NAME       VALUE '16'.
                88 IM-LKUP-BANK-ADDRESS    VALUE '17'.
                88 IM-LKUP-BANK-CSZ        VALUE '18'.
                88 IM-LKUP-XINV-FUND       VALUE '19'.
                88 IM-LKUP-GEN-TRAN-DESC   VALUE '20'.
             05 IM-LKUP-VALUE-REGION        PIC X(10).
             05 FILLER REDEFINES IM-LKUP-VALUE-REGION.
                10 IM-LKUP-VALUE           PIC X(5).
                10 FILLER                  PIC X(5).
           03 FILLER                          PIC X(17).
           03 IM-LKUP-LAST-MAINT.
              05 IM-LKUP-ONLINE-RBA          PIC X(4).
              05 IM-LKUP-LM-OPER-ID.
                 07 IM-LKUP-LM-TS-TELLER    PIC X(5).
                 07 FILLER                  PIC X(3).
              05 IM-LKUP-LM-BRANCH           PIC X(3).
              05 IM-LKUP-LM-TERM-ID          PIC X(4).
              05 IM-LKUP-LM-DATE.
                 07 IM-LKUP-LM-DT-CC        PIC XX.
                 07 IM-LKUP-LM-DT.
                    09 IM-LKUP-LM-DT-YY    PIC XX.
                    09 IM-LKUP-LM-DT-MM    PIC XX.
                    09 IM-LKUP-LM-DT-DD    PIC XX.
              05 IM-LKUP-LM-TIME.
                 07 IM-LKUP-LM-HH-MM-SS     PIC S9(7)   COMP-3.
           03 IM-LKUP-NAME                    PIC X(30).
           03 FILLER REDEFINES IM-LKUP-NAME.
              05 IM-LKUP-SCB-BR-LIMIT-AMT    PIC 9(13).
              05 IM-LKUP-SCB-BR-LIMIT-PCT    PIC V9(03).
              05 IM-LKUP-SCB-BR-LIMIT-MAX    PIC 9(13).
              05 FILLER                      PIC X.

       FD STNLKUP-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   122 CHARACTERS
           DATA   RECORD     IS STWS-LOOKUP-RECORD.
       01 STWS-LOOKUP-RECORD.
          03 ST-LKUP-KEY.
             05 STWS-LU-CONTROL.
                07  STWS-LU-CTL1                 PIC XX.
                07  STWS-LU-CTL2                 PIC XXX.
                07  STWS-LU-CTL3                 PIC XXX.
             05 STWS-LU-REC-ID                   PIC X.
             05 STWS-LU-LANGUAGE                 PIC XX.
             05 STWS-LU-FIELD                    PIC XX.
             05 STWS-LU-FIELD-9 REDEFINES STWS-LU-FIELD
                                                 PIC 99.
             05 STWS-LOOKUP.
                07  STWS-LU-VALUE5               PIC X(5).
                07  FILLER                       PIC X(5).
          03 STWS-LU-LAST-MAINT.
             05 STWS-LU-LOG-RBA              PIC X(4).
             05 STWS-LU-SOURCE-INFO.
                07 STWS-LU-SI-OPER-ID.
                   09 STWS-LU-SI-TS-TELLER PIC X(5).
                   09 FILLER               PIC XXX.
                07 STWS-LU-SI-BRANCH        PIC X(5).
                07 STWS-LU-SI-TERM-ID       PIC X(4).
                07 STWS-LU-SI-DATE.
                   09 STWS-LU-SI-DT-CC     PIC XX.
                   09 STWS-LU-SI-DT.
                      11 STWS-LU-SI-DT-YY PIC XX.
                      11 STWS-LU-SI-DT-MM PIC XX.
                      11 STWS-LU-SI-DT-DD PIC XX.
                07 STWS-LU-SI-TIME.
                   09 STWS-LU-SI-HH-MM-SS  PIC S9(7) COMP-3.
          03 FILLER                           PIC X(6).
          03 STWS-LU-DESCRIPTION.
             05 STWS-LU-DESC-30              PIC X(30).
             05 FILLER                       PIC X(30).
          03 STWS-LU-RA  REDEFINES STWS-LU-DESCRIPTION.
             05 STWS-LU-INVEST-ID            PIC X.
             05 STWS-LU-INVEST-NAME          PIC X(8).
             05 STWS-LU-DESC-20              PIC X(20).
             05 STWS-LU-VALUE-DATE           PIC X(6).
             05 STWS-LU-UNIT-PRICE           PIC S9(8)V9(5) COMP-3.
             05 FILLER                       PIC X(18).
          03 STWS-LU-EFFECT-NOT-CLEAR REDEFINES
                       STWS-LU-DESCRIPTION.
             05 STWS-LU-ENC-RATE             PIC 9(4).
             05 STWS-LU-ENC-MINIMUM          PIC 9(13).
             05 STWS-LU-ENC-MAXIMUM          PIC 9(13).
             05 STWS-LU-ENC-FILLER           PIC X(30).

       FD STMT-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   2500 CHARACTERS
           DATA   RECORD     IS STMT-OUT-REC.
       01 STMT-OUT-REC.
      *   05 FILLER             PIC  X(2500).
          05 O-INTEBANK.
             07 SWK-REC-TYPE            PIC  X(01).
             07 FILLER                  PIC  X(05).
             07 SWK-STMT-ACCT-NO        PIC  X(10).
             07 SWK-STMT-BANK-ID        PIC  X(03).
             07 SWK-STMT-BRNO           PIC  X(04).
             07 SWK-STMT-TDATE.
                15 SWK-STMT-TDATE-YYYY  PIC  X(04).
                15 SWK-STMT-TDATE-MM    PIC  X(02).
                15 SWK-STMT-TDATE-DD    PIC  X(02).
             07 SWK-STMT-BAMT           PIC  X(16).
             07 SWK-STMT-BAMT-SIGN      PIC  X(01).
             07 SWK-ACCT-NAME           PIC  X(30).
             07 FILLER                  PIC  X(34).
             07 SWK-STMT-SERIAL-NO      PIC  X(06).
             07 SWK-STMT-CHQNO-10       PIC  X(10).
             07 SWK-HEAD-SUM-DR         PIC  9(14)V99.
             07 SWK-HEAD-SUM-DR-TYP     PIC  X(01).
             07 SWK-HEAD-SUM-CR         PIC  9(14)V99.
             07 SWK-HEAD-SUM-CR-TYP     PIC  X(01).
             07 FILLER                  PIC  X(06).
             07 SWK-STMT-SYMBOL         PIC  X(03).
             07 SWK-STMT-CHAN           PIC  X(04).
          05 O-PAYMENT.
             07 SWK-STMT-TAMT-ERP       PIC  9(13)V99.
             07 SWK-STMT-CRDR-ERP       PIC  X(02).
      *RFT-> 07 FILLER                  PIC  X(853).
             07 O-PAYMENT-EXTEND.
      *RFT->    09 O-PAY-COMP-ID            PIC  X(12).
      *RFT->       09 O-RFT-COMP-ID                   PIC  X(15).
                09 O-RFT-COMP-ID            PIC  X(12).
                09 O-PAY-TRANS-DATE         PIC  X(21).
                09 O-PAY-TRANS-AMT          PIC  X(16).
                09 O-PAY-ACTION-FLAG        PIC  X(01).
      *RFT->    09 O-PAY-CN-REF             PIC  X(24).
      *RFT->       09 O-RFT-BATCH-REF                 PIC  X(35).
                09 O-RFT-BATCH-REF          PIC  X(24).
      *RFT->    09 O-PAY-S1-REF             PIC  X(32).
                09 O-RFT-FILE-REF           PIC  X(32).
      *RFT->    09 O-PAY-CUST-REF           PIC  X(40).
      *RFT->       09 O-RFT-TRANS-REF                 PIC  X(32).
                09 O-RFT-TRANS-REF          PIC  X(40).
      *RFT->    09 O-PAY-PROD-CODE          PIC  X(03).
                09 O-RFT-PROD-CODE          PIC  X(03).
      *RFT->    09 O-PAY-VALUE-DATE         PIC  X(08).
                09 O-RFT-VALUE-DATE         PIC  X(08).
      *RFT->    09 O-PAY-DEBIT-ACCT         PIC  X(25).
                09 O-RFT-DR-AC              PIC  X(25).
                09 O-PAY-DB-ACCT-TYPE       PIC  X(02).
                09 O-PAY-DB-BRC-CODE        PIC  X(04).
      *RFT->    09 O-PAY-DB-CURRENCY        PIC  X(03).
                09 O-RFT-DR-CCY             PIC  X(03).
      *RFT->    09 O-PAY-DB-AMT             PIC  X(16).
                09 O-RFT-DR-AMT             PIC  X(16).
      *RFT->    09 O-PAY-DB-FEE-ACCT        PIC  X(25).
                09 O-RFT-FE-DR-AC           PIC  X(25).
                09 O-PAY-DB-FEE-ACCT-TYPE   PIC  X(02).
                09 O-PAY-DB-FEE-BRC-CODE    PIC  X(04).
      *RFT->    09 O-PAY-NO-CREDIT          PIC  X(06).
                09 O-RFT-NO-OF-CR           PIC  X(06).
      *RFT->    09 O-PAY-CR-SEQ             PIC  X(06).
                09 O-RFT-CR-SEQ-NO          PIC  X(06).
      *RFT->    09 O-PAY-CR-ACCT            PIC  X(25).
                09 O-RFT-BENE-AC            PIC  X(25).
      *RFT->    09 O-PAY-CR-AMT             PIC  X(16).
      *RFT->    09 O-RFT-PAY-AMT                      PIC  X(15).
      *         09 O-RFT-PAY-AMT            PIC  X(16).
                09 O-RFT-PAY-AMT            PIC  *************.99.
      *RFT->    09 O-PAY-CR-NET-AMT         PIC  X(16).
      *RFT->    09 O-RFT-NET-PAY-AMT                  PIC X(15).
      *         09 O-RFT-NET-PAY-AMT        PIC  X(16).
                09 O-RFT-NET-PAY-AMT        PIC  *************.99.
                09 O-PAY-CR-BENE-FEE        PIC  X(16).
      *RFT->    09 O-PAY-TRANS-STATUS       PIC  X(01).
                09 O-RFT-PROC-STAT          PIC  X(01).
      *RFT->    09 O-PAY-TRANS-DES          PIC  X(100).
                09 O-RFT-PROC-REMARK        PIC  X(100).
      *RFT->    09 O-PAY-CR-CURRENCY        PIC  X(03).
                09 O-RFT-CR-CCY             PIC  X(03).
      *RFT->    09 O-PAY-RECEIVE-BANK-CD    PIC  X(03).
                09 O-RFT-BENE-BNK-CD        PIC  X(03).
      *RFT->    09 O-PAY-RECEIVE-BANK-NAME  PIC  X(35).
                09 O-RFT-BENE-BNK-NME       PIC  X(35).
      *RFT->    09 O-PAY-RECEIVE-BRC-CD     PIC  X(04).
                09 O-RFT-BENE-BR-CD         PIC  X(04).
      *RFT->    09 O-PAY-RECEIVE-BRC-NAME   PIC  X(35).
                09 O-RFT-BENE-BR-NME        PIC  X(35).
                09 O-PAY-WHT-PRESENT        PIC  X(01).
                09 O-PAY-INV-DET-PRESENT    PIC  X(01).
                09 O-PAY-CR-ADV-REQUIRE     PIC  X(01).
                09 O-PAY-DELIVERY-MODE      PIC  X(01).
                09 O-PAY-PICKUP-LOCATION    PIC  X(04).
                09 O-PAY-WHT-FORM-TYPE      PIC  X(02).
                09 O-PAY-WHT-TAX-NO         PIC  X(14).
                09 O-PAY-WHT-ATT-NO         PIC  X(06).
                09 O-PAY-NO-WHT-DET         PIC  X(02).
                09 O-PAY-TOT-WHT-AMT        PIC  X(16).
                09 O-PAY-NO-INV-DET         PIC  X(06).
                09 O-PAY-TOT-INV-AMT        PIC  X(16).
                09 O-PAY-WHT-PAY-TYPE       PIC  X(01).
                09 O-PAY-WHT-REMARK         PIC  X(40).
                09 O-PAY-WHT-DEDUCT-CODE    PIC  X(08).
                09 O-PAY-WHT-SIGNATORY      PIC  X(01).
                09 O-PAY-BENE-NOTIFI        PIC  X(01).
      *RFT      09 O-PAY-CUST-REF-NO        PIC  X(20).
                09 O-RFT-CUST-REF-NO        PIC  X(20).
                09 O-PAY-CHQ-REF-DOC-TYPE   PIC  X(01).
                09 O-PAY-PAY-TYPE-CODE      PIC  X(03).
                09 O-PAY-SERVICE-TYPE       PIC  X(02).
      *RFT      09 O-PAY-PAYEE-NAME-TH      PIC  X(100).
                09 O-RFT-BENE-NME           PIC  X(100).
                09 O-PAY-PAYEE-NAME-EN      PIC  X(70).
      *RFT->    09 O-PAY-BENE-TAX-ID        PIC  X(10).
                09 O-RFT-PAYEE-TAX-ID       PIC  X(10).
                09 O-PAY-CHQ-NO             PIC  X(08).
      *RFT->    09 O-PAY-WHT-SERIAL-NO      PIC  X(14).
                09 O-RFT-WHT-SEQ-NO         PIC  X(14).
      *RFT->
      *****  07  O-RFT-COMP-NAME    PIC X(35).
      *****  07  O-RFT-REMARK       PIC X(50).
      *RFT->
      **  05 FILLER             PIC  X(155).
          05 O-RFT-EXTEND.
             07  O-RFT-BENE-TAX-ID  PIC X(15).
             07  O-RFT-PP-CYCLE     PIC X(01).
             07  O-RFT-PROXY-TYP    PIC X(03).
             07  O-RFT-TAX-ID       PIC X(13).
             07  O-RFT-MOBILE-NO    PIC X(10).
             07  O-RFT-TRANS-REMARK PIC X(32).
             07  O-RFT-PP-ON-OS     PIC X(01).
             07  O-RFT-BENE-CHG     PIC X(02).
             07  O-RFT-SUB-PROD     PIC X(08).
             07  O-RFT-KEY-REF      PIC X(40).
             07  FILLER             PIC X(30).
          05 O-LCC              PIC  X(519).
          05 FILLER             PIC  X(100).
      *   05 O-BPAY             PIC  X(263).
          05 O-BPAY.
             07  O-STMT-BANK-CD      PIC X(02).
             07  O-STMT-CURR-CD      PIC X(03).
             07  O-STMT-ACCT         PIC 9(11).
             07  O-STMT-R-DATE       PIC X(10).
             07  O-STMT-SEQ-NO       PIC 9(09).
             07  O-STMT-D-TRAN       PIC X(10).
             07  O-STMT-D-DATE       PIC X(10).
             07  O-STMT-D-TIME       PIC X(05).
             07  O-STMT-T-CODE       PIC X(03).
             07  O-STMT-CHANNEL      PIC X(04).
             07  O-STMT-TR           PIC X(02).
             07  O-STMT-BR           PIC X(04).
             07  O-STMT-TERMINAL     PIC X(05).
             07  O-STMT-CHQNO        PIC X(08).
             07  O-STMT-AMT-S        PIC X(01).
             07  O-STMT-AMT          PIC X(16).
             07  O-STMT-BAL          PIC X(17).
             07  O-STMT-DESC         PIC X(40).
             07  O-STMT-BR-OF-AC     PIC X(04).
             07  O-STMT-ACC-TYP      PIC X(02).
             07  O-STMT-CUST-NME     PIC X(50).
             07  O-STMT-REF1         PIC X(20).
             07  O-STMT-REF2         PIC X(20).
             07  O-STMT-STATUS-MONEY PIC X(03).
             07  O-STMT-CH-CODE      PIC X(04).
          05 O-STMT-BR-ENG-NAME      PIC  X(30).
          05 FILLER             PIC  X(112).
          05 O-EPP              PIC  X(133).
          05 O-ADJUST-STATUS    PIC  X(01).
          05 O-ADJUST-DESC      PIC  X(42).
          05 FILLER             PIC  X(100).


       FD PRTF-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   132 CHARACTERS
           DATA   RECORD     IS PRTF-OUT-REC.
       01 PRTF-OUT-REC.
          05 FILLER             PIC X(132).

      *------------------------*
       WORKING-STORAGE SECTION.
      *------------------------*
       77 SW-OPENINFL                  PIC X(01)   VALUE 'Y'.
          88 ERR-OPENINFL                          VALUE 'N'.
          88 SUCCESS-OPENINFL                      VALUE 'Y'.

       77 SW-STMTINF                   PIC X(01)   VALUE 'N'.
          88 EOF-STMTINF                           VALUE 'Y'.
          88 NOT-EOF-STMTINF                       VALUE 'N'.
       77 SW-IMNLKUP-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-IMNLKUP                         VALUE 'Y'.
          88 NOT-FOUND-IMNLKUP                     VALUE 'N'.
       77 SW-STNLKUP-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-STNLKUP                         VALUE 'Y'.
          88 NOT-FOUND-STNLKUP                     VALUE 'N'.

       01  WK-FILE-STATUS.
           03  STMT-IN-STAT        PIC  X(02).
           03  IMNLKUP-IN-STAT     PIC  X(02).
           03  STNLKUP-IN-STAT     PIC  X(02).
           03  STMT-OUT-STAT       PIC  X(02).
           03  PRTF-OUT-STAT       PIC  X(02).
       01  WK-DATE-X               PIC  9(08).
       01  WK-DATE-Y.
           03  WK-Y4               PIC  9(04).
           03  WK-MM               PIC  9(02).
           03  WK-DD               PIC  9(02).

       01  WK-INT-DATE-X           PIC  9(08).
       01  WK-INT-DATE-Y.
           03  FILLER              PIC  9(02) VALUE ZEROS.
           03  WK-INT-D6           PIC  9(06).

       01 HEAD-OUT-REC.
          05 PRT-STMT-ACCT-NO           PIC  X(10).
          05 FILLER                     PIC  X(01) VALUE SPACES.
          05 PRT-STMT-ACCT-NAME         PIC  X(30).
          05 FILLER                     PIC  X(01) VALUE SPACES.
          05 PRT-STMT-SUM-DESC          PIC  X(16).
          05 FILLER                     PIC  X(01) VALUE SPACES.
          05 PRT-HEAD-SUM-DR            PIC  9(14)V99.
          05 PRT-HEAD-SUM-DR-TYP        PIC  X(01).
          05 PRT-HEAD-SUM-CR            PIC  9(14)V99.
          05 PRT-HEAD-SUM-CR-TYP        PIC  X(01).
          05 FILLER                     PIC  X(45) VALUE SPACES.

       02 DIFF-OUT-REC.
          05 FILLER                     PIC  X(11) VALUE SPACES.
          05 PRT-DETL-DESC              PIC  X(30).
          05 FILLER                     PIC  X(01) VALUE SPACES.
          05 PRT-DETL-SUM-DESC          PIC  X(16).
          05 FILLER                     PIC  X(01) VALUE SPACES.
          05 PRT-DETL-SUM-DR            PIC  9(14)V99.
          05 PRT-DETL-SUM-DR-TYP        PIC  X(01).
          05 PRT-DETL-SUM-CR            PIC  9(14)V99.
          05 PRT-DETL-SUM-CR-TYP        PIC  X(01).
          05 FILLER                     PIC  X(45) VALUE SPACES.

       01  WK-ORG-REC-IN-AMT       PIC  S9(13)V99 VALUE ZEROS.
       01  WK-NEW-SEQ-NO           PIC  9(06) VALUE ZEROS.
       01  WK-NEW-STMT-IN-BAL      PIC  S9(14)V99 VALUE ZEROS.
       01  WK-STMT-IN-AMT          PIC  S9(13)V99 VALUE ZEROS.
       01  WK-HEAD-SUM-AMT-DR      PIC  S9(14)V99 VALUE ZEROS.
       01  WK-HEAD-SUM-AMT-CR      PIC  S9(14)V99 VALUE ZEROS.
       01  WK-DETL-SUM-AMT-DR      PIC  S9(14)V99 VALUE ZEROS.
       01  WK-DETL-SUM-AMT-CR      PIC  S9(14)V99 VALUE ZEROS.
       01  CNV-STMT-IN-AMT         PIC  -*************9.99.

      *---------------------------------------------------------*
       PROCEDURE DIVISION.
      *-------------------*
       0000-MAIN-PROCESS.

           PERFORM  1000-INITIAL-RTN   THRU  1000-EXIT.
           PERFORM  2000-MAIN-PROCESS  THRU  2000-EXIT.
           PERFORM  9999-CLOSE-RTN     THRU  9999-EXIT.

       0000-EXIT.    EXIT.

      *-------------------------------------------------------*
       1000-INITIAL-RTN.

           SET SUCCESS-OPENINFL TO TRUE.

           OPEN INPUT  STMT-IN-FL.
           IF  STMT-IN-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCREF ***'       UPON CONSOLE
              DISPLAY 'OPEN STMTINF ERROR,CODE  = '    STMT-IN-STAT
                                                        UPON CONSOLE
              DISPLAY 'OPEN STMTINF ERROR,CODE  = '    STMT-IN-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN INPUT IMNLKUP-IN-FL.
           IF IMNLKUP-IN-STAT NOT = '00' AND
              IMNLKUP-IN-STAT NOT = '35'
              DISPLAY '*** PROGRAM STMMCREF ***'  UPON CONSOLE
              DISPLAY 'OPEN IMNLKUP IN ERROR,CODE = ' IMNLKUP-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'OPEN IMNLKUP IN ERROR,CODE = ' IMNLKUP-IN-STAT
              SET ERR-OPENINFL TO TRUE
           END-IF.

           OPEN INPUT STNLKUP-IN-FL.
           IF STNLKUP-IN-STAT NOT = '00' AND
              STNLKUP-IN-STAT NOT = '35'
              DISPLAY '*** PROGRAM STMMCREF ***'  UPON CONSOLE
              DISPLAY 'OPEN STNLKUP IN ERROR,CODE = ' STNLKUP-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'OPEN STNLKUP IN ERROR,CODE = ' STNLKUP-IN-STAT
              SET ERR-OPENINFL TO TRUE
           END-IF.

           OPEN OUTPUT STMT-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCREF ***'        UPON CONSOLE
              DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
                                                         UPON CONSOLE
              DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN OUTPUT PRTF-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCREF ***'        UPON CONSOLE
              DISPLAY 'OPEN PRTFOUTF ERROR,CODE  = ' PRTF-OUT-STAT
                                                         UPON CONSOLE
              DISPLAY 'OPEN PRTFOUTF ERROR,CODE  = ' PRTF-OUT-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

       1000-EXIT.    EXIT.
      *-------------------------------------------------------*
       2000-MAIN-PROCESS.
           PERFORM 2100-READ-STMTINF-RTN  THRU 2100-EXIT.
           PERFORM UNTIL EOF-STMTINF
              PERFORM 3000-PROCESS-GEN-STMT-RTN  THRU 3000-EXIT
              PERFORM 2100-READ-STMTINF-RTN  THRU 2100-EXIT
           END-PERFORM.
            GO TO  2000-EXIT.
       2000-EXIT.    EXIT.
      *-------------------------------------------------------*
       2100-READ-STMTINF-RTN.

           READ STMT-IN-FL AT END
                SET EOF-STMTINF TO TRUE.

       2100-EXIT.    EXIT.
      *-------------------------------------------------------*
       2200-READ-IMNLKUP-RTN.
      *    DISPLAY 'IM-LKUP-KEY : ' IM-LKUP-KEY
           READ IMNLKUP-IN-FL
           END-READ.
           IF IMNLKUP-IN-STAT = '00'
      *       DISPLAY 'IM-LKUP-NAME : ' IM-LKUP-NAME
              MOVE IM-LKUP-NAME     TO O-STMT-BR-ENG-NAME
              SET FOUND-IMNLKUP     TO TRUE.
           IF IMNLKUP-IN-STAT = '23'
      *       DISPLAY 'IM-LKUP-NAME : ' IM-LKUP-VALUE
              MOVE IM-LKUP-VALUE    TO O-STMT-BR-ENG-NAME
              SET NOT-FOUND-IMNLKUP TO TRUE.

       2200-EXIT. EXIT.
      *-------------------------------------------------------*
       2300-READ-STNLKUP-RTN.
      *    DISPLAY 'ST-LKUP-KEY : ' ST-LKUP-KEY
           READ STNLKUP-IN-FL
           END-READ.
           IF STNLKUP-IN-STAT = '00'
      *       DISPLAY 'STWS-LU-DESC-30 : ' STWS-LU-DESC-30
              MOVE STWS-LU-DESC-30  TO O-STMT-BR-ENG-NAME
              SET FOUND-STNLKUP     TO TRUE.
           IF STNLKUP-IN-STAT = '23'
      *       DISPLAY 'STWS-LU-DESC-30 : OTHER '
              MOVE 'OTHER'          TO O-STMT-BR-ENG-NAME
              SET NOT-FOUND-STNLKUP TO TRUE.

       2300-EXIT. EXIT.
      *-------------------------------------------------------*
       3000-PROCESS-GEN-STMT-RTN.

      *    MOVE   STMT-IN-REC   TO   STMT-OUT-REC.

           MOVE   SPACE         TO   STMT-OUT-REC.
           MOVE   I-INTEBANK    TO   O-INTEBANK.
           MOVE   I-PAYMENT     TO   O-PAYMENT.
           IF I-PMT-EWT-FLG = 'EWT'
             MOVE I-PMT-EWT-FLG TO   O-RFT-PROD-CODE
           END-IF
           MOVE   I-LCC         TO   O-LCC.
           MOVE   I-EPP         TO   O-EPP.

           MOVE  I-STMT-BANK-CD    TO  O-STMT-BANK-CD
           MOVE  I-STMT-CURR-CD    TO  O-STMT-CURR-CD
           MOVE  I-STMT-ACCT       TO  O-STMT-ACCT
           MOVE  I-STMT-R-DATE     TO  O-STMT-R-DATE
           MOVE  I-STMT-SEQ-NO     TO  O-STMT-SEQ-NO
           MOVE  I-STMT-D-TRAN     TO  O-STMT-D-TRAN
           MOVE  I-STMT-D-DATE     TO  O-STMT-D-DATE
           MOVE  I-STMT-D-TIME     TO  O-STMT-D-TIME
           MOVE  I-STMT-T-CODE     TO  O-STMT-T-CODE
           MOVE  I-STMT-CHANNEL    TO  O-STMT-CHANNEL
           MOVE  I-STMT-TR         TO  O-STMT-TR
           MOVE  I-STMT-BR         TO  O-STMT-BR
           MOVE  I-STMT-TERMINAL   TO  O-STMT-TERMINAL
           MOVE  I-STMT-CHQNO      TO  O-STMT-CHQNO
           MOVE  I-STMT-AMT-S      TO  O-STMT-AMT-S
           MOVE  I-STMT-AMT        TO  O-STMT-AMT
           MOVE  I-STMT-BAL        TO  O-STMT-BAL
           MOVE  I-STMT-DESC       TO  O-STMT-DESC
           MOVE  I-STMT-BR-OF-AC   TO  O-STMT-BR-OF-AC
           MOVE  I-STMT-ACC-TYP    TO  O-STMT-ACC-TYP
           MOVE  I-STMT-CUST-NME   TO  O-STMT-CUST-NME
           MOVE  I-STMT-REF1       TO  O-STMT-REF1
           MOVE  I-STMT-REF2       TO  O-STMT-REF2
           MOVE  I-STMT-STATUS-MONEY TO  O-STMT-STATUS-MONEY
           MOVE  I-STMT-CH-CODE      TO  O-STMT-CH-CODE
           MOVE  SPACE             TO  O-STMT-BR-ENG-NAME

           IF  I-STMT-BR  NOT = SPACES
               PERFORM 3500-LOOKUP-BR-NAME-RTN THRU 3500-EXIT
           END-IF.

           IF  I-BCHQCOR(1:5)  = 'ERP02'
               MOVE  'A'                 TO O-ADJUST-STATUS
               MOVE  I-BCHQCOR           TO O-ADJUST-DESC
           END-IF.

      **   DISPLAY 'I-PMT-TRN-DESC : ' I-PMT-TRN-DESC
           IF  I-PMT-TRN-DESC(1:5)  = 'ERP03'
               MOVE  'A'                  TO O-ADJUST-STATUS
               MOVE  I-PMT-TRN-DESC(1:42) TO O-ADJUST-DESC
           END-IF.

           IF  I-EPP(1:5)  = 'ERP02'
               MOVE  'A'                 TO O-ADJUST-STATUS
               MOVE  I-EPP(1:42)         TO O-ADJUST-DESC
           END-IF.
           IF  I-RFT-KEY-REF NOT = SPACES
             MOVE I-RFT-KEY-REF      TO  O-RFT-KEY-REF
             MOVE I-RFT-COMP-ID      TO  O-RFT-COMP-ID
      *RFT-> MOVE I-RFT-COMP-NAME    TO  O-RFT-COMP-NAME
             MOVE I-RFT-FILE-REF     TO  O-RFT-FILE-REF
             MOVE I-RFT-BATCH-REF    TO  O-RFT-BATCH-REF
             MOVE I-RFT-PROD-CODE    TO  O-RFT-PROD-CODE
             MOVE I-RFT-SUB-PROD     TO  O-RFT-SUB-PROD
             MOVE I-RFT-TRANS-REF    TO  O-RFT-TRANS-REF
             MOVE I-RFT-DR-AC        TO  O-RFT-DR-AC
             MOVE I-RFT-VALUE-DATE   TO  O-RFT-VALUE-DATE
      **     DISPLAY 'I-RFT-PAY-AMT => 'I-RFT-PAY-AMT
             MOVE N-RFT-PAY-AMT        TO  O-RFT-PAY-AMT
      **     DISPLAY 'O-RFT-PAY-AMT => 'O-RFT-PAY-AMT
             MOVE I-RFT-BENE-AC      TO  O-RFT-BENE-AC
             MOVE I-RFT-BENE-NME     TO  O-RFT-BENE-NME
             MOVE I-RFT-BENE-BNK-CD  TO  O-RFT-BENE-BNK-CD
             MOVE I-RFT-BENE-BNK-NME TO  O-RFT-BENE-BNK-NME
             MOVE I-RFT-BENE-BR-CD   TO  O-RFT-BENE-BR-CD
             MOVE I-RFT-BENE-BR-NME  TO  O-RFT-BENE-BR-NME
             MOVE I-RFT-PP-CYCLE     TO  O-RFT-PP-CYCLE
             MOVE I-RFT-PROXY-TYP    TO  O-RFT-PROXY-TYP
             MOVE I-RFT-TAX-ID       TO  O-RFT-TAX-ID
             MOVE I-RFT-MOBILE-NO    TO  O-RFT-MOBILE-NO
             MOVE I-RFT-CUST-REF-NO  TO  O-RFT-CUST-REF-NO
             MOVE I-RFT-TRANS-REMARK TO  O-RFT-TRANS-REMARK
             MOVE I-RFT-PROC-STAT    TO  O-RFT-PROC-STAT
             MOVE I-RFT-PROC-REMARK  TO  O-RFT-PROC-REMARK
      **     DISPLAY 'I-RFT-NET-PAY-AMT => 'I-RFT-NET-PAY-AMT
             MOVE N-RFT-NET-PAY-AMT    TO  O-RFT-NET-PAY-AMT
      **     DISPLAY 'O-RFT-NET-PAY-AMT => 'O-RFT-NET-PAY-AMT
             MOVE I-RFT-WHT-SEQ-NO   TO  O-RFT-WHT-SEQ-NO
             MOVE I-RFT-PAYEE-TAX-ID TO  O-RFT-PAYEE-TAX-ID
             MOVE I-RFT-BENE-TAX-ID  TO  O-RFT-BENE-TAX-ID
      *RFT-> MOVE I-RFT-REMARK       TO  O-RFT-REMARK

             MOVE I-RFT-CR-SEQ-NO    TO  O-RFT-CR-SEQ-NO
             MOVE I-RFT-NO-OF-CR     TO  O-RFT-NO-OF-CR

             IF   I-RFT-REMARK(1:5)  = 'ERP03'
                  MOVE  'A'                TO O-ADJUST-STATUS
                                              O-RFT-PROC-STAT
                  MOVE  I-RFT-REMARK(1:42) TO O-ADJUST-DESC
                                              O-RFT-PROC-REMARK
                  MOVE  '000000'           TO  O-RFT-CR-SEQ-NO
                                               O-RFT-NO-OF-CR
             END-IF

             MOVE I-RFT-PP-ON-OS     TO  O-RFT-PP-ON-OS
             MOVE I-RFT-BENE-CHG     TO  O-RFT-BENE-CHG
             MOVE I-RFT-FE-DR-AC     TO  O-RFT-FE-DR-AC
             MOVE I-RFT-DR-CCY       TO  O-RFT-DR-CCY
             MOVE I-RFT-DR-AMT       TO  O-RFT-DR-AMT
             MOVE I-RFT-CR-CCY       TO  O-RFT-CR-CCY
             MOVE '0000000000000.00' TO  O-PAY-CR-BENE-FEE
                                         O-PAY-TOT-WHT-AMT
                                         O-PAY-TOT-INV-AMT
             MOVE '00000000'         TO  O-PAY-CHQ-NO
             MOVE SPACE              TO  O-PAY-DB-ACCT-TYPE
                                         O-PAY-DB-BRC-CODE
                                         O-PAY-DB-FEE-ACCT-TYPE
                                         O-PAY-DB-FEE-BRC-CODE
                                         O-PAY-WHT-PRESENT
                                         O-PAY-INV-DET-PRESENT
                                         O-PAY-CR-ADV-REQUIRE
                                         O-PAY-DELIVERY-MODE
                                         O-PAY-PICKUP-LOCATION
                                         O-PAY-WHT-FORM-TYPE
                                         O-PAY-WHT-TAX-NO
                                         O-PAY-WHT-ATT-NO
                                         O-PAY-NO-WHT-DET
                                         O-PAY-NO-INV-DET
                                         O-PAY-WHT-PAY-TYPE
                                         O-PAY-WHT-REMARK
                                         O-PAY-WHT-DEDUCT-CODE
                                         O-PAY-WHT-SIGNATORY
                                         O-PAY-BENE-NOTIFI
                                         O-PAY-CHQ-REF-DOC-TYPE
                                         O-PAY-PAY-TYPE-CODE
                                         O-PAY-SERVICE-TYPE
                                         O-PAY-PAYEE-NAME-EN
           END-IF.

           IF  SWK-REC-TYPE = '1'
               MOVE  SWK-STMT-ACCT-NO     TO  PRT-STMT-ACCT-NO
               MOVE  SWK-ACCT-NAME        TO  PRT-STMT-ACCT-NAME
               MOVE  SWK-HEAD-SUM-DR      TO  PRT-HEAD-SUM-DR
                                              WK-HEAD-SUM-AMT-DR
               MOVE  SWK-HEAD-SUM-DR-TYP  TO  PRT-HEAD-SUM-DR-TYP
               MOVE  SWK-HEAD-SUM-CR      TO  PRT-HEAD-SUM-CR
                                              WK-HEAD-SUM-AMT-CR
               MOVE  SWK-HEAD-SUM-CR-TYP  TO  PRT-HEAD-SUM-CR-TYP
               MOVE  ZEROES               TO  WK-DETL-SUM-AMT-DR
                                              WK-DETL-SUM-AMT-CR
               MOVE  'SUM STM. BALANCE'   TO  PRT-STMT-SUM-DESC
           END-IF.

           IF  SWK-REC-TYPE = '2'
               IF SWK-STMT-CRDR-ERP = 'D '
                  ADD SWK-STMT-TAMT-ERP  TO  WK-DETL-SUM-AMT-DR
               END-IF
               IF SWK-STMT-CRDR-ERP = 'C '
                  ADD SWK-STMT-TAMT-ERP  TO  WK-DETL-SUM-AMT-CR
               END-IF
           END-IF.

           IF  SWK-REC-TYPE = '3'
               MOVE  WK-DETL-SUM-AMT-DR    TO  PRT-DETL-SUM-DR
               MOVE  'D'                   TO  PRT-DETL-SUM-DR-TYP
               MOVE  WK-DETL-SUM-AMT-CR    TO  PRT-DETL-SUM-CR
               MOVE  'C'                   TO  PRT-DETL-SUM-CR-TYP
               MOVE  '*** DIFF ***'        TO  PRT-DETL-DESC
               MOVE  '    TXN. DETAILS'    TO  PRT-DETL-SUM-DESC
               IF ( WK-DETL-SUM-AMT-DR NOT = WK-HEAD-SUM-AMT-DR)
               OR ( WK-DETL-SUM-AMT-CR NOT = WK-HEAD-SUM-AMT-CR)
                  DISPLAY '** DIFF ** SUM AMT. HEADER/DETAIL : '
                                                  PRT-STMT-ACCT-NO
                  MOVE  HEAD-OUT-REC      TO  PRTF-OUT-REC
                  WRITE PRTF-OUT-REC
                  MOVE  DIFF-OUT-REC      TO  PRTF-OUT-REC
                  WRITE PRTF-OUT-REC
               END-IF
           END-IF.

      **   WRITE STMT-OUT-REC.
           PERFORM  5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT.

       3000-EXIT.    EXIT.
      *-------------------------------------------------------*
       3500-LOOKUP-BR-NAME-RTN.
           IF  O-STMT-ACC-TYP   =    'IM'
               MOVE SPACES      TO   IM-LKUP-KEY
               MOVE '14'        TO   IM-LKUP-CTL1
               MOVE '764'       TO   IM-LKUP-CTL2
               MOVE '000'       TO   IM-LKUP-CTL3
               MOVE '05'        TO   IM-LKUP-FIELD
               MOVE '0'         TO   IM-LKUP-VALUE(1:1)
               MOVE O-STMT-BR   TO   IM-LKUP-VALUE(2:4)
               PERFORM 2200-READ-IMNLKUP-RTN  THRU 2200-EXIT
           ELSE
               MOVE SPACES      TO   ST-LKUP-KEY
               MOVE '14'        TO   STWS-LU-CTL1
               MOVE '764'       TO   STWS-LU-CTL2
               MOVE '000'       TO   STWS-LU-CTL3
               MOVE 'L'         TO   STWS-LU-REC-ID
               MOVE '05'        TO   STWS-LU-FIELD
               MOVE '0'         TO   STWS-LU-VALUE5(1:1)
               MOVE O-STMT-BR   TO   STWS-LU-VALUE5(2:4)
               PERFORM 2300-READ-STNLKUP-RTN  THRU 2300-EXIT
           END-IF.
       3500-EXIT.    EXIT.
      *-------------------------------------------------------*
       5000-WRITE-OUTPUT-STMT-RTN.

           WRITE STMT-OUT-REC.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCREF ***'         UPON CONSOLE
               DISPLAY 'WRITE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'WRITE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
           END-IF.

           IF  PRTF-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCREF ***'         UPON CONSOLE
               DISPLAY 'WRITE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'WRITE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT
           END-IF.

       5000-EXIT.    EXIT.
      *-------------------------------------------------------*

       9999-CLOSE-RTN.
      *** CLOSE FILE
           CLOSE  STMT-IN-FL.
           IF  STMT-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCREF ***'        UPON CONSOLE
               DISPLAY 'CLOSE STMTINF ERROR,CODE  = '    STMT-IN-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE STMTINF ERROR,CODE  = '    STMT-IN-STAT
           END-IF.

           CLOSE IMNLKUP-IN-FL.
           IF IMNLKUP-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCREF ***'  UPON CONSOLE
               DISPLAY 'CLOSE IMNLKUP IN ERROR,CODE = ' IMNLKUP-IN-STAT
                                                   UPON CONSOLE
               DISPLAY 'CLOSE IMNLKUP IN ERROR,CODE = ' IMNLKUP-IN-STAT
           END-IF.

           CLOSE STNLKUP-IN-FL.
           IF STNLKUP-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCREF ***'  UPON CONSOLE
               DISPLAY 'CLOSE STNLKUP IN ERROR,CODE = ' STNLKUP-IN-STAT
                                                  UPON CONSOLE
               DISPLAY 'CLOSE STNLKUP IN ERROR,CODE = ' STNLKUP-IN-STAT
           END-IF.

           CLOSE  STMT-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCREF ***'         UPON CONSOLE
               DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
           END-IF.

           CLOSE  PRTF-OUT-FL.
           IF  PRTF-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCREF ***'         UPON CONSOLE
               DISPLAY 'CLOSE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'CLOSE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT
           END-IF.

           STOP   RUN.

       9999-EXIT.    EXIT.

      *-------------------- END PROGRAM -------------------*
/*
//LKED.SYSLIB   DD  DSN=&LIBPRFX..SCEELKED,DISP=SHR
//              DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD,DISP=SHR
//LKED.SYSLMOD  DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD(STMMCREF),DISP=SHR
//LKED.SYSPRINT DD  SYSOUT=X
/*
/*