//EBCMMC09 JOB EBCMMC09,'EBCMMC09',MSGCLASS=I,CLASS=7,                  00010001
//         MSGLEVEL=(1,1),REGION=8M                                     00020000
//*--------------------------------------------------------------------*00030000
//         EXEC BCMDATE1                                                00031000
//*--------------------------------------------------------------------*00031100
//STEP01   EXEC PGM=IEFBR14                                             00031200
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS2500.MCASH.STMT,                     00031305
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00031400
/*                                                                      00031500
//STEP02   EXEC PGM=IEFBR14                                             00031600
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS2500.MCASH.STMT,                     00031705
//           DISP=(NEW,CATLG,DELETE),                                   00031800
//           DCB=(RECFM=FB,LRECL=2500,BLKSIZE=0,DSORG=PS),              00031900
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        00032000
/*                                                                      00032100
//STEP03   EXEC PGM=IEFBR14                                             00032200
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.REF,              00032305
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00032400
/*                                                                      00032500
//STEP04   EXEC PGM=IEFBR14                                             00032600
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.REF,              00032705
//           DISP=(NEW,CATLG,DELETE),                                   00032800
//           DCB=(RECFM=FB,LRECL=132,BLKSIZE=0,DSORG=PS),               00032900
//           SPACE=(CYL,(1,5),RLSE),UNIT=3390                           00033000
/*                                                                      00033100
//STEP05   EXEC PGM=STMMCREF                                            00033200
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  00033305
//SYSCOUNT DD SYSOUT=X                                                  00033400
//SYSOUT   DD SYSOUT=X                                                  00033500
//SYSPRINT DD SYSOUT=X                                                  00033600
//*==================================================                   00033700
//STMTINF  DD DSN=PSPBCM.BCM.BCM.PS3200.GENSTMT.DETL,DISP=SHR           00033805
//IMNLKUP  DD DSN=GVBFNF.IM.IM.P140.IMLKPM.V,DISP=SHR                   00033905
//STNLKUP  DD DSN=GVBFNF.ST.ST.P140.STLKPM.V,DISP=SHR                   00034005
//STMTOUTF DD DSN=PSPBCM.BCM.BCM.PS2500.MCASH.STMT,DISP=SHR             00034105
//PRTFOUTF DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.REF,DISP=SHR      00034205
/*                                                                      00034300
//*--------------------------------------------------------------------*00035000
//*    MERGE STMT. & FOR-OUTWARD DETAIL                               -*00050000
//*    WAIT FOR FILE                                                  -*00100000
//*    ==> PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R1ST                  -*00121000
//*       (BACKUP FILE : PSPBCM.FOR.BCM.OUTWARD.R1ST.&DAYDATE.V1.TM2) -*00122000
//*    ==> PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R2ND                  -*00123000
//*       (BACKUP FILE : PSPBCM.FOR.BCM.OUTWARD.R2ND.&DAYDATE.V1.TM2) -*00123100
//*--------------------------------------------------------------------*00130000
//STEP06   EXEC PGM=IEFBR14                                             00131000
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS1100.OUTWARD.R1ST.SORT,              00132005
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00133000
/*                                                                      00134000
//STEP07   EXEC PGM=IEFBR14                                             00135000
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS1100.OUTWARD.R1ST.SORT,              00136005
//           DISP=(NEW,CATLG,DELETE),                                   00137000
//           DCB=(RECFM=FB,LRECL=1100,BLKSIZE=0,DSORG=PS),              00138000
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        00139000
/*                                                                      00139100
//STEP08   EXEC PGM=SORT                                                00139200
//SORTIN   DD DSN=PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R1ST,DISP=SHR    00139305
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS1100.OUTWARD.R1ST.SORT,DISP=SHR      00139405
//SYSOUT   DD SYSOUT=X                                                  00139500
//SYSIN    DD *                                                         00139600
    SORT   FIELDS=(106,16,A),                                           00139700
           FORMAT=CH,EQUALS                                             00139800
    OUTREC FIELDS=(106,16,                                              00139900
                   1,1038,46X)                                          00140000
    RECORD TYPE=F,LENGTH=1100                                           00140100
    OPTION  EQUALS                                                      00140200
   END                                                                  00140300
/*                                                                      00140400
//STEP09   EXEC PGM=IEFBR14                                             00140500
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS1100.OUTWARD.R2ND.SORT,              00140605
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00140700
/*                                                                      00140800
//STEP10   EXEC PGM=IEFBR14                                             00140900
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS1100.OUTWARD.R2ND.SORT,              00141005
//           DISP=(NEW,CATLG,DELETE),                                   00141100
//           DCB=(RECFM=FB,LRECL=1100,BLKSIZE=0,DSORG=PS),              00141200
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        00141300
/*                                                                      00141400
//STEP11   EXEC PGM=SORT                                                00141500
//SORTIN   DD DSN=PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R2ND,DISP=SHR    00141605
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS1100.OUTWARD.R2ND.SORT,DISP=SHR      00141705
//SYSOUT   DD SYSOUT=X                                                  00141800
//SYSIN    DD *                                                         00141900
    SORT   FIELDS=(106,16,A),                                           00142000
           FORMAT=CH,EQUALS                                             00142100
    OUTREC FIELDS=(106,16,                                              00142200
                   1,1038,46X)                                          00142300
    RECORD TYPE=F,LENGTH=1100                                           00142400
    OPTION  EQUALS                                                      00142500
   END                                                                  00142600
/*                                                                      00142700
//STEP12   EXEC PGM=IDCAMS,REGION=8M                                    00144800
//SYSPRINT DD SYSOUT=*                                                  00144900
//SYSIN    DD *                                                         00145000
   DELETE  PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL -                         00145105
           PURGE                                                        00145200
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL) -          00145305
           CYL(50 50)                                 -                 00145400
           RECSZ(1100 1100)                           -                 00145500
           VOL(JP2165 JP2166)                         -                 00145600
           INDEXED                                    -                 00145700
           KEYS(16 0))                                -                 00145800
   DATA    (NAME(PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL.DATA)) -            00145905
   INDEX   (NAME(PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL.INDEX))             00146005
/*                                                                      00146100
//STEP13   EXEC PGM=IDCAMS                                              00146200
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS1100.OUTWARD.R1ST.SORT,DISP=SHR      00146305
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL,DISP=SHR           00146505
//SYSOUT   DD SYSOUT=X                                                  00146600
//SYSPRINT DD SYSOUT=X                                                  00146700
//SYSIN    DD *                                                         00146800
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 00146900
/*                                                                      00147000
//STEP14   EXEC PGM=IDCAMS                                              00147100
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS1100.OUTWARD.R2ND.SORT,DISP=SHR      00147305
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL,DISP=SHR           00147405
//SYSOUT   DD SYSOUT=X                                                  00147500
//SYSPRINT DD SYSOUT=X                                                  00147600
//SYSIN    DD *                                                         00147700
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 00147800
/*                                                                      00147900
//STEP15   EXEC PGM=IEFBR14                                             00148000
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS2516.MCASH.STMT.OR,                  00149005
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00149100
/*                                                                      00149200
//STEP16   EXEC PGM=IEFBR14                                             00149300
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS2516.MCASH.STMT.OR,                  00149405
//           DISP=(NEW,CATLG,DELETE),                                   00149500
//           DCB=(RECFM=FB,LRECL=2516,BLKSIZE=0,DSORG=PS),              00149600
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        00149700
/*                                                                      00149800
//STEP17   EXEC PGM=SORT                                                00149900
//SORTIN   DD DSN=PSPBCM.BCM.BCM.PS2500.MCASH.STMT,DISP=SHR             00150005
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS2516.MCASH.STMT.OR,DISP=SHR          00150105
//SYSOUT   DD SYSOUT=X                                                  00150200
//SYSIN    DD *                                                         00150300
    SORT   FIELDS=(7,10,A,113,6,A),                                     00150400
           FORMAT=CH,EQUALS                                             00150500
    OUTREC FIELDS=(7,10,113,6,                                          00150600
                   1,2500)                                              00150700
    RECORD TYPE=F,LENGTH=2516                                           00150800
    OPTION  EQUALS                                                      00150900
   END                                                                  00151000
/*                                                                      00151100
//STEP18   EXEC PGM=IDCAMS,REGION=8M                                    00151200
//SYSPRINT DD SYSOUT=*                                                  00151300
//SYSIN    DD *                                                         00151400
   DELETE  PVBBCM.BCM.BCM.PS2516.MCASH.STMT -                           00151505
           PURGE                                                        00151600
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.PS2516.MCASH.STMT) -            00151705
           CYL(50 50)                                 -                 00151800
           RECSZ(2516 2516)                           -                 00151900
           VOL(JP2165 JP2166)                         -                 00152000
           INDEXED                                    -                 00152100
           KEYS(16 0))                                -                 00152200
   DATA    (NAME(PVBBCM.BCM.BCM.PS2516.MCASH.STMT.DATA)) -              00152305
   INDEX   (NAME(PVBBCM.BCM.BCM.PS2516.MCASH.STMT.INDEX))               00152405
/*                                                                      00152500
//STEP19   EXEC PGM=IDCAMS                                              00152600
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS2516.MCASH.STMT.OR,DISP=SHR          00152705
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS2516.MCASH.STMT,DISP=SHR             00152805
//SYSOUT   DD SYSOUT=X                                                  00152900
//SYSPRINT DD SYSOUT=X                                                  00153000
//SYSIN    DD *                                                         00153100
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 00153200
/*                                                                      00153300
//STEP20   EXEC PGM=IEFBR14                                             00153400
//FILE001  DD DSN=PSPBCM.BCM.ERP.PS2500.MCASH.STMT,                     00153505
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00153600
/*                                                                      00153700
//STEP21   EXEC PGM=IEFBR14                                             00153800
//FILE001  DD DSN=PSPBCM.BCM.ERP.PS2500.MCASH.STMT,                     00153905
//           DISP=(NEW,CATLG,DELETE),                                   00154000
//           DCB=(RECFM=FB,LRECL=2500,BLKSIZE=0,DSORG=PS),              00154100
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        00154200
/*                                                                      00154300
//STEP22   EXEC PGM=IEFBR14                                             00154400
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.OR,               00154505
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00154600
/*                                                                      00154700
//STEP23   EXEC PGM=IEFBR14                                             00154800
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.OR,               00154905
//           DISP=(NEW,CATLG,DELETE),                                   00155000
//           DCB=(RECFM=FB,LRECL=132,BLKSIZE=0,DSORG=PS),               00155100
//           SPACE=(CYL,(1,5),RLSE),UNIT=3390                           00155200
/*                                                                      00155300
//STEP24   EXEC PGM=STMMCG07                                            00155400
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  00155505
//SYSCOUNT DD SYSOUT=X                                                  00155600
//SYSOUT   DD SYSOUT=X                                                  00155700
//SYSPRINT DD SYSOUT=X                                                  00155800
//*==================================================                   00155900
//ACCPROF  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       00156005
//STMTINF  DD DSN=PVBBCM.BCM.BCM.PS2516.MCASH.STMT,DISP=SHR             00156105
//DETLINF  DD DSN=PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL,DISP=SHR           00156205
//STMTOUTF DD DSN=PSPBCM.BCM.ERP.PS2500.MCASH.STMT,DISP=SHR             00156305
//PRTFOUTF DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.OR,DISP=SHR       00156405
/*                                                                      00156500
//STEPB01  EXEC PGM=IDCAMS                                              00156600
//INPUT    DD DSN=PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R1ST,DISP=SHR    00156705
//OUTPUT   DD DSN=PSPBCM.FOR.BCM.OUTWARD.R1ST.&DAYDATE.V1.TM2,          00156805
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(CYL,(50,100),RLSE),        00156900
//         DCB=(RECFM=FB,LRECL=1038,BLKSIZE=0)                          00157000
//SYSOUT   DD SYSOUT=X                                                  00157100
//SYSPRINT DD SYSOUT=X                                                  00157200
//SYSIN    DD *                                                         00157300
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 00157400
/*                                                                      00157500
//STEPB02  EXEC PGM=IDCAMS                                              00157600
//INPUT    DD DSN=PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R2ND,DISP=SHR    00157705
//OUTPUT   DD DSN=PSPBCM.FOR.BCM.OUTWARD.R2ND.&DAYDATE.V1.TM2,          00157805
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(CYL,(50,100),RLSE),        00157900
//         DCB=(RECFM=FB,LRECL=1038,BLKSIZE=0)                          00158000
//SYSOUT   DD SYSOUT=X                                                  00158100
//SYSPRINT DD SYSOUT=X                                                  00158200
//SYSIN    DD *                                                         00158300
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 00158400
/*                                                                      00158500
//STEPB03  EXEC PGM=IDCAMS                                              00158600
//INPUT    DD DSN=PSPBCM.BCM.ERP.PS2500.MCASH.STMT,DISP=SHR             00158705
//OUTPUT   DD DSN=PSPBCM.BCM.ERP.MCASH.STMT.&DAYDATE.V1.TM2,            00158805
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(CYL,(50,100),RLSE),        00158900
//         DCB=(RECFM=FB,LRECL=2500,BLKSIZE=0)                          00159000
//SYSOUT   DD SYSOUT=X                                                  00159100
//SYSPRINT DD SYSOUT=X                                                  00159200
//SYSIN    DD *                                                         00159300
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 00159400
/*                                                                      00159500
//STEPD1  EXEC PGM=IEFBR14                                              00159604
//FILE001 DD DSN=PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R1ST,             00159705
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           00159804
/*                                                                      00159904
//STEPD2  EXEC PGM=IEFBR14                                              00160004
//FILE002 DD DSN=PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R2ND,             00160105
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           00160204
/*                                                                      00160304
//                                                                      00160404
//                                                                      00161000
//                                                                      00170000