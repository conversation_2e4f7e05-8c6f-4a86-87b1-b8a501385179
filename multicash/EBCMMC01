//EBCMMC01 JOB EBCMMC01,'EBCMMC01',MSGCLASS=X,CLASS=7,
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)
//*----------------------------------------------------------------*
//         EXEC BCMDATE1
//*----------------------------------------------------------------*
//JOBLIB   DD DSN=CEE.SCEERUN,DISP=SHR
//STEP01   EXEC PGM=IEFBR14
//FILE002  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,
//         SPACE=(CYL,(60,30),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)
/*
//STEP02   EXEC PGM=IEFBR14
//FILE002  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,
//           DISP=(NEW,CATLG,DELETE),
//           DCB=(RECFM=FB,LRECL=200,BLKSIZE=0,DSORG=PS),
//           SPACE=(CYL,(60,30),RLSE),UNIT=3390
/*
//STEP03  EXEC PGM=SORT
//SORTIN   DD DSN=PSPBCM.ERP.BCM.P140.ERPACCT,DISP=SHR
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR
//SYSOUT   DD SYSOUT=X
//SYSIN    DD *
    SORT   FIELDS=(17,10,A),
           FORMAT=CH,EQUALS
    INCLUDE COND=(151,1,CH,EQ,C'Y')
    RECORD TYPE=F,LENGTH=200
    OPTION  EQUALS
   END
/*
//STEP04   EXEC PGM=IDCAMS,REGION=8M
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
   DELETE  PVBBCM.BCM.BCM.P140.ERPACCT.MCASH -
           PURGE
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.P140.ERPACCT.MCASH) -
           CYL(50 50)                                 -
           RECSZ(200 200)                             -
           VOL(JP2165 JP2166)                         -
           INDEXED                                    -
           KEYS(10 16))                                -
   DATA    (NAME(PVBBCM.BCM.BCM.P140.ERPACCT.MCASH.DATA)) -
   INDEX   (NAME(PVBBCM.BCM.BCM.P140.ERPACCT.MCASH.INDEX))
/*
//STEP05   EXEC PGM=IDCAMS
//INPUT    DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.P140.ERPACCT.MCASH,DISP=SHR
//SYSOUT   DD SYSOUT=X
//SYSPRINT DD SYSOUT=X
//SYSIN    DD *
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)
  IF   MAXCC EQ 08 THEN SET MAXCC EQ 12
/*
//
//
//
//
//