//EBCMMC06 JOB <PERSON><PERSON>MMC06,'EBCMMC06',MSGCLASS=I,CLASS=7,                  00010015
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)                         00020000
//*----------------------------------------------------------------*    00030016
//         EXEC BCMDATE1                                                00031019
//*----------------------------------------------------------------*    00032016
//* WAIT FILE : PSPBCM.EPP.BCM.PS150.BILLER.PAYTRN               --*    00050026
//*           : PSPBCM.EPPD.BCM.PS150.BILLER.PAYTRN              --*    00050127
//*--  MER<PERSON> STMT. & EPP DETAIL                                  --*    00051026
//* SR-17185   EPP PROJECT DATA FOR MULTI CASH STATEMENT REPORT  --*    00052029
//*            IN MVP2.1.4 EFF. BATCH DATE @ 08/07/2023          --*    00052129
//*            WAIT FOR NEW INPUT FROM EPPD SYSTEM               --*    00053027
//*           - MERGE EPP & EPPD @ STEP03                        --*    00054027
//*           - HANDLE CASE EPP & EPPD DUPLICATE REC @ STEP05    --*    00054131
//*           - BACKUP INPUT FROM EPPD @ STEP12                  --*    00055027
//*           - DELETE INPUT FROM EPPD @ STEP14                  --*    00056027
//*----------------------------------------------------------------*    00060013
//STEP01   EXEC PGM=IEFBR14                                             01496300
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS185.EBPPSTMT.DETL,                   01496425
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01496500
/*                                                                      01497000
//STEP02   EXEC PGM=IEFBR14                                             01500000
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS185.EBPPSTMT.DETL,                   01510025
//           DISP=(NEW,CATLG,DELETE),                                   01520000
//           DCB=(RECFM=FB,LRECL=185,BLKSIZE=0,DSORG=PS),               01530009
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01540000
/*                                                                      01550000
//STEP03   EXEC PGM=SORT                                                01550201
//SORTIN   DD DSN=PSPBCM.EPP.BCM.PS150.BILLER.PAYTRN,DISP=SHR           01550425
//         DD DSN=PSPBCM.EPPD.BCM.PS150.BILLER.PAYTRN,DISP=SHR          01550527
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS185.EBPPSTMT.DETL,DISP=SHR           01550627
//SYSOUT   DD SYSOUT=X                                                  01550727
//SYSIN    DD *                                                         01550827
    SORT   FIELDS=(1,1,A,124,10,A,64,6,A,27,16,A),                      01550927
           FORMAT=CH,EQUALS                                             01551027
    INCLUDE COND=(1,1,CH,EQ,C'P')                                       01551127
    OUTREC  FIELDS=(1,1,124,10,64,6,27,16,1,150,2X)                     01551209
    RECORD TYPE=F,LENGTH=185                                            01551509
    OPTION  EQUALS                                                      01551601
   END                                                                  01551701
/*                                                                      01551801
//STEP04   EXEC PGM=IDCAMS,REGION=8M                                    01551902
//SYSPRINT DD SYSOUT=*                                                  01552002
//SYSIN    DD *                                                         01552102
   DELETE  PVBBCM.BCM.BCM.PS185.EBPPSTMT.DETL -                         01552225
           PURGE                                                        01552302
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.PS185.EBPPSTMT.DETL) -          01552425
           CYL(50 50)                                 -                 01552502
           RECSZ(185 185)                             -                 01552609
           VOL(JP2165 JP2166)                         -                 01552702
           INDEXED                                    -                 01552802
           KEYS(33 0))                                -                 01552908
   DATA    (NAME(PVBBCM.BCM.BCM.PS185.EBPPSTMT.DETL.DATA)) -            01553025
   INDEX   (NAME(PVBBCM.BCM.BCM.PS185.EBPPSTMT.DETL.INDEX))             01553125
/*                                                                      01553202
//STEP05   EXEC PGM=IDCAMS                                              01553909
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS185.EBPPSTMT.DETL,DISP=SHR           01554025
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS185.EBPPSTMT.DETL,DISP=SHR           01554125
//SYSOUT   DD SYSOUT=X                                                  01554209
//SYSPRINT DD SYSOUT=X                                                  01554309
//SYSIN    DD *                                                         01554409
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT) REPLACE  ERRORLIMIT(500)        01554530
   IF   MAXCC EQ 08 THEN SET MAXCC EQ 00                                01554630
/*                                                                      01555002
//STEP06   EXEC PGM=IEFBR14                                             01555720
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS700.GENSTMT.DETL,                    01555825
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01555909
/*                                                                      01556009
//STEP07   EXEC PGM=IEFBR14                                             01556120
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS700.GENSTMT.DETL,                    01556225
//           DISP=(NEW,CATLG,DELETE),                                   01557001
//           DCB=(RECFM=FB,LRECL=700,BLKSIZE=0,DSORG=PS),               01558007
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01559001
/*                                                                      01559101
//STEP08   EXEC PGM=IEFBR14                                             01560020
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.EBPP,             01570025
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01580000
/*                                                                      01590000
//STEP09   EXEC PGM=IEFBR14                                             01600020
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.EBPP,             01610025
//           DISP=(NEW,CATLG,DELETE),                                   01620000
//           DCB=(RECFM=FB,LRECL=132,BLKSIZE=0,DSORG=PS),               01630000
//           SPACE=(CYL,(1,5),RLSE),UNIT=3390                           01640000
/*                                                                      01650000
//STEP10   EXEC PGM=STMMCG03                                            01840020
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  01850025
//SYSCOUNT DD SYSOUT=X                                                  01860000
//SYSOUT   DD SYSOUT=X                                                  01870000
//SYSPRINT DD SYSOUT=X                                                  01880000
//*==================================================                   02030000
//ACCPROF  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       02031025
//STMTINF  DD DSN=PVBBCM.BCM.BCM.PS550.GENSTMT.STMTBPDT,DISP=SHR        02040025
//DETLINF  DD DSN=PVBBCM.BCM.BCM.PS185.EBPPSTMT.DETL,DISP=SHR           02050025
//STMTOUTF DD DSN=PSPBCM.BCM.BCM.PS700.GENSTMT.DETL,DISP=SHR            02070025
//PRTFOUTF DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.EBPP,DISP=SHR     02080025
/*                                                                      02100000
//STEP11  EXEC PGM=IDCAMS                                               02100220
//INPUT    DD DSN=PSPBCM.EPP.BCM.PS150.BILLER.PAYTRN,DISP=SHR           02100325
//OUTPUT   DD DSN=PSPBCM.EPP.BCM.BILLER.PAYTRN.&DAYDATE.V1.TM1,         02100525
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(TRK,(900,900),RLSE),       02100616
//         DCB=(RECFM=FB,LRECL=150,BLKSIZE=15000)                       02100716
//SYSOUT   DD SYSOUT=X                                                  02100816
//SYSPRINT DD SYSOUT=X                                                  02100916
//SYSIN    DD *                                                         02101016
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 02101116
   IF   MAXCC EQ 12 THEN SET MAXCC EQ 00                                02101216
/*                                                                      02101316
//STEP12  EXEC PGM=IDCAMS                                               02101427
//INPUT    DD DSN=PSPBCM.EPPD.BCM.PS150.BILLER.PAYTRN,DISP=SHR          02101527
//OUTPUT   DD DSN=PSPBCM.EPPD.BILLER.PAYTRN.&DAYDATE.V1.TM1,            02101627
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(TRK,(900,900),RLSE),       02101727
//         DCB=(RECFM=FB,LRECL=150,BLKSIZE=15000)                       02101827
//SYSOUT   DD SYSOUT=X                                                  02101927
//SYSPRINT DD SYSOUT=X                                                  02102027
//SYSIN    DD *                                                         02102127
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 02102227
   IF   MAXCC EQ 12 THEN SET MAXCC EQ 00                                02102327
/*                                                                      02102427
//STEP13  EXEC PGM=IEFBR14                                              02102527
//FILE002 DD DSN=PSPBCM.EPP.BCM.PS150.BILLER.PAYTRN,                    02102627
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           02102727
/*                                                                      02102827
//STEP14  EXEC PGM=IEFBR14                                              02102927
//FILE002 DD DSN=PSPBCM.EPPD.BCM.PS150.BILLER.PAYTRN,                   02103027
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           02103127
/*                                                                      02103227
//STEPBK   EXEC PGM=IDCAMS                                              02103327
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS700.GENSTMT.DETL,DISP=SHR            02103427
//OUTPUT   DD DSN=PSPBCM.BCM.BCM.PS700.GSTMT.&DAYDATE.V1.TM2,           02103527
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(CYL,(50,100),RLSE),        02103627
//         DCB=(RECFM=FB,LRECL=700,BLKSIZE=0)                           02103727
//SYSOUT   DD SYSOUT=X                                                  02103827
//SYSPRINT DD SYSOUT=X                                                  02103927
//SYSIN    DD *                                                         02104027
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 02104127
/*                                                                      02104227
//                                                                      02104327
//                                                                      02104427
//                                                                      02104527
//                                                                      02104627
//                                                                      02105000