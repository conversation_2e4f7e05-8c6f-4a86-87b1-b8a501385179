//EBCMMC08 JOB EBCMMC08,'EBCMMC08',MSGCLASS=I,CLASS=7,                  00010001
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)                         00020005
//*--------------------------------------------------------------------*00030000
//         EXEC BCMDATE1                                                00031000
//*--------------------------------------------------------------------*00032000
//*    MERGE STMT. & CN/IPS DETAIL                                    -*00050000
//*    RUN AFTER JOB : EBCMERP4                                       -*00090000
//*    WAIT FOR FILE                                                  -*00100000
//*       ==>   PSPBCM.IPS.BCM.DEBITFL.&DAYDATE.V1.TM2                -*00121000
//*           ( BACKUP FILE : PSPBCM.IPS.BCM.P140.DEBITFL )           -*00122000
//*       ==>   PSPBCM.IPS.BCM.CREDITFL.&DAYDATE.V1.TM2               -*00124000
//*           ( BACKUP FILE : PSPBCM.IPS.BCM.P140.CREDITFL )          -*00125000
//*       ==>   PSPBCM.CN.BCM.DEBITFL.&DAYDATE.V1.TM2                 -*00127000
//*           ( BACKUP FILE : PSPBCM.CN.BCM.P140.DEBITFL )            -*00128000
//*       ==>   PSPBCM.CN.BCM.CREDITFL.&DAYDATE.V1.TM2                -*00129100
//*           ( BACKUP FILE : PSPBCM.CN.BCM.P140.CREDITFL )           -*00129200
//*--------------------------------------------------------------------*00130000
//* OT61020014 CN Decommissioning @ 11/05/2018                        - 00150000
//*-------------------------------------------------------------------- 00160000
//STEP01   EXEC PGM=IEFBR14                                             01010000
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL.SORT,              01020003
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01030000
/*                                                                      01040000
//STEP02   EXEC PGM=IEFBR14                                             01050000
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL.SORT,              01060003
//           DISP=(NEW,CATLG,DELETE),                                   01070000
//           DCB=(RECFM=FB,LRECL=2100,BLKSIZE=0,DSORG=PS),              01080000
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01090000
/*                                                                      01100000
//STEP03   EXEC PGM=SORT                                                01110000
//SORTIN   DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL,DISP=SHR           01130003
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL.SORT,DISP=SHR      01140003
//SYSOUT   DD SYSOUT=X                                                  01150000
//SYSIN    DD *                                                         01160000
    SORT   FIELDS=(1,16,A),                                             01170000
           FORMAT=CH,EQUALS                                             01180000
    RECORD TYPE=F,LENGTH=2100                                           01200000
    OPTION  EQUALS                                                      01210000
   END                                                                  01220000
/*                                                                      01230000
//STEP04   EXEC PGM=IDCAMS,REGION=8M                                    01240000
//SYSPRINT DD SYSOUT=*                                                  01250000
//SYSIN    DD *                                                         01260000
   DELETE  PVBBCM.BCM.BCM.PS2100.GENSTMT.DETL -                         01270003
           PURGE                                                        01280000
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.PS2100.GENSTMT.DETL) -          01290003
           CYL(50 50)                                 -                 01300000
           RECSZ(2100 2100)                           -                 01310000
           VOL(JP2165 JP2166)                         -                 01320000
           INDEXED                                    -                 01330000
           KEYS(16 0))                                -                 01340000
   DATA    (NAME(PVBBCM.BCM.BCM.PS2100.GENSTMT.DETL.DATA)) -            01350003
   INDEX   (NAME(PVBBCM.BCM.BCM.PS2100.GENSTMT.DETL.INDEX))             01360003
/*                                                                      01370000
//STEP05   EXEC PGM=IDCAMS                                              01380000
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL.SORT,DISP=SHR      01390003
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS2100.GENSTMT.DETL,DISP=SHR           01400003
//SYSOUT   DD SYSOUT=X                                                  01410000
//SYSPRINT DD SYSOUT=X                                                  01420000
//SYSIN    DD *                                                         01430000
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 01440000
/*                                                                      01441000
//*----------------------------------------------------------------*    01450000
//*   CN / IPS                                                          01451000
//*----------------------------------------------------------------*    01451300
//STEP01   EXEC PGM=IDCAMS,REGION=8M                                    01459600
//SYSPRINT DD SYSOUT=*                                                  01459800
//SYSIN    DD *                                                         01459900
   DELETE  PVBBCM.BCM.BCM.P140.DRSTMT.MCASH.WORK     -                  01460003
           PURGE                                                        01460100
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.P140.DRSTMT.MCASH.WORK) -       01460203
           CYL(50 50)                                 -                 01460300
           RECSZ(950 950)                             -                 01460400
           VOLUMES(JP4101 JP441E)                     -                 01460500
           INDEXED                                    -                 01460600
           KEYS(71 0))                                -                 01460700
   DATA    (NAME(PVBBCM.BCM.BCM.P140.DRSTMT.MCASH.WORK.DATA)) -         01460803
   INDEX   (NAME(PVBBCM.BCM.BCM.P140.DRSTMT.MCASH.WORK.INDEX))          01460903
/*                                                                      01461000
//STEP02   EXEC PGM=IDCAMS,REGION=8M                                    01461100
//SYSPRINT DD SYSOUT=*                                                  01461200
//SYSIN    DD *                                                         01461300
   DELETE  PVBBCM.BCM.BCM.P140.CRSTMT.MCASH.WORK      -                 01461403
           PURGE                                                        01461500
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.P140.CRSTMT.MCASH.WORK) -       01461603
           CYL(50 50)                                 -                 01461700
           RECSZ(950 950)                             -                 01461800
           VOLUMES(JP4101 JP441E)                     -                 01461900
           INDEXED                                    -                 01462000
           KEYS(71 0))                                -                 01462100
   DATA    (NAME(PVBBCM.BCM.BCM.P140.CRSTMT.MCASH.WORK.DATA)) -         01462203
   INDEX   (NAME(PVBBCM.BCM.BCM.P140.CRSTMT.MCASH.WORK.INDEX))          01462303
/*                                                                      01462400
//*-------------------------------------------------*                   01462500
//* Edit Fild Amount                                                    01462600
//*    Ex.  0123456789012987 => 0123456789012.98                        01462700
//*                                                                     01462800
//* INPUT FILE  : PSPBCM.IPS.BCM.P140.DEBITFL                           01462903
//*               PSPBCM.IPS.BCM.P140.CREDITFL                          01463003
//*                                                                     01463100
//* OUTPUT FILE : PSPBCM.IPS.BCM.P140.DEBITFL.EDTAMT                    01463203
//*               PSPBCM.IPS.BCM.P140.CREDITFL.EDTAMT                   01463303
//*                                                                     01463400
//*    *--------------------------------------------*                   01463500
//*    |     Field Name       | From | To  | Length |                   01463600
//*    |                      |      |     |  Byte  |                   01463700
//*    |----------------------+------+-----+--------|                   01463800
//*    | Transaction Amount   |   34 |  49 |   16   |                   01463900
//*    | Debit Amount         |  192 | 207 |   16   |                   01464000
//*    | Credit Amount        |  276 | 291 |   16   |                   01464100
//*    | Net Credit Amount    |  292 | 307 |   16   |                   01464200
//*    | Bene Fee Charge      |  308 | 323 |   16   |                   01464300
//*    | Total WHT Amount     |  537 | 552 |   16   |                   01464400
//*    | Total Invoice Amount |  559 | 574 |   16   |                   01464500
//*    *--------------------------------------------*                   01464600
//*                                                                     01464700
//STEP03  EXEC PGM=IEFBR14                                              01464800
//FILE002 DD DSN=PSPBCM.IPS.BCM.P140.DEBITFL.MCASH.EDTAMT,              01464903
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           01465000
/*                                                                      01465100
//STEP04   EXEC PGM=IEFBR14                                             01465200
//FILE002  DD DSN=PSPBCM.IPS.BCM.P140.DEBITFL.MCASH.EDTAMT,             01465303
//           DISP=(NEW,CATLG,DELETE),                                   01465400
//           DCB=(RECFM=FB,LRECL=900,BLKSIZE=27900,DSORG=PS),           01465500
//           SPACE=(CYL,(5,10),RLSE),UNIT=3390                          01465600
/*                                                                      01465700
//STEP05   EXEC PGM=SORT                                                01465800
//SORTIN   DD DSN=PSPBCM.IPS.BCM.DEBITFL.&DAYDATE.V1.TM2,DISP=SHR       01466003
//SORTOUT  DD DSN=PSPBCM.IPS.BCM.P140.DEBITFL.MCASH.EDTAMT,DISP=SHR     01466103
//SYSOUT   DD SYSOUT=X                                                  01466200
//SYSIN    DD *                                                         01466300
    SORT   FIELDS=COPY                                                  01466400
    OUTREC  FIELDS=(1,46,C'.',47,2,50,155,C'.',205,2,                   01466500
                    208,81,C'.',289,2,292,13,C'.',305,2,                01466600
                    308,13,C'.',321,2,324,226,C'.',550,2,               01466700
                    553,19,C'.',572,2,575,326)                          01466800
    RECORD TYPE=F,LENGTH=900                                            01466900
    OPTION  EQUALS                                                      01467000
   END                                                                  01467100
/*                                                                      01467200
//STEP06  EXEC PGM=IEFBR14                                              01467300
//FILE002 DD DSN=PSPBCM.IPS.BCM.P140.CREDITFL.MCASH.EDTAMT,             01467403
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           01467500
/*                                                                      01467600
//STEP07   EXEC PGM=IEFBR14                                             01467700
//FILE002  DD DSN=PSPBCM.IPS.BCM.P140.CREDITFL.MCASH.EDTAMT,            01467803
//           DISP=(NEW,CATLG,DELETE),                                   01467900
//           DCB=(RECFM=FB,LRECL=900,BLKSIZE=27900,DSORG=PS),           01468000
//           SPACE=(CYL,(5,10),RLSE),UNIT=3390                          01468100
/*                                                                      01468200
//STEP08   EXEC PGM=SORT                                                01468300
//SORTIN   DD DSN=PSPBCM.IPS.BCM.CREDITFL.&DAYDATE.V1.TM2,DISP=SHR      01468503
//SORTOUT  DD DSN=PSPBCM.IPS.BCM.P140.CREDITFL.MCASH.EDTAMT,DISP=SHR    01468603
//SYSOUT   DD SYSOUT=X                                                  01468700
//SYSIN    DD *                                                         01468800
    SORT   FIELDS=COPY                                                  01468900
    OUTREC  FIELDS=(1,46,C'.',47,2,50,155,C'.',205,2,                   01469000
                    208,81,C'.',289,2,292,13,C'.',305,2,                01469100
                    308,13,C'.',321,2,324,226,C'.',550,2,               01469200
                    553,19,C'.',572,2,575,326)                          01469300
    RECORD TYPE=F,LENGTH=900                                            01469400
    OPTION  EQUALS                                                      01469500
   END                                                                  01469600
/*                                                                      01469700
//STEP09  EXEC PGM=IEFBR14                                              01470000
//FILE002 DD DSN=PSPBCM.BCM.BCM.P140.P950.DRSTMT.MCASH.WK1,             01470103
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           01470200
/*                                                                      01470300
//STEP10  EXEC PGM=IEFBR14                                              01470400
//FILE002 DD DSN=PSPBCM.BCM.BCM.P140.P950.DRSTMT.MCASH.WK2,             01470503
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           01470600
/*                                                                      01470700
//STEP11   EXEC PGM=IEFBR14                                             01470800
//FILE002  DD DSN=PSPBCM.BCM.BCM.P140.P950.DRSTMT.MCASH.WK1,            01470903
//           DISP=(NEW,CATLG,DELETE),                                   01471000
//           DCB=(RECFM=FB,LRECL=950,BLKSIZE=28500,DSORG=PS),           01471100
//           SPACE=(CYL,(5,10),RLSE),UNIT=3390                          01471200
/*                                                                      01471300
//STEP12   EXEC PGM=IEFBR14                                             01471400
//FILE002  DD DSN=PSPBCM.BCM.BCM.P140.P950.DRSTMT.MCASH.WK2,            01471503
//           DISP=(NEW,CATLG,DELETE),                                   01471600
//           DCB=(RECFM=FB,LRECL=950,BLKSIZE=28500,DSORG=PS),           01471700
//           SPACE=(CYL,(5,10),RLSE),UNIT=3390                          01471800
/*                                                                      01471900
//STEP13   EXEC PGM=SORT                                                01472000
//SORTIN   DD DSN=PSPBCM.IPS.BCM.P140.DEBITFL.MCASH.EDTAMT,DISP=SHR     01472103
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.P140.P950.DRSTMT.MCASH.WK1,DISP=SHR    01472203
//SYSOUT   DD SYSOUT=X                                                  01472300
//SYSIN    DD *                                                         01472400
    SORT   FIELDS=(127,12,A,147,3,A,158,10,A,1,12,A,51,24,A,245,6,A),   01472500
           FORMAT=CH,EQUALS                                             01472600
    OUTREC  FIELDS=(127,12,147,3,1X,147,3,158,10,1,12,51,24,245,6,      01472700
                    1,852,27X)                                          01472800
    RECORD TYPE=F,LENGTH=950                                            01472900
    OPTION  EQUALS                                                      01473000
   END                                                                  01473100
/*                                                                      01473200
//STEP14   EXEC PGM=SORT                                                01473300
//SORTIN   DD DSN=PSPBCM.BCM.BCM.P140.P950.DRSTMT.MCASH.WK1,DISP=SHR    01473403
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.P140.P950.DRSTMT.MCASH.WK2,DISP=SHR    01473503
//SYSOUT   DD SYSOUT=X                                                  01473600
//SYSIN    DD *                                                         01473700
   OPTION COPY                                                          01473800
       INREC IFTHEN=(WHEN=(911,3,CH,EQ,C'EWT'),                         01473900
                     OVERLAY=(13:C'EWT')),                              01474000
             IFTHEN=(WHEN=(911,3,CH,EQ,C'   '),                         01474100
                     OVERLAY=(17:C'   '))                               01474200
   END                                                                  01474300
/*                                                                      01474400
//STEP15   EXEC PGM=SORT                                                01474500
//SORTIN   DD DSN=PSPBCM.BCM.BCM.P140.P950.DRSTMT.MCASH.WK2,DISP=SHR    01474603
//SORTOUT  DD DSN=PVBBCM.BCM.BCM.P140.DRSTMT.MCASH.WORK,DISP=SHR        01474703
//SYSOUT   DD SYSOUT=X                                                  01474800
//SYSIN    DD *                                                         01474900
    SORT   FIELDS=(1,71,A),                                             01475000
           FORMAT=CH,EQUALS                                             01475100
    OUTREC  FIELDS=(1,950)                                              01475200
    RECORD TYPE=F,LENGTH=950                                            01475300
    OPTION  EQUALS                                                      01475400
   END                                                                  01475500
/*                                                                      01475600
//STEP16  EXEC PGM=IEFBR14                                              01475700
//FILE002 DD DSN=PSPBCM.BCM.BCM.P140.P950.CRSTMT.MCASH.WK1,             01475803
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           01475900
/*                                                                      01476000
//STEP17  EXEC PGM=IEFBR14                                              01476100
//FILE002 DD DSN=PSPBCM.BCM.BCM.P140.P950.CRSTMT.MCASH.WK2,             01476203
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           01476300
/*                                                                      01476400
//STEP18   EXEC PGM=IEFBR14                                             01476500
//FILE002  DD DSN=PSPBCM.BCM.BCM.P140.P950.CRSTMT.MCASH.WK1,            01476603
//           DISP=(NEW,CATLG,DELETE),                                   01476700
//           DCB=(RECFM=FB,LRECL=950,BLKSIZE=28500,DSORG=PS),           01476800
//           SPACE=(CYL,(5,10),RLSE),UNIT=3390                          01476900
/*                                                                      01477000
//STEP19   EXEC PGM=IEFBR14                                             01477100
//FILE002  DD DSN=PSPBCM.BCM.BCM.P140.P950.CRSTMT.MCASH.WK2,            01477203
//           DISP=(NEW,CATLG,DELETE),                                   01477300
//           DCB=(RECFM=FB,LRECL=950,BLKSIZE=28500,DSORG=PS),           01477400
//           SPACE=(CYL,(5,10),RLSE),UNIT=3390                          01477500
/*                                                                      01477600
//STEP20   EXEC PGM=SORT                                                01477700
//SORTIN   DD DSN=PSPBCM.IPS.BCM.P140.CREDITFL.MCASH.EDTAMT,DISP=SHR    01477803
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.P140.P950.CRSTMT.MCASH.WK1,DISP=SHR    01477903
//SYSOUT   DD SYSOUT=X                                                  01478000
//SYSIN    DD *                                                         01478100
    SORT   FIELDS=(127,12,A,147,3,A,158,10,A,1,12,A,51,24,A,245,6,A),   01478200
           FORMAT=CH,EQUALS                                             01478300
    OUTREC  FIELDS=(127,12,147,3,1X,147,3,158,10,1,12,51,24,245,6,      01478400
                    1,852,27X)                                          01478500
    RECORD TYPE=F,LENGTH=950                                            01478600
    OPTION  EQUALS                                                      01478700
   END                                                                  01478800
/*                                                                      01478900
//STEP21   EXEC PGM=SORT                                                01479000
//SORTIN   DD DSN=PSPBCM.BCM.BCM.P140.P950.CRSTMT.MCASH.WK1,DISP=SHR    01479103
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.P140.P950.CRSTMT.MCASH.WK2,DISP=SHR    01479203
//SYSOUT   DD SYSOUT=X                                                  01479300
//SYSIN    DD *                                                         01479400
   OPTION COPY                                                          01479500
       INREC IFTHEN=(WHEN=(911,3,CH,EQ,C'EWT'),                         01479600
                     OVERLAY=(13:C'EWT')),                              01479700
             IFTHEN=(WHEN=(911,3,CH,EQ,C'   '),                         01479800
                     OVERLAY=(17:C'   '))                               01479900
   END                                                                  01480000
/*                                                                      01480100
//STEP22   EXEC PGM=SORT                                                01480200
//SORTIN   DD DSN=PSPBCM.BCM.BCM.P140.P950.CRSTMT.MCASH.WK2,DISP=SHR    01480303
//SORTOUT  DD DSN=PVBBCM.BCM.BCM.P140.CRSTMT.MCASH.WORK,DISP=SHR        01480403
//SYSOUT   DD SYSOUT=X                                                  01480500
//SYSIN    DD *                                                         01480600
    SORT   FIELDS=(1,71,A),                                             01480700
           FORMAT=CH,EQUALS                                             01480800
    OUTREC  FIELDS=(1,950)                                              01480900
    RECORD TYPE=F,LENGTH=950                                            01481000
    OPTION  EQUALS                                                      01481100
   END                                                                  01481200
/*                                                                      01481300
//STEP23   EXEC PGM=IEFBR14                                             01496300
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS3200.GENSTMT.DETL,                   01496403
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01496500
/*                                                                      01497000
//STEP24   EXEC PGM=IEFBR14                                             01500000
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS3200.GENSTMT.DETL,                   01510003
//           DISP=(NEW,CATLG,DELETE),                                   01520000
//           DCB=(RECFM=FB,LRECL=3200,BLKSIZE=0,DSORG=PS),              01530000
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01540000
/*                                                                      01550000
//STEP25   EXEC PGM=IEFBR14                                             01560000
//SYSPRINT DD  SYSOUT=X                                                 01570000
//SYSUT1   DD DSN=PSPBCM.BCM.BCM.P140.RERPX004,                         01580003
//         DISP=(MOD,DELETE),                                           01590000
//         SPACE=(TRK,(1,1),RLSE),UNIT=3390                             01600000
/*                                                                      01610000
//STEP26   EXEC PGM=IEFBR14                                             01620000
//SYSPRINT DD  SYSOUT=X                                                 01630000
//SYSUT1   DD DSN=PSPBCM.BCM.BCM.P140.RERPX004,                         01640003
//         DISP=(NEW,CATLG,DELETE),                                     01650000
//         DCB=(RECFM=FB,BLKSIZE=0,LRECL=280,DSORG=PS),                 01660000
//         SPACE=(CYL,(50,100),RLSE),UNIT=3390                          01670000
/*                                                                      01680000
//STEP27   EXEC PGM=IDCAMS                                              01690000
//SYSPRINT DD SYSOUT=X                                                  01700000
//SYSIN    DD *                                                         01710000
   DELETE  PSPBCM.BCM.BCM.P140.RERPX004.DET01 -                         01720003
           PURGE                                                        01730000
   IF LASTCC EQ 08 THEN -                                               01740000
     SET MAXCC EQ 04                                                    01750000
                                                                        01760000
   DEFINE  CLUSTER(NAME(PSPBCM.BCM.BCM.P140.RERPX004.DET01) -           01770003
           VOL(JP4101 JP441E)                    -                      01780000
           CYL(50 50)                            -                      01790000
           RECSZ(157 157)                        -                      01800000
           NONINDEXED)                           -                      01810000
   DATA(NAME(PSPBCM.BCM.BCM.P140.RERPX004.DET01.DATA))                  01820003
/*                                                                      01830000
//STEP28   EXEC PGM=STMMCG05                                            01840000
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  01850003
//SYSDBOUT DD SYSOUT=X                                                  01860000
//SYSCOUNT DD SYSOUT=X                                                  01870000
//SYSOUT   DD SYSOUT=X                                                  01880000
//SYSPRINT DD SYSOUT=X                                                  01890000
//SORTWK01 DD UNIT=SYSDA,SPACE=(CYL,(50))                               01900000
//SORTWK02 DD UNIT=SYSDA,SPACE=(CYL,(50))                               01910000
//SORTWK03 DD UNIT=SYSDA,SPACE=(CYL,(50))                               01920000
//SORTWK04 DD UNIT=SYSDA,SPACE=(CYL,(50))                               01930000
//SORTWK05 DD UNIT=SYSDA,SPACE=(CYL,(50))                               01940000
//SORTWK06 DD UNIT=SYSDA,SPACE=(CYL,(50))                               01950000
//SORTWK07 DD UNIT=SYSDA,SPACE=(CYL,(50))                               01960000
//*==================================================                   01970000
//CTRLFILE DD DSN=PVBDPB.DPB.DPB.FRMTK001.CONTROL,DISP=SHR              01980004
//CHECKBR  DD DSN=PVBDPB.DPB.DPB.FRMTK006.CHECKBR1,DISP=SHR             01990004
//DATAFILE DD DSN=PSPBCM.BCM.BCM.P140.RERPX003.DET01,DISP=SHR           02000003
//BKPFILE1 DD DUMMY                                                     02010000
//BKPFILE2 DD DUMMY                                                     02020000
//*==================================================                   02030000
//STMTINF  DD DSN=PVBBCM.BCM.BCM.PS2100.GENSTMT.DETL,DISP=SHR           02040003
//DRBTIOF  DD DSN=PVBBCM.BCM.BCM.P140.DRSTMT.MCASH.WORK,DISP=SHR        02050003
//CRDTIOF  DD DSN=PVBBCM.BCM.BCM.P140.CRSTMT.MCASH.WORK,DISP=SHR        02060003
//ACCTINF  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       02070003
//STMTOUTD DD DSN=PSPBCM.BCM.BCM.PS3200.GENSTMT.DETL,DISP=SHR           02080003
//XCPTOUTF DD DSN=PSPBCM.BCM.BCM.P140.RERPX004,DISP=SHR                 02090003
/*                                                                      02100000
//STEPBK   EXEC PGM=IDCAMS                                              02100100
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS3200.GENSTMT.DETL,DISP=SHR           02100203
//OUTPUT   DD DSN=PSPBCM.BCM.BCM.PS3200.GSTMT.&DAYDATE.V1.TM2,          02100303
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(CYL,(50,100),RLSE),        02100400
//         DCB=(RECFM=FB,LRECL=3200,BLKSIZE=0)                          02100500
//SYSOUT   DD SYSOUT=X                                                  02100600
//SYSPRINT DD SYSOUT=X                                                  02100700
//SYSIN    DD *                                                         02100800
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 02100900
/*                                                                      02101000
//                                                                      02101100
//                                                                      02101200
//                                                                      02102000
//                                                                      02103000
//                                                                      02104000