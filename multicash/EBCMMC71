//EBCMMC71 JOB EBCMMC71,'EBCMMC71',MSGCLASS=X,CLASS=7,                  00010012
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)                         00020013
//*--------------------------------------------------------------------*00030000
//         EXEC BCMDATE1                                                00031000
//*--------------------------------------------------------------------*00032000
//*    WAIT FOR FILE FROM CONTROL-M JOB                               -*00100011
//*                                                                   -*00121111
//*    CTRL-M => 23.00                                                -*00121211
//*    TABLE : AFT_ISP_ALLD_DAILY_P                                   -*00121311
//*    GROUP : IPS_ALLD_EXTRACT_PPY_TO_HOST                           -*00121411
//*===>FILE  : PSPBCM.IPS.BCM.P140.PPY.STMT.DETAIL                    -*00121511
//*                                                                   -*00121611
//*    CTRL-M.=> 1.30                                                 -*00121711
//*    TABLE : AFT_S1_PROMPTPAY_BCM_DAILY                             -*00121811
//*===>FILE  : PSDBCM.S1.BCM.P140.XFERDTLS                            -*00121911
//*                                                                   -*00122011
//*-------------------------------------------------------------------- 00140000
//STEP01   EXEC PGM=IEFBR14                                             01010000
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL.SORT,              01020008
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01030000
/*                                                                      01040000
//STEP02   EXEC PGM=IEFBR14                                             01050000
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL.SORT,              01060008
//           DISP=(NEW,CATLG,DELETE),                                   01070000
//           DCB=(RECFM=FB,LRECL=1300,BLKSIZE=0,DSORG=PS),              01080000
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01090000
/*                                                                      01100000
//STEP03   EXEC PGM=SORT                                                01130100
//SORTIN   DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL,DISP=SHR           01130208
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL.SORT,DISP=SHR      01140008
//SYSOUT   DD SYSOUT=X                                                  01150000
//SYSIN    DD *                                                         01160000
    SORT   FIELDS=(1,16,A),                                             01170000
           FORMAT=CH,EQUALS                                             01180000
    RECORD TYPE=F,LENGTH=1300                                           01200000
    OPTION  EQUALS                                                      01210000
   END                                                                  01220000
/*                                                                      01230000
//STEP04   EXEC PGM=IDCAMS,REGION=8M                                    01240000
//SYSPRINT DD SYSOUT=*                                                  01250000
//SYSIN    DD *                                                         01260000
   DELETE  PVBBCM.BCM.BCM.PS1300.GENSTMT.DETL -                         01270008
           PURGE                                                        01280000
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.PS1300.GENSTMT.DETL) -          01290008
           CYL(50 50)                                 -                 01300000
           RECSZ(1300 1300)                           -                 01310000
           VOL(JP2165 JP2166)                         -                 01320000
           INDEXED                                    -                 01330000
           KEYS(16 0))                                -                 01340000
   DATA    (NAME(PVBBCM.BCM.BCM.PS1300.GENSTMT.DETL.DATA)) -            01350008
   INDEX   (NAME(PVBBCM.BCM.BCM.PS1300.GENSTMT.DETL.INDEX))             01360008
/*                                                                      01370000
//STEP05   EXEC PGM=IDCAMS                                              01380000
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS1300.GENSTMT.DETL.SORT,DISP=SHR      01390008
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS1300.GENSTMT.DETL,DISP=SHR           01400008
//SYSOUT   DD SYSOUT=X                                                  01410000
//SYSPRINT DD SYSOUT=X                                                  01420000
//SYSIN    DD *                                                         01430000
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 01440000
/*                                                                      01441000
//STEP06   EXEC PGM=IEFBR14                                             01441100
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS855.XFERDTLS.SORT,              01441208
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01441300
/*                                                                      01441400
//STEP07   EXEC PGM=IEFBR14                                             01441500
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS855.XFERDTLS.SORT,              01441608
//           DISP=(NEW,CATLG,DELETE),                                   01441700
//           DCB=(RECFM=FB,LRECL=855,BLKSIZE=0,DSORG=PS),               01441800
//           SPACE=(CYL,(5,2),RLSE),UNIT=3390                           01441900
/*                                                                      01442000
//STEP08   EXEC PGM=SORT                                                01442100
//SORTIN   DD DSN=PSDBCM.S1.BCM.P140.XFERDTLS,DISP=SHR                  01442209
//         DD DSN=PSPBCM.IPS.BCM.P140.PPY.STMT.DETAIL,DISP=SHR          01442309
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.P140.PS855.XFERDTLS.SORT,DISP=SHR      01442509
//SYSOUT   DD SYSOUT=X                                                  01442600
//SYSIN    DD *                                                         01442700
    SORT   FIELDS=(201,10,A,1,40,A),                                    01442800
           FORMAT=CH,EQUALS                                             01442900
    OUTREC  FIELDS=(201,10,2,36,                                        01443002
                    47:SEQNUM,6,ZD,START=000001,INCR=1,1,796,7X)        01443100
    RECORD TYPE=F,LENGTH=855                                            01443200
    OPTION  EQUALS                                                      01443300
   END                                                                  01443400
/*                                                                      01443500
//STEP09   EXEC PGM=IDCAMS,REGION=8M                                    01444100
//SYSPRINT DD SYSOUT=*                                                  01444200
//SYSIN    DD *                                                         01444300
   DELETE  PVBBCM.BCM.BCM.P140.PS855.XFERDTLS -                         01444409
           PURGE                                                        01444500
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.P140.PS855.XFERDTLS) -          01444609
           CYL(50 50)                                 -                 01444700
           RECSZ(855 855)                           -                   01444800
           VOL(JP2165 JP2166)                         -                 01444900
           INDEXED                                    -                 01445000
           KEYS(52 0))                                -                 01445100
   DATA    (NAME(PVBBCM.BCM.BCM.P140.PS855.XFERDTLS.DATA)) -            01445209
   INDEX   (NAME(PVBBCM.BCM.BCM.P140.PS855.XFERDTLS.INDEX))             01445309
/*                                                                      01445400
//STEP10   EXEC PGM=IDCAMS                                              01445500
//INPUT    DD DSN=PSPBCM.BCM.BCM.P140.PS855.XFERDTLS.SORT,DISP=SHR      01445609
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.P140.PS855.XFERDTLS,DISP=SHR           01445709
//SYSOUT   DD SYSOUT=X                                                  01445800
//SYSPRINT DD SYSOUT=X                                                  01445900
//SYSIN    DD *                                                         01446000
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 01446100
/*                                                                      01447000
//STEP11   EXEC PGM=IEFBR14                                             01496300
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL,                   01496409
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01496500
/*                                                                      01497000
//STEP12   EXEC PGM=IEFBR14                                             01500000
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL,                   01510009
//           DISP=(NEW,CATLG,DELETE),                                   01520000
//           DCB=(RECFM=FB,LRECL=2100,BLKSIZE=0,DSORG=PS),              01530000
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01540000
/*                                                                      01550000
//STEP13   EXEC PGM=IEFBR14                                             01560000
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.XFER,             01570009
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01580000
/*                                                                      01590000
//STEP14   EXEC PGM=IEFBR14                                             01600000
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.XFER,             01610009
//           DISP=(NEW,CATLG,DELETE),                                   01620000
//           DCB=(RECFM=FB,LRECL=132,BLKSIZE=0,DSORG=PS),               01630000
//           SPACE=(CYL,(1,5),RLSE),UNIT=3390                           01640000
/*                                                                      01650000
//STEP15   EXEC PGM=STMMCG06                                            01840000
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  01850009
//SYSCOUNT DD SYSOUT=X                                                  01860000
//SYSOUT   DD SYSOUT=X                                                  01870000
//SYSPRINT DD SYSOUT=X                                                  01880000
//*==================================================                   01890000
//ACCPROF  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       01900009
//STMTINF  DD DSN=PVBBCM.BCM.BCM.PS1300.GENSTMT.DETL,DISP=SHR           01910009
//DETLINF  DD DSN=PVBBCM.BCM.BCM.P140.PS855.XFERDTLS,DISP=SHR           01920009
//STMTOUTF DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL,DISP=SHR           01930009
//PRTFOUTF DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.XFER,DISP=SHR     01940009
/*                                                                      02100000
//STEP16  EXEC PGM=IDCAMS                                               02101800
//INPUT    DD DSN=PSDBCM.S1.BCM.P140.XFERDTLS,DISP=SHR                  02102009
//OUTPUT   DD DSN=PSPBCM.S1.BCM.XFERDTLS.&DAYDATE.V1.TM1,               02102109
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(TRK,(900,900),RLSE),       02102200
//         DCB=(RECFM=FB,LRECL=796,BLKSIZE=7960)                        02102300
//SYSOUT   DD SYSOUT=X                                                  02102400
//SYSPRINT DD SYSOUT=X                                                  02102500
//SYSIN    DD *                                                         02102600
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 02102700
   IF   MAXCC EQ 12 THEN SET MAXCC EQ 00                                02102800
/*                                                                      02102900
//STEP17  EXEC PGM=IEFBR14                                              02103000
//FILE002 DD DSN=PSDBCM.S1.BCM.P140.XFERDTLS,                           02103109
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           02103200
/*                                                                      02103300
//STEP18  EXEC PGM=IDCAMS                                               02103404
//INPUT    DD DSN=PSPBCM.IPS.BCM.P140.PPY.STMT.DETAIL,DISP=SHR          02103509
//OUTPUT   DD DSN=PSPBCM.IPS.BCM.PPY.STMT.&DAYDATE.V1.TM1,              02103609
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(TRK,(900,900),RLSE),       02103704
//         DCB=(RECFM=FB,LRECL=796,BLKSIZE=7960)                        02103804
//SYSOUT   DD SYSOUT=X                                                  02103904
//SYSPRINT DD SYSOUT=X                                                  02104004
//SYSIN    DD *                                                         02104104
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 02104204
   IF   MAXCC EQ 12 THEN SET MAXCC EQ 00                                02104304
/*                                                                      02104404
//STEP19  EXEC PGM=IEFBR14                                              02104504
//FILE002 DD DSN=PSPBCM.IPS.BCM.P140.PPY.STMT.DETAIL,                   02104609
//        SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)           02104704
/*                                                                      02104804
//STEPBK   EXEC PGM=IDCAMS                                              02104900
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS2100.GENSTMT.DETL,DISP=SHR           02105009
//OUTPUT   DD DSN=PSPBCM.BCM.BCM.PS2100.GSTMT.&DAYDATE.V1.TM2,          02105109
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(CYL,(50,100),RLSE),        02105200
//         DCB=(RECFM=FB,LRECL=2100,BLKSIZE=0)                          02105300
//SYSOUT   DD SYSOUT=X                                                  02105400
//SYSPRINT DD SYSOUT=X                                                  02105500
//SYSIN    DD *                                                         02105600
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 02105700
/*                                                                      02105800
//                                                                      02105900
//                                                                      02106000
//                                                                      02106110
//                                                                      02106210
//                                                                      02106310