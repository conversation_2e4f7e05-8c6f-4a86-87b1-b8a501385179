//EBCMMC03 JOB EBCMMC03,'EBCMMC03',MSGCLASS=X,CLASS=7,                  00010021
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)                         00020010
//*----------------------------------------------------------------*    00030023
//         EXEC BCMDATE1                                                00040024
//*----------------------------------------------------------------*    00050023
//* RUN AFTER JOB : EBCMERP6                                            00080029
//*                  PSPBCM.FNF.BCM.DIM50000.DESC.&DAYDATE.V1.TM2       00110030
//*----------------------------------------------------------------*    00160029
//*   STEP :                                                            00590010
//*   FOR TRANS-CODE = 'CMZ'                                            00600010
//*       REPLACE SPACE IN FIELD 'CHQ NO. 7 DIGIT' BY '0'               00610010
//*       EX.                                                           00620010
//*          '      1' ==> '0000001'                                    00630010
//*          '     12' ==> '0000012'                                    00640010
//*          '    123' ==> '0000123'                                    00650010
//*          '   1234' ==> '0001234'                                    00660010
//*          '  12345' ==> '0012345'                                    00670010
//*          ' 123456' ==> '0123456'                                    00680010
//*          '1234567' ==> '1234567'                                    00690010
//*----------------------------------------------------------------*    00700010
//STEP01   EXEC PGM=IEFBR14                                             00710013
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS175.STMTINTB.DESC.CMZ,               00720028
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00730010
/*                                                                      00740010
//STEP02   EXEC PGM=IEFBR14                                             00750013
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS175.STMTINTB.DESC.CMZ,               00760028
//           DISP=(NEW,CATLG,DELETE),                                   00770010
//           DCB=(RECFM=FB,LRECL=175,BLKSIZE=0,DSORG=PS),               00780019
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        00790010
/*                                                                      00800010
//STEP03   EXEC PGM=SORT                                                00810013
//SORTIN   DD DSN=PSPBCM.FNF.BCM.DIM50000.DESC.&DAYDATE.V1.TM2,DISP=SHR 00821028
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS175.STMTINTB.DESC.CMZ,DISP=SHR       00830028
//SYSOUT   DD SYSOUT=X                                                  00840010
//SYSIN    DD *                                                         00850010
   OPTION COPY                                                          00860010
       INREC IFTHEN=(WHEN=(66,14,CH,EQ,C'CMZ 0000      '),              00870010
                     OVERLAY=(66:C'CMZ 0000000000',121:C'0000000')),    00880031
             IFTHEN=(WHEN=(66,13,CH,EQ,C'CMZ 0000     '),               00890010
                     OVERLAY=(66:C'CMZ 000000000',121:C'000000')),      00900031
             IFTHEN=(WHEN=(66,12,CH,EQ,C'CMZ 0000    '),                00910010
                     OVERLAY=(66:C'CMZ 00000000',121:C'00000')),        00920031
             IFTHEN=(WHEN=(66,11,CH,EQ,C'CMZ 0000   '),                 00930010
                     OVERLAY=(66:C'CMZ 0000000',121:C'0000')),          00940031
             IFTHEN=(WHEN=(66,10,CH,EQ,C'CMZ 0000  '),                  00950010
                     OVERLAY=(66:C'CMZ 000000',121:C'000')),            00960031
             IFTHEN=(WHEN=(66,9,CH,EQ,C'CMZ 0000 '),                    00970010
                     OVERLAY=(66:C'CMZ 00000',121:C'00'))               00980031
   END                                                                  00990010
/*                                                                      00991015
//STEP04   EXEC PGM=IEFBR14                                             01000015
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS175.STMTINTB.DESC.SORT,              01010028
//         SPACE=(CYL,(30,20),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)        01020015
/*                                                                      01030015
//STEP05   EXEC PGM=IEFBR14                                             01040015
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS175.STMTINTB.DESC.SORT,              01050028
//           DISP=(NEW,CATLG,DELETE),                                   01060015
//           DCB=(RECFM=FB,LRECL=175,BLKSIZE=0,DSORG=PS),               01070019
//           SPACE=(CYL,(30,20),RLSE),UNIT=3390                         01080015
/*                                                                      01090015
//STEP06   EXEC PGM=SORT                                                01090117
//SORTIN   DD DSN=PSPBCM.BCM.BCM.PS175.STMTINTB.DESC.CMZ,DISP=SHR       01090228
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS175.STMTINTB.DESC.SORT,DISP=SHR      01090328
//SYSOUT   DD SYSOUT=X                                                  01090416
//SYSIN    DD *                                                         01090516
    SORT   FIELDS=(7,10,A,113,6,A),                                     01090616
           FORMAT=CH,EQUALS                                             01090716
    RECORD TYPE=F,LENGTH=175                                            01090819
    OPTION  EQUALS                                                      01090916
   END                                                                  01091016
/*                                                                      01091116
//STEP07   EXEC PGM=IEFBR14                                             01091217
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS175.STMTINTB.DESC,              01092028
//         SPACE=(CYL,(30,20),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)        01093016
/*                                                                      01094016
//STEP08   EXEC PGM=IEFBR14                                             01095017
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS175.STMTINTB.DESC,              01096028
//           DISP=(NEW,CATLG,DELETE),                                   01097016
//           DCB=(RECFM=FB,LRECL=175,BLKSIZE=0,DSORG=PS),               01098019
//           SPACE=(CYL,(30,20),RLSE),UNIT=3390                         01099016
/*                                                                      01099116
//*----------------------------------------------------------------*    01100015
//STEP09  EXEC PGM=STMBDD07                                             01110017
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  01120028
//STMTIN   DD DSN=PSPBCM.BCM.BCM.PS175.STMTINTB.DESC.SORT,DISP=SHR      01130028
//ACCTIN   DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       01140028
//STMTOUT  DD DSN=PSPBCM.BCM.BCM.P140.PS175.STMTINTB.DESC,DISP=SHR      01150028
/*                                                                      01160015
//                                                                      01170027
//                                                                      01180027
//                                                                      01190027
//                                                                      01200027