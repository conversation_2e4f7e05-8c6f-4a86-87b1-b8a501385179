//EBCMMC04 JOB EBCMMC04,'EBCMMC04',MSGCLASS=X,CLASS=7,                  ********
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)                         ********
//*----------------------------------------------------------------*    ********
//*--  MERGE INTERBANK STMT. & HISTORICAL STMT WITH BILL PAYMENT --*    ********
//*----------------------------------------------------------------*    ********
//STEP01   EXEC PGM=IEFBR14                                             ********
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS525.INTBDESC.STMTBPDT,               ********
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          ********
/*                                                                      ********
//STEP02   EXEC PGM=IEFBR14                                             ********
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS525.INTBDESC.STMTBPDT,               ********
//           DISP=(NEW,CATLG,DELETE),                                   ********
//           DCB=(RECFM=FB,LRECL=525,BLKSIZE=0,DSORG=PS),               ********
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        ********
/*                                                                      ********
//STEP03   EXEC PGM=STMMCG01                                            00810008
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  00810123
//SYSCOUNT DD SYSOUT=X                                                  00811004
//SYSOUT   DD SYSOUT=X                                                  00812004
//SYSPRINT DD SYSOUT=X                                                  00813004
//INTBINF  DD DSN=PSPBCM.BCM.BCM.P140.PS175.STMTINTB.DESC,DISP=SHR      00814023
//STMTINF  DD DSN=PVBBCM.BCM.BCM.P140.PS350.STMTBPDT,DISP=SHR           00815023
//STMTOUTF DD DSN=PSPBCM.BCM.BCM.PS525.INTBDESC.STMTBPDT,DISP=SHR       00816023
//XCPTOUTF DD SYSOUT=X                                                  00819105
/*                                                                      00819502
//STEP04   EXEC PGM=IEFBR14                                             00819621
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS550.INTBDESC.STMTBPDT.SORT,          00819723
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          00819809
/*                                                                      00819909
//STEP05   EXEC PGM=IEFBR14                                             00820021
//FILE002  DD DSN=PSPBCM.BCM.BCM.PS550.INTBDESC.STMTBPDT.SORT,          00820123
//           DISP=(NEW,CATLG,DELETE),                                   00820209
//           DCB=(RECFM=FB,LRECL=550,BLKSIZE=0,DSORG=PS),               00820309
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        00820409
/*                                                                      00820509
//STEP06   EXEC PGM=SORT                                                00820621
//SORTIN   DD DSN=PSPBCM.BCM.BCM.PS525.INTBDESC.STMTBPDT,DISP=SHR       00820723
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS550.INTBDESC.STMTBPDT.SORT,DISP=SHR  00820823
//SYSOUT   DD SYSOUT=X                                                  00820910
//SYSIN    DD *                                                         00821010
    SORT   FIELDS=(7,10,A,113,6,A),                                     00821110
           FORMAT=CH,EQUALS                                             00821210
    OUTREC  FIELDS=(7,10,113,6,1,23,28,4,26,2,24,2,32,494,9X)           00821310
    RECORD TYPE=F,LENGTH=550                                            00821410
    OPTION  EQUALS                                                      00821510
   END                                                                  00821610
/*                                                                      00821710
//STEP07   EXEC PGM=IDCAMS,REGION=8M                                    00821821
//SYSPRINT DD SYSOUT=*                                                  00821911
//SYSIN    DD *                                                         00822011
   DELETE  PVBBCM.BCM.BCM.PS550.INTBDES.STMTBPDT -                      00822123
           PURGE                                                        00822211
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.PS550.INTBDES.STMTBPDT) -       00822323
           CYL(50 50)                                 -                 00822411
           RECSZ(550 550)                             -                 00822511
           VOL(JP2165 JP2166)                         -                 00822611
           INDEXED                                    -                 00822711
           KEYS(16 0))                                -                 00822811
   DATA    (NAME(PVBBCM.BCM.BCM.PS550.INTBDES.STMTBPDT.DATA)) -         00822923
   INDEX   (NAME(PVBBCM.BCM.BCM.PS550.INTBDES.STMTBPDT.INDEX))          00823023
/*                                                                      00823111
//STEP08   EXEC PGM=IDCAMS                                              00823321
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS550.INTBDESC.STMTBPDT.SORT,DISP=SHR  00823423
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS550.INTBDES.STMTBPDT,DISP=SHR        00823523
//SYSOUT   DD SYSOUT=X                                                  00823612
//SYSPRINT DD SYSOUT=X                                                  00823712
//SYSIN    DD *                                                         00823812
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 00823912
/*                                                                      00824012
//                                                                      00824112
//                                                                      00824212
//                                                                      00824312
//                                                                      00824402
//                                                                      00824505