//EBCMMC02 JOB EBCMMC02,'EBCMMC02',MSGCLASS=X,CLASS=7,
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)
//*----------------------------------------------------------------*
//* RUN AFTER JOB : EBCMST06
//*                  PSDFNF.IM.BM.P140.IMBPSTMT.SCB
//*                  PSPBCM.FNF.BCM.P140.STMTBPDT.CUR
//*                  PSPBCM.CBS.BCM.STMTBPDT.CUR.&DAYDATE.V1.TM2
//* RUN AFTER JOB : EBCMST07
//*                  PSDFNF.ST.BM.P140.SVBPST.DST6900.SCB
//*                  PSPBCM.FNF.BCM.P140.STMTBPDT.SAV
//*                  PSPBCM.CBS.BCM.STMTBPDT.SAV.&DAYDATE.V1.TM2
//*----------------------------------------------------------------*
//         EXEC BCMDATE1
//*----------------------------------------------------------------*
//JOBLIB   DD DSN=CEE.SCEERUN,DISP=SHR
//STEP01   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS350.STMTBPDT.SAV.SORT,
//         SPACE=(CYL,(30,20),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)
/*
//STEP02   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS350.STMTBPDT.SAV.SORT,
//           DISP=(NEW,CATLG,DELETE),
//           DCB=(RECFM=FB,LRECL=350,BLKSIZE=0,DSORG=PS),
//           SPACE=(CYL,(30,20),RLSE),UNIT=3390
/*
//STEP03  EXEC PGM=SORT
//SORTIN   DD DSN=PSPBCM.CBS.BCM.STMTBPDT.SAV.&DAYDATE.V1.TM2,DISP=SHR
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS350.STMTBPDT.SAV.SORT,DISP=SHR
//SYSOUT   DD SYSOUT=X
//SYSIN    DD *
    SORT   FIELDS=(8,11,A,1,2,A,4,3,A,20,10,A,31,9,A,41,10,A,
                   52,10,A,63,5,A),
           FORMAT=CH,EQUALS
    RECORD TYPE=F,LENGTH=350
    OPTION  EQUALS
   END
/*
//STEP04   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS350.STMTBPDT.CUR.SORT,
//         SPACE=(CYL,(30,20),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)
/*
//STEP05   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS350.STMTBPDT.CUR.SORT,
//           DISP=(NEW,CATLG,DELETE),
//           DCB=(RECFM=FB,LRECL=350,BLKSIZE=0,DSORG=PS),
//           SPACE=(CYL,(30,20),RLSE),UNIT=3390
/*
//STEP06  EXEC PGM=SORT
//SORTIN   DD DSN=PSPBCM.CBS.BCM.STMTBPDT.CUR.&DAYDATE.V1.TM2,DISP=SHR
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.PS350.STMTBPDT.CUR.SORT,DISP=SHR
//SYSOUT   DD SYSOUT=X
//SYSIN    DD *
    SORT   FIELDS=(8,11,A,1,2,A,4,3,A,20,10,A,31,9,A,41,10,A,
                   52,10,A,63,5,A),
           FORMAT=CH,EQUALS
    RECORD TYPE=F,LENGTH=350
    OPTION  EQUALS
   END
/*
//STEP07   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.SAV,
//         SPACE=(CYL,(30,20),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)
/*
//STEP08   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.SAV,
//           DISP=(NEW,CATLG,DELETE),
//           DCB=(RECFM=FB,LRECL=350,BLKSIZE=0,DSORG=PS),
//           SPACE=(CYL,(30,20),RLSE),UNIT=3390
/*
//*----------------------------------------------------------------*
//STEP09  EXEC PGM=STMBDD06
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR
//STMTIN   DD DSN=PSPBCM.BCM.BCM.PS350.STMTBPDT.SAV.SORT,DISP=SHR       00670036
//ACCTIN   DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       00670036
//STMTOUT  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.SAV,DISP=SHR
/*
//STEP10   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.CUR,
//         SPACE=(CYL,(30,20),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)
/*
//STEP11   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.CUR,
//           DISP=(NEW,CATLG,DELETE),
//           DCB=(RECFM=FB,LRECL=350,BLKSIZE=0,DSORG=PS),
//           SPACE=(CYL,(30,20),RLSE),UNIT=3390
/*
//*----------------------------------------------------------------*
//STEP12  EXEC PGM=STMBDD06
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR
//STMTIN   DD DSN=PSPBCM.BCM.BCM.PS350.STMTBPDT.CUR.SORT,DISP=SHR       00670036
//ACCTIN   DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       00670036
//STMTOUT  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.CUR,DISP=SHR
/*
//STEP13   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.ALL,
//         SPACE=(CYL,(60,30),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)
/*
//STEP14   EXEC PGM=IEFBR14
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.ALL,
//           DISP=(NEW,CATLG,DELETE),
//           DCB=(RECFM=FB,LRECL=350,BLKSIZE=0,DSORG=PS),
//           SPACE=(CYL,(60,30),RLSE),UNIT=3390
/*
//STEP15  EXEC PGM=SORT
//SORTIN   DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.CUR,DISP=SHR
//         DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.SAV,DISP=SHR
//SORTOUT  DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.ALL,DISP=SHR
//SYSOUT   DD SYSOUT=X
//SYSIN    DD *
    SORT   FIELDS=(1,68,A),
           FORMAT=CH,EQUALS
    RECORD TYPE=F,LENGTH=350
    OPTION  EQUALS
   END
/*
//STEP16   EXEC PGM=IDCAMS,REGION=8M
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
   DELETE  PVBBCM.BCM.BCM.P140.PS350.STMTBPDT -
           PURGE
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.P140.PS350.STMTBPDT) -
           CYL(50 50)                                 -
           RECSZ(350 350)                             -
           VOL(JP2165 JP2166)                         -
           INDEXED                                    -
           KEYS(16 333))                                -
   DATA    (NAME(PVBBCM.BCM.BCM.P140.PS350.STMTBPDT.DATA)) -
   INDEX   (NAME(PVBBCM.BCM.BCM.P140.PS350.STMTBPDT.INDEX))
/*
//STEP17   EXEC PGM=IDCAMS
//INPUT    DD DSN=PSPBCM.BCM.BCM.P140.PS350.STMTBPDT.ALL,DISP=SHR
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.P140.PS350.STMTBPDT,DISP=SHR
//SYSOUT   DD SYSOUT=X
//SYSPRINT DD SYSOUT=X
//SYSIN    DD *
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)
/*
//
//
//
//
//
//
//
//
//
//
//
//