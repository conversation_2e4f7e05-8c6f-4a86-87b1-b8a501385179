//STMMCG06 JOB STMMCG06,'STMMCG06',MSGCLASS=X,CLASS=A,
//     NOTIFY=&SYSUID
//STEP01   EXEC IGYWCL
//SYSPRINT DD   SYSOUT=X
//<PERSON><PERSON>SI<PERSON>    DD   *
      *------------------------*
       IDENTIFICATION DIVISION.
      *------------------------*
       PROGRAM-ID.   STMMCG06.
      *----------------------------------------------------------------*
      * PROGRAM NAME: STMMCG06
      * AUTHOR      : ITTICHOTE CH.
      * STARTED DATE: 31/01/2019
      *----------------------------------------------------------------*
      * DESCRIPTION : THIS PROGRAM WILL READ STMT FILE AND ACCT PROFILE
      *               AND DETAIL STMT FILE FOR FILLERING ONLY ACCOUNT
      *               THEN WRITE THE OUTPUT FILE INCLUDE DETAIL.
      *               GENERATE RFT DETAIL.
      *------------------------------------------------------------*
      * CB62020008  : 16/05/19 - Modify Program for support promptPay
      *                          transaction from IPS System
      *------------------------------------------------------------*
       ENVIRONMENT DIVISION.
      *---------------------*
       CONFIGURATION SECTION.
       SOURCE-COMPUTER. IBM-4341.
       OBJECT-COMPUTER. IBM-4341.
       SPECIAL-NAMES.   C01 IS  NEXT-PAGE.
      *----------------------*
       INPUT-OUTPUT SECTION.
      *----------------------*
       FILE-CONTROL.

      *** READ ACCOUNT PROFILE (PS INPUT)
            SELECT ACCT-IN-FL ASSIGN    TO  ACCPROF
                   FILE STATUS          IS  ACC-PROF-STAT.

      *** READ STMT FILE (VSAM INPUT)
            SELECT STMT-IN-FL  ASSIGN   TO  STMTINF
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  STMT-CNTL-KEY
                   FILE STATUS          IS  STMT-IN-STAT.

      *** READ DETAIL FILE (VSAM INPUT)
            SELECT DETL-IO-FL  ASSIGN   TO  DETLINF
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  DETL-CNTL-KEY
                   FILE STATUS          IS  DETL-IO-STAT.

      *** WRITE STMT FILE (PS OUTPUT)
            SELECT STMT-OUT-FL ASSIGN   TO  STMTOUTF
                   FILE STATUS          IS  STMT-OUT-STAT.

      *** WRITE PRTF FILE (PS OUTPUT)
            SELECT PRTF-OUT-FL ASSIGN   TO  PRTFOUTF
                   FILE STATUS          IS  PRTF-OUT-STAT.

      *--------------*
       DATA DIVISION.
      *--------------*
       FILE SECTION.

       FD ACCT-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   200 CHARACTERS
           DATA   RECORD     IS ACCT-IN-REC.
       01 ACCT-IN-REC.
          05 FILLER                 PIC  X(16).
          05 ACCT-NO                PIC  X(10).
          05 FILLER                 PIC  X(03).
          05 ACCT-NAME              PIC  X(50).
          05 FILLER                 PIC  X(71).
          05 ACCT-MCASH-FLG         PIC  X(01).
          05 ACCT-PAYMENT-FLG       PIC  X(01).
          05 ACCT-LCC-FLG           PIC  X(01).
          05 ACCT-BPAY-FLG          PIC  X(01).
          05 ACCT-EBPP-FLG          PIC  X(01).
          05 ACCT-RFT-FLG           PIC  X(01).
          05 FILLER                 PIC  X(44).

       FD STMT-IN-FL                                                    00910000
           RECORD CONTAINS   1300 CHARACTERS                            00920000
           DATA   RECORD     IS STMT-IN-REC.                            00930000
       01 STMT-IN-REC.                                                  00940000
          05 STMT-CNTL-KEY.                                             00950000
             10 STMT-IN-ACCT-KEY       PIC  X(10).                      ********
             10 STMT-IN-SEQ-KEY        PIC  X(06).                      ********
          05 STMT-DETL-REC.                                             ********
             10 STMT-IN-REC-TYP        PIC  X(01).                      ********
             10 FILLER                 PIC  X(05).                      ********
             10 STMT-IN-ACCT-NO        PIC  X(10).                      ********
             10 STMT-IN-BANK-ID        PIC  X(03).                      ********
             10 STMT-IN-BRNO           PIC  X(04).                      ********
             10 STMT-IN-TDATE.                                          ********
                15 STMT-IN-TDATE-YYYY  PIC  X(04).                      ********
                15 STMT-IN-TDATE-MM    PIC  X(02).                      ********
                15 STMT-IN-TDATE-DD    PIC  X(02).                      ********
             10 STMT-IN-BAL            PIC  9(14)V99.                   ********
             10 STMT-IN-BAL-SIGN       PIC  X(01).                      ********
             10 STMT-IN-AMT            PIC  9(13)V99.                   ********
             10 STMT-IN-DC-CODE        PIC  X(02).                      ********
             10 STMT-IN-TCODE          PIC  X(04).                      ********
             10 STMT-IN-TERM           PIC  X(04).                      ********
             10 STMT-IN-CHQNO          PIC  X(07).                      01150000
             10 STMT-IN-REF-NUM        PIC  X(25).                      01160000
             10 FILLER                 PIC  X(02).                      01170000
             10 STMT-IN-GROUP-ID       PIC  X(05).                      01180000
             10 STMT-IN-SERIAL-NO      PIC  X(06).                      01190000
             10 STMT-IN-CHQNO-10       PIC  X(10).                      01200000
             10 STMT-IN-DESC-40        PIC  X(40).                      01210000
             10 STMT-IN-SYMBOL         PIC  X(03).                      01230000
             10 STMT-IN-CHANNEL        PIC  X(04).                      01240000
          05 STMT-DETL-REC2            PIC  X(1109).                    01251000

       FD DETL-IO-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   855 CHARACTERS
           DATA   RECORD     IS  DETL-IO-REC.
       01 DETL-IO-REC.
          05 DETL-CNTL-KEY.
             07 CNTL-RFT-ACC-NO          PIC X(10).
             07 CNTL-RFT-DESC            PIC X(36).
             07 CNTL-RFT-SERIAL-NO       PIC X(06).
          05 DETL-RFT-REC.
             07 RFT-STMT-DESC            PIC X(40).
             07 RFT-COMP-ID              PIC X(15).
             07 RFT-COMP-NAME            PIC X(35).
             07 RFT-FILE-REF             PIC X(32).
             07 RFT-BATCH-REF            PIC X(35).
             07 RFT-PROD-CODE            PIC X(03).
             07 RFT-SUB-PROD             PIC X(08).
             07 RFT-TRANS-REF            PIC X(32).
             07 RFT-DEBT-ACC             PIC X(25).
             07 RFT-VALUE-DATE           PIC X(08).
     *****   07 RFT-PAY-AMT              PIC 9(15).
             07 RFT-PAY-AMT              PIC 9(13)V99.
             07 RFT-BENE-ACC             PIC X(25).
             07 RFT-BENE-NAME            PIC X(100).
             07 RFT-BENE-BANK            PIC X(03).
             07 RFT-BENE-BANK-NAME       PIC X(35).
             07 RFT-BENE-BR              PIC X(04).
             07 RFT-BENE-BR-NAME         PIC X(35).
             07 RFT-PP-CYCLE             PIC X(01).
             07 RFT-PMT-TYPE             PIC X(03).
             07 RFT-TAX-ID               PIC X(13).
             07 RFT-MOBILE-NO            PIC X(10).
             07 RFT-CUST-REF             PIC X(20).
             07 RFT-TRANS-REM            PIC X(32).
             07 RFT-PROCESS-STAT         PIC X(01).
             07 RFT-PROCESS-REM          PIC X(100).
     *****   07 RFT-NET-PAY-AMT          PIC 9(15).
             07 RFT-NET-PAY-AMT          PIC 9(13)V99.
             07 RFT-WHT-SEQ-NO           PIC X(14).
             07 RFT-PAYEE-TAX-ID         PIC X(10).
             07 RFT-BENE-TAX-ID          PIC X(15).
             07 RFT-REMARKS              PIC X(50).
             07 RFT-PP-ON-OS             PIC X(01).
             07 RFT-BENE-CHARGE          PIC X(02).
             07 RFT-FEE-DR-ACC           PIC X(25).
             07 RFT-DR-CCY               PIC X(03).
             07 RFT-DR-AMT               PIC X(16).
             07 RFT-NO-OF-CR             PIC X(06).
             07 RFT-CR-CCY               PIC X(03).
             07 RFT-CR-SEQ-NO            PIC X(06).
          05 FILLER                      PIC X(06).
          05 RFT-REC-PROCESS             PIC X(01).

       FD STMT-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS  2100 CHARACTERS
           DATA   RECORD     IS STMT-OUT-REC.
       01 STMT-OUT-REC.
          05 SOUT-CNTL-KEY.
             07 SOUT-ACCT-KEY   PIC X(10).
             07 SOUT-SEQ-KEY    PIC X(06).
          05 SOUT-IN-REC-TYP           PIC  X(01).
          05 FILLER                    PIC  X(05).
          05 SOUT-IN-ACCT-NO           PIC  X(10).
          05 FILLER                    PIC  X(15).
          05 SOUT-IN-BAL               PIC  9(14)V99.
          05 SOUT-IN-BAL-SIGN          PIC  X(01).
          05 SOUT-IN-AMT               PIC  9(13)V99.
          05 SOUT-IN-DC-CODE           PIC  X(02).
          05 FILLER                    PIC  X(47).
          05 SOUT-IN-SEQ-NO            PIC  9(06).
          05 SOUT-IN-CHG-NO10          PIC  9(10).
          05 SOUT-IN-DESC40            PIC  X(40).
          05 SOUT-IN-SYMBOL            PIC  X(03).
          05 SOUT-IN-CHANNEL           PIC  X(04).
      *   05 SOUT-STMTBPDT             PIC  X(350).
          05 SOUT-STMTBPDT.
             07 SOUT-BANK-CD             PIC X(02).
             07 FILLER                   PIC X(01).
             07 SOUT-CURR-CD             PIC X(03).
             07 FILLER                   PIC X(01).
             07 SOUT-ACCT                PIC 9(11).
             07 FILLER                   PIC X(01).
             07 SOUT-R-DATE              PIC X(10).
             07 FILLER                   PIC X(01).
             07 SOUT-SEQ-NO              PIC 9(09).
             07 FILLER                   PIC X(01).
             07 SOUT-D-TRAN              PIC X(10).
             07 FILLER                   PIC X(01).
             07 SOUT-D-DATE              PIC X(10).
             07 FILLER                   PIC X(01).
             07 SOUT-D-TIME              PIC X(05).
             07 FILLER                   PIC X(01).
             07 SOUT-T-CODE              PIC X(03).
             07 FILLER                   PIC X(01).
             07 SOUT-CHANNEL             PIC X(04).
             07 FILLER                   PIC X(01).
             07 SOUT-TR                  PIC X(02).
             07 FILLER                   PIC X(01).
             07 SOUT-BR                  PIC X(04).
             07 FILLER                   PIC X(01).
             07 SOUT-TERMINAL            PIC X(05).
             07 FILLER                   PIC X(01).
             07 SOUT-CHQNO               PIC X(08).
             07 FILLER                   PIC X(01).
             07 SOUT-AMT-S               PIC X(01).
             07 FILLER                   PIC X(01).
             07 SOUT-AMT                 PIC 9999999999999.99.
             07 FILLER                   PIC X(01).
             07 SOUT-BAL                 PIC -9999999999999.99.
             07 FILLER                   PIC X(01).
             07 SOUT-DESC                PIC X(40).
             07 FILLER                   PIC X(23).
             07 FILLER                   PIC X(01).
             07 SOUT-BR-OF-AC            PIC X(04).
             07 FILLER                   PIC X(01).
             07 SOUT-ACC-TYP             PIC X(02).
             07 FILLER                   PIC X(01).
             07 SOUT-CUST-NME            PIC X(50).
             07 FILLER                   PIC X(01).
             07 SOUT-REF1                PIC X(20).
             07 FILLER                   PIC X(01).
             07 SOUT-REF2                PIC X(20).
             07 FILLER                   PIC X(01).
             07 SOUT-STATUS-MONEY        PIC X(03).
             07 FILLER                   PIC X(01).
             07 SOUT-CH-CODE             PIC X(04).
             07 FILLER                   PIC X(01).
             07 SOUT-AVL-BAL             PIC X(17).
             07 FILLER                   PIC X(01).
             07 FILLER                   PIC X(14).
             07 SOUT-AC-SEQ-NO           PIC 9(06).
             07 FILLER                   PIC X(01).
          05 FILLER                    PIC  X(09).
          05 SOUT-EBPP-DETL            PIC  X(150).
          05 OUT-BC-DETL-REC           PIC  X(600).
     **** 05 OUT-RFT-DETL-REC          PIC  X(796).
          05 OUT-RFT-DETL-REC.
             07 O-RFT-STMT-DESC            PIC X(40).
             07 O-RFT-COMP-ID              PIC X(15).
             07 O-RFT-COMP-NAME            PIC X(35).
             07 O-RFT-FILE-REF             PIC X(32).
             07 O-RFT-BATCH-REF            PIC X(35).
             07 O-RFT-PROD-CODE            PIC X(03).
             07 O-RFT-SUB-PROD             PIC X(08).
             07 O-RFT-TRANS-REF            PIC X(32).
             07 O-RFT-DEBT-ACC             PIC X(25).
             07 O-RFT-VALUE-DATE           PIC X(08).
     *****   07 O-RFT-PAY-AMT              PIC 9(15).
             07 O-RFT-PAY-AMT              PIC 9(13)V99.
             07 O-RFT-BENE-ACC             PIC X(25).
             07 O-RFT-BENE-NME             PIC X(100).
             07 O-RFT-BENE-BNK             PIC X(03).
             07 O-RFT-BENE-BNK-NME         PIC X(35).
             07 O-RFT-BENE-BR              PIC X(04).
             07 O-RFT-BENE-BR-NME          PIC X(35).
             07 O-RFT-PP-CYCLE             PIC X(01).
             07 O-RFT-PMT-TYPE             PIC X(03).
             07 O-RFT-TAX-ID               PIC X(13).
             07 O-RFT-MOBILE-NO            PIC X(10).
             07 O-RFT-CUST-REF             PIC X(20).
             07 O-RFT-TRANS-REM            PIC X(32).
             07 O-RFT-PROCESS-STAT         PIC X(01).
             07 O-RFT-PROCESS-REM          PIC X(100).
     *****   07 O-RFT-NET-PAY-AMT          PIC 9(15).
             07 O-RFT-NET-PAY-AMT          PIC 9(13)V99.
             07 O-RFT-WHT-SEQ-NO           PIC X(14).
             07 O-RFT-PAYEE-TAX-ID         PIC X(10).
             07 O-RFT-BENE-TAX-ID          PIC X(15).
             07 O-RFT-REMARKS              PIC X(50).
             07 O-RFT-PP-ON-OS             PIC X(01).
             07 O-RFT-BENE-CHARGE          PIC X(02).
             07 O-RFT-FEE-DR-ACC           PIC X(25).
             07 O-RFT-DR-CCY               PIC X(03).
             07 O-RFT-DR-AMT               PIC X(16).
             07 O-RFT-NO-OF-CR             PIC X(06).
             07 O-RFT-CR-CCY               PIC X(03).
             07 O-RFT-CR-SEQ-NO            PIC X(06).
          05 FILLER                    PIC  X(04).

       FD PRTF-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   132 CHARACTERS
           DATA   RECORD     IS PRTF-OUT-REC.
       01 PRTF-OUT-REC.
          05 FILLER             PIC X(132).

      *------------------------*
       WORKING-STORAGE SECTION.
      *------------------------*
       77 SW-OPENINFL                  PIC X(01)   VALUE 'Y'.
          88 ERR-OPENINFL                          VALUE 'N'.
          88 SUCCESS-OPENINFL                      VALUE 'Y'.

       77 SW-ACCTINF                   PIC X(01)   VALUE 'N'.
          88 EOF-ACCTINF                           VALUE 'Y'.
          88 NOT-EOF-ACCTINF                       VALUE 'N'.
       77 SW-STMTINF                   PIC X(01)   VALUE 'N'.
          88 EOF-STMTINF                           VALUE 'Y'.
          88 NOT-EOF-STMTINF                       VALUE 'N'.

       77 SW-ACCT-RFT-FLG              PIC X(01)   VALUE 'N'.
          88 GEN-RFT-STMTINF                       VALUE 'Y'.
          88 NOT-GEN-RFT-STMTINF                   VALUE 'N'.

       77 SW-STMTINF-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-STMTINF                         VALUE 'Y'.
          88 NOT-FOUND-STMTINF                     VALUE 'N'.
       77 SW-STMTINF-CHANGE            PIC X(01)   VALUE 'N'.
          88 CHANGE-STMTINF                        VALUE 'Y'.
          88 NOT-CHANGE-STMTINF                    VALUE 'N'.

       77 SW-DETLINF                   PIC X(01)   VALUE 'N'.
          88 EOF-DETLINF                           VALUE 'Y'.
          88 NOT-EOF-DETLINF                       VALUE 'N'.
       77 SW-DETLINF-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-DETLINF                         VALUE 'Y'.
          88 NOT-FOUND-DETLINF                     VALUE 'N'.
       77 SW-DETLINF-CHANGE            PIC X(01)   VALUE 'N'.
          88 CHANGE-DETLINF                        VALUE 'Y'.
          88 NOT-CHANGE-DETLINF                    VALUE 'N'.

       77 SW-FOUND-RFT-STMT            PIC X(01)   VALUE 'N'.
          88 FOUND-RFT-STMT                        VALUE 'Y'.
          88 NOT-FOUND-RFT-STMT                    VALUE 'N'.

      *>>CB62020008
      *77 SW-TYPE-TRANS                PIC X(01)   VALUE 'S'.
       77 SW-TYPE-TRANS                PIC X(01)   VALUE 'X'.
      *<<CB62020008
          88 SINGLE-TRANS                          VALUE 'S'.
          88 BLULK-TRANS                           VALUE 'B'.

       77 SW-GEN-DETAIL                PIC X(01)   VALUE 'N'.
          88 GEN-DETAIL                            VALUE 'Y'.
          88 NOT-GEN-DETAIL                        VALUE 'N'.

       01  WK-FILE-STATUS.
           03  ACC-PROF-STAT       PIC  X(02).
           03  STMT-IN-STAT        PIC  X(02).
           03  DETL-IO-STAT        PIC  X(02).
           03  STMT-OUT-STAT       PIC  X(02).
           03  PRTF-OUT-STAT       PIC  X(02).
       01  WK-DATE-X               PIC  9(08).
       01  WK-DATE-Y.
           03  WK-Y4               PIC  9(04).
           03  WK-MM               PIC  9(02).
           03  WK-DD               PIC  9(02).

       01  WK-INT-DATE-X           PIC  9(08).
       01  WK-INT-DATE-Y.
           03  FILLER              PIC  9(02) VALUE ZEROS.
           03  WK-INT-D6           PIC  9(06).

       01  WK-ORG-REC-IN-AMT       PIC  S9(13)V99 VALUE ZEROS.
       01  WK-NEW-SEQ-NO           PIC  9(06) VALUE ZEROS.
       01  WK-NEW-STMT-IN-BAL      PIC  S9(14)V99 VALUE ZEROS.
       01  WK-STMT-IN-AMT          PIC  S9(13)V99 VALUE ZEROS.
       01  CNV-STMT-IN-AMT         PIC  -99999999999999.99.
       01  N-STMT-DESC-LEN         PIC 9(05) VALUE ZEROES.
       01  THREE-SPACE             PIC X(03) VALUE '   '.

       01 TAIL-IN-REC.
          05 TAIL-CNTL-KEY.
             07 TAIL-IN-ACCT-KEY   PIC X(10).
             07 TAIL-IN-SEQ-KEY    PIC 9(06).
          05 TAIL-IN-REC-TYP           PIC  X(01).
          05 FILLER                    PIC  X(05).
          05 TAIL-IN-ACCT-NO           PIC  X(10).
          05 FILLER                    PIC  X(15).
          05 TAIL-IN-BAL               PIC  9(14)V99.
          05 TAIL-IN-BAL-SIGN          PIC  X(01).
          05 TAIL-IN-AMT               PIC  9(14)V99.
          05 TAIL-IN-DC-CODE           PIC  X(01).
          05 TAIL-ALL-SEQ-NO           PIC  9(06).
          05 FILLER                    PIC  X(41).
          05 TAIL-IN-SEQ-NO            PIC  9(06).
          05 TAIL-IN-CHG-NO10          PIC  9(10).
          05 TAIL-IN-DESC40            PIC  X(40).
          05 TAIL-IN-SYMBOL            PIC  X(03).
          05 TAIL-IN-CHANNEL           PIC  X(04).
          05 FILLER                    PIC  X(350).

      *---------------------------------------------------------*
       PROCEDURE DIVISION.
      *-------------------*
       0000-MAIN-PROCESS.

           PERFORM  1000-INITIAL-RTN   THRU  1000-EXIT.
           PERFORM  2000-MAIN-PROCESS  THRU  2000-EXIT.
           PERFORM  9999-CLOSE-RTN     THRU  9999-EXIT.

       0000-EXIT.    EXIT.

      *-------------------------------------------------------*
       1000-INITIAL-RTN.

           SET SUCCESS-OPENINFL TO TRUE.

           OPEN INPUT  ACCT-IN-FL.

           IF  ACC-PROF-STAT  NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'       UPON CONSOLE
               DISPLAY 'OPEN ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN INPUT  STMT-IN-FL.
           IF  STMT-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'       UPON CONSOLE
               DISPLAY 'OPEN STMTINF ERROR,CODE  = '    STMT-IN-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN STMTINF ERROR,CODE  = '    STMT-IN-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN I-O    DETL-IO-FL.
           IF  DETL-IO-STAT NOT = '00' AND
               DETL-IO-STAT NOT = '35'
               DISPLAY '*** PROGRAM STMMCG06 ***'       UPON CONSOLE
               DISPLAY 'OPEN DETLINF ERROR,CODE  = '    DETL-IO-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN DETLINF ERROR,CODE  = '    DETL-IO-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN OUTPUT STMT-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'        UPON CONSOLE
               DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
                                                         UPON CONSOLE
               DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN OUTPUT PRTF-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'        UPON CONSOLE
               DISPLAY 'OPEN PRTFOUTF ERROR,CODE  = ' PRTF-OUT-STAT
                                                         UPON CONSOLE
               DISPLAY 'OPEN PRTFOUTF ERROR,CODE  = ' PRTF-OUT-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

       1000-EXIT.    EXIT.
      *-------------------------------------------------------*
       2000-MAIN-PROCESS.
           PERFORM 2100-READ-ACCTINF-RTN  THRU 2100-EXIT.
           PERFORM UNTIL EOF-ACCTINF
              PERFORM 3000-PROCESS-GEN-STMT-RTN  THRU 3000-EXIT
              PERFORM 2100-READ-ACCTINF-RTN  THRU 2100-EXIT
           END-PERFORM.
            GO TO  2000-EXIT.
       2000-EXIT.    EXIT.
      *-------------------------------------------------------*
       2100-READ-ACCTINF-RTN.
           MOVE ZERO   TO WK-NEW-SEQ-NO.
           SET NOT-GEN-RFT-STMTINF   TO TRUE.

           READ ACCT-IN-FL AT END
                SET EOF-ACCTINF TO TRUE.

           IF ACCT-RFT-FLG = 'Y'
              SET GEN-RFT-STMTINF   TO TRUE.

       2100-EXIT.    EXIT.
      *-------------------------------------------------------*
       2200-READ-STMTINF-RTN.

           SET NOT-EOF-STMTINF TO TRUE.
           READ STMT-IN-FL NEXT AT END
                SET EOF-STMTINF  TO TRUE
                GO TO 2200-EXIT.
           IF STMT-IN-STAT NOT = '00' AND
              STMT-IN-STAT NOT = '10'
              DISPLAY '*** PROGRAM STMMCG06 ***'  UPON CONSOLE
              DISPLAY 'READ STMTINF IN ERROR,CODE = ' STMT-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'READ STMTINF IN ERROR,CODE = ' STMT-IN-STAT
              SET EOF-STMTINF TO TRUE
           END-IF.

           IF ACCT-NO NOT = STMT-IN-ACCT-KEY
              SET EOF-STMTINF       TO TRUE.

       2200-EXIT.    EXIT.
      *-------------------------------------------------------*
       2300-READ-DETLINF-RTN.

           READ DETL-IO-FL NEXT AT END
                SET EOF-DETLINF  TO TRUE
                GO TO 2300-EXIT.
           IF DETL-IO-STAT NOT = '00' AND
              DETL-IO-STAT NOT = '10'
              DISPLAY '*** PROGRAM STMMCG06 ***'  UPON CONSOLE
              DISPLAY 'READ DETLINF IN ERROR,CODE = ' DETL-IO-STAT
                                                  UPON CONSOLE
              DISPLAY 'READ DETLINF IN ERROR,CODE = ' DETL-IO-STAT
              SET EOF-DETLINF TO TRUE
           END-IF.

           IF (STMT-IN-ACCT-KEY      NOT = CNTL-RFT-ACC-NO)
              OR (STMT-IN-DESC-40(2:36) NOT = CNTL-RFT-DESC)
              SET NOT-FOUND-DETLINF TO TRUE
              SET EOF-DETLINF       TO TRUE.

       2300-EXIT.    EXIT.
      *-------------------------------------------------------*
       3000-PROCESS-GEN-STMT-RTN.
           MOVE ACCT-NO   TO  STMT-IN-ACCT-KEY
           MOVE '000000'  TO  STMT-IN-SEQ-KEY
      *    DISPLAY 'START GEN-STMT: ' STMT-IN-ACCT-KEY,
      *                               STMT-IN-SEQ-KEY
           SET   NOT-EOF-STMTINF     TO TRUE
           START STMT-IN-FL KEY IS GREATER THAN STMT-CNTL-KEY
              INVALID KEY
              DISPLAY 'NO STATEMENT TRANSACTION FOR THIS A/C '
                       STMT-IN-ACCT-KEY
              SET NOT-FOUND-STMTINF TO TRUE
              GO TO 3000-EXIT.

           PERFORM 2200-READ-STMTINF-RTN  THRU 2200-EXIT
           PERFORM UNTIL EOF-STMTINF
      ***     DISPLAY 'STMT-IN       : ' STMT-IN-ACCT-KEY,
      ***                                STMT-IN-SEQ-KEY

              IF  STMT-IN-REC-TYP  =  '1'
                  IF STMT-IN-BAL-SIGN = 'C'
                     COMPUTE WK-NEW-STMT-IN-BAL  = STMT-IN-BAL * 1
                  ELSE
                     COMPUTE WK-NEW-STMT-IN-BAL  = STMT-IN-BAL * -1
                  END-IF
              END-IF

              IF (STMT-IN-DESC-40(2:4) = 'PPYR' OR 'IBFT')
                  SET GEN-DETAIL       TO TRUE
                  PERFORM 6250-PROCESS-CHECK-DESC THRU 6250-EXIT
              ELSE
      **>>CB62020008
                  IF (STMT-IN-DESC-40(2:3) = 'PPY')
                      SET GEN-DETAIL       TO TRUE
                      PERFORM 6250-PROCESS-CHECK-DESC THRU 6250-EXIT
                  ELSE
      **<<CB62020008
                      SET NOT-GEN-DETAIL   TO TRUE
      **>>CB62020008
                  END-IF
      **<<CB62020008
              END-IF

              IF ( STMT-IN-CHANNEL = 'BCMS' AND
                   STMT-IN-SYMBOL NOT = 'FE ')
                 AND GEN-RFT-STMTINF
                 AND GEN-DETAIL

      ***        DISPLAY ' STEP 1 : ' ACCT-NO ' ' STMT-IN-CHANNEL
      ***                             ' ' STMT-IN-DESC-40
                 PERFORM 4000-PROCESS-GEN-DETL-STMT-RTN  THRU 4000-EXIT
              ELSE
                 ADD     1           TO  WK-NEW-SEQ-NO
                 PERFORM 5500-GEN-DATA-FOR-OUTPUT  THRU 5500-EXIT
                 PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
              END-IF
              PERFORM 2200-READ-STMTINF-RTN  THRU 2200-EXIT
           END-PERFORM.
       3000-EXIT.    EXIT.
      *-------------------------------------------------------*
       4000-PROCESS-GEN-DETL-STMT-RTN.

           IF STMT-IN-DC-CODE  = 'C '
              COMPUTE WK-ORG-REC-IN-AMT  = STMT-IN-AMT * 1
           ELSE
              COMPUTE WK-ORG-REC-IN-AMT  = STMT-IN-AMT * -1
           END-IF
      *    DISPLAY 'WK-ORG-REC-IN-AMT : ' WK-ORG-REC-IN-AMT
           MOVE STMT-IN-ACCT-KEY      TO CNTL-RFT-ACC-NO
           MOVE STMT-IN-DESC-40(2:36) TO CNTL-RFT-DESC
           MOVE SPACE                 TO CNTL-RFT-SERIAL-NO

      **   DISPLAY 'CNTL-RFT              :' CNTL-RFT-ACC-NO,
      **                                     STMT-IN-DESC-40(2:36),
      **                                     CNTL-RFT-SERIAL-NO

           SET   NOT-EOF-DETLINF  TO TRUE
           START DETL-IO-FL KEY IS GREATER THAN DETL-CNTL-KEY
              INVALID KEY
              DISPLAY 'NO DETAIL STATEMENT TRANSACTION FOR THIS A/C '
                       CNTL-RFT-ACC-NO, CNTL-RFT-DESC
              SET NOT-FOUND-DETLINF TO TRUE
              GO TO 4000-EXIT.

           PERFORM 2300-READ-DETLINF-RTN  THRU 2300-EXIT
           PERFORM UNTIL EOF-DETLINF
      *       DISPLAY '    GEN-DETL  : ' DETL-CNTL-KEY
              PERFORM 6000-CHK-DETL-DATA-FOR-OUTPUT  THRU 6000-EXIT
              IF FOUND-RFT-STMT
                 ADD     1           TO  WK-NEW-SEQ-NO
                 PERFORM 5250-GEN-DETL-DATA-FOR-OUTPUT  THRU 5250-EXIT
                 PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
              END-IF
              PERFORM 2300-READ-DETLINF-RTN  THRU 2300-EXIT
           END-PERFORM.
      *    DISPLAY 'EOF-WK-ORG-REC-IN-AMT : ' WK-ORG-REC-IN-AMT.
           IF EOF-DETLINF AND
              ( WK-ORG-REC-IN-AMT NOT = ZERO )
      **      DISPLAY 'EOF-WK-ORG-REC-IN-AMT : ' WK-ORG-REC-IN-AMT
              PERFORM 5750-GEN-DIFF-DETL-DATA-OUTPUT  THRU 5750-EXIT
              PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
           END-IF.
       4000-EXIT.    EXIT.
      *-------------------------------------------------------*
       5000-WRITE-OUTPUT-STMT-RTN.

      *    DISPLAY 'WK-NEW-SEQ-NO : ' WK-NEW-SEQ-NO
           WRITE STMT-OUT-REC.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'         UPON CONSOLE
               DISPLAY 'WRITE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'WRITE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT.

       5000-EXIT.    EXIT.
      *-------------------------------------------------------*
       5250-GEN-DETL-DATA-FOR-OUTPUT.

           MOVE  STMT-IN-REC     TO STMT-OUT-REC
           MOVE  DETL-RFT-REC    TO OUT-RFT-DETL-REC
           MOVE  WK-NEW-SEQ-NO   TO SOUT-SEQ-KEY
                                    SOUT-IN-SEQ-NO
           COMPUTE  SOUT-SEQ-NO    = WK-NEW-SEQ-NO - 1
           COMPUTE  SOUT-AC-SEQ-NO = SOUT-SEQ-NO
      **   DISPLAY 'RFT-NET-PAY-AMT : ' RFT-NET-PAY-AMT
           MOVE  RFT-NET-PAY-AMT TO SOUT-IN-AMT
                                    SOUT-AMT

           IF STMT-IN-DC-CODE     = 'C '
             COMPUTE WK-STMT-IN-AMT  = RFT-PAY-AMT * 1
           ELSE
             COMPUTE WK-STMT-IN-AMT  = RFT-PAY-AMT * -1
           END-IF
           COMPUTE WK-NEW-STMT-IN-BAL = WK-NEW-STMT-IN-BAL +
                                        WK-STMT-IN-AMT

           COMPUTE WK-ORG-REC-IN-AMT  = WK-ORG-REC-IN-AMT -
                                        WK-STMT-IN-AMT
      *    DISPLAY 'COMP WK-ORG-REC-IN-AMT : ' WK-ORG-REC-IN-AMT
           IF WK-NEW-STMT-IN-BAL > 0
              MOVE 'C'      TO   SOUT-IN-BAL-SIGN
           ELSE
              MOVE 'D'      TO   SOUT-IN-BAL-SIGN
           END-IF
           MOVE  WK-NEW-STMT-IN-BAL TO  SOUT-IN-BAL
                                        SOUT-BAL
           IF WK-STMT-IN-AMT     > 0
              MOVE 'C '      TO   SOUT-IN-DC-CODE
              MOVE 'C'       TO   SOUT-AMT-S
           ELSE
              MOVE 'D '      TO   SOUT-IN-DC-CODE
              MOVE 'D'       TO   SOUT-AMT-S
           END-IF.

       5250-EXIT.    EXIT.
      *-------------------------------------------------------*
       5500-GEN-DATA-FOR-OUTPUT.

           MOVE  STMT-IN-REC     TO STMT-OUT-REC.
           MOVE  WK-NEW-SEQ-NO   TO SOUT-SEQ-KEY
                                    SOUT-IN-SEQ-NO

           IF STMT-IN-REC-TYP  =  '2'
              IF STMT-IN-DC-CODE  = 'C '
                 COMPUTE WK-STMT-IN-AMT  = STMT-IN-AMT * 1
              ELSE
                 COMPUTE WK-STMT-IN-AMT  = STMT-IN-AMT * -1
              END-IF
              COMPUTE WK-NEW-STMT-IN-BAL = WK-NEW-STMT-IN-BAL +
                                           WK-STMT-IN-AMT
              IF WK-NEW-STMT-IN-BAL > 0
                 MOVE 'C'      TO   SOUT-IN-BAL-SIGN
              ELSE
                 MOVE 'D'      TO   SOUT-IN-BAL-SIGN
              END-IF
              MOVE  WK-NEW-STMT-IN-BAL TO  SOUT-IN-BAL
                                           SOUT-BAL
              COMPUTE  SOUT-SEQ-NO  =  WK-NEW-SEQ-NO - 1
              COMPUTE  SOUT-AC-SEQ-NO = SOUT-SEQ-NO
           END-IF.

           IF NOT-GEN-RFT-STMTINF
               MOVE  SPACE           TO SOUT-AVL-BAL
           END-IF.

           IF  STMT-IN-REC-TYP  =  '3'
               MOVE  STMT-OUT-REC    TO TAIL-IN-REC
               MOVE  SOUT-SEQ-KEY    TO TAIL-ALL-SEQ-NO
               MOVE  TAIL-IN-REC     TO STMT-OUT-REC
           END-IF.

       5500-EXIT.    EXIT.
      *-------------------------------------------------------*
       5750-GEN-DIFF-DETL-DATA-OUTPUT.

           MOVE  STMT-IN-REC     TO STMT-OUT-REC
           ADD   1               TO WK-NEW-SEQ-NO
           MOVE  WK-NEW-SEQ-NO   TO SOUT-SEQ-KEY
                                    SOUT-IN-SEQ-NO
           COMPUTE  SOUT-SEQ-NO  =  WK-NEW-SEQ-NO - 1
           COMPUTE  SOUT-AC-SEQ-NO = SOUT-SEQ-NO
           MOVE  WK-ORG-REC-IN-AMT TO SOUT-IN-AMT
                                      SOUT-AMT
           IF WK-ORG-REC-IN-AMT  > 0
              MOVE 'C '      TO   SOUT-IN-DC-CODE
              MOVE 'C'       TO   SOUT-AMT-S
      **>>CB62020008
              IF (STMT-IN-DESC-40(2:4) = 'PPYR' OR 'IBFT')
      **<<CB62020008
                   MOVE  'ERP032:IBFT/PPYR Credit Adjust Txn'
                                      TO O-RFT-REMARKS
                   MOVE  ' IBFT/PPYR Credit Adjust Txn'
                                      TO O-RFT-STMT-DESC
      **>>CB62020008
              ELSE
                   MOVE  'ERP032:PPY Credit Adjust Txn'
                                      TO O-RFT-REMARKS
                   MOVE  ' PPY Credit Adjust Txn'
                                      TO O-RFT-STMT-DESC
              END-IF
      **<<CB62020008
      **      MOVE '000000000000000'  TO O-RFT-PAY-AMT
              MOVE WK-ORG-REC-IN-AMT  TO O-RFT-PAY-AMT
                                         O-RFT-NET-PAY-AMT
           ELSE
              MOVE 'D '      TO   SOUT-IN-DC-CODE
              MOVE 'D'       TO   SOUT-AMT-S
      **>>CB62020008
              IF (STMT-IN-DESC-40(2:4) = 'PPYR' OR 'IBFT')
      **<<CB62020008
                   MOVE 'ERP031:IBFT/PPYR Debit Adjust Txn'
                                      TO O-RFT-REMARKS
                   MOVE ' IBFT/PPYR Debit Adjust Txn'
                                      TO O-RFT-STMT-DESC
      **>>CB62020008
              ELSE
                   MOVE 'ERP031:PPY Debit Adjust Txn'
                                      TO O-RFT-REMARKS
                   MOVE ' PPY Debit Adjust Txn'
                                      TO O-RFT-STMT-DESC
              END-IF
      **<<CB62020008
      **      MOVE '000000000000000'  TO O-RFT-PAY-AMT
              MOVE WK-ORG-REC-IN-AMT  TO O-RFT-PAY-AMT
                                         O-RFT-NET-PAY-AMT
           END-IF
           IF SOUT-IN-DC-CODE  = 'C '
              COMPUTE WK-STMT-IN-AMT  = SOUT-IN-AMT * 1
           ELSE
              COMPUTE WK-STMT-IN-AMT  = SOUT-IN-AMT * -1
           END-IF
           COMPUTE WK-NEW-STMT-IN-BAL = WK-NEW-STMT-IN-BAL +
                                        WK-STMT-IN-AMT

           COMPUTE WK-ORG-REC-IN-AMT  = WK-ORG-REC-IN-AMT -
                                        WK-STMT-IN-AMT
           IF WK-NEW-STMT-IN-BAL > 0
              MOVE 'C'      TO   SOUT-IN-BAL-SIGN
           ELSE
              MOVE 'D'      TO   SOUT-IN-BAL-SIGN
           END-IF
           MOVE  WK-NEW-STMT-IN-BAL TO  SOUT-IN-BAL
                                        SOUT-BAL.

       5750-EXIT.    EXIT.
      *-------------------------------------------------------*
       6000-CHK-DETL-DATA-FOR-OUTPUT.

           SET  NOT-FOUND-RFT-STMT   TO TRUE
      **>>CB62020008
           IF ((STMT-IN-DESC-40(2:3) = 'PPY') AND
                CNTL-RFT-DESC = STMT-IN-DESC-40(2:36)) OR
      **<<CB62020008
              ((STMT-IN-DESC-40(2:4) = 'PPYR' OR 'IBFT') AND
                CNTL-RFT-DESC = STMT-IN-DESC-40(2:36))
      *    DISPLAY 'CNTL-RFT-DESC :' CNTL-RFT-DESC
      *    DISPLAY 'STMT-IN-DESC-40(2:4) :' STMT-IN-DESC-40(2:4)
      *    DISPLAY 'STMT-IN-DESC-40(2:36) :' STMT-IN-DESC-40(2:36)
                 IF SINGLE-TRANS
                    IF (STMT-IN-AMT = RFT-NET-PAY-AMT) AND
                       (RFT-REC-PROCESS NOT = 'Y')
                       MOVE 'Y'       TO   RFT-REC-PROCESS
                       REWRITE DETL-IO-REC
                       SET FOUND-RFT-STMT     TO TRUE
                       SET EOF-DETLINF        TO TRUE
                    ELSE
                       SET NOT-FOUND-RFT-STMT TO TRUE
                    END-IF
                 ELSE
                    IF RFT-REC-PROCESS     NOT = 'Y'
                       MOVE 'Y'       TO   RFT-REC-PROCESS
                       REWRITE DETL-IO-REC
                       SET FOUND-RFT-STMT     TO TRUE
                    ELSE
                       SET NOT-FOUND-RFT-STMT TO TRUE
                    END-IF
                 END-IF
           END-IF.

       6000-EXIT.    EXIT.

      *-------------------------------------------------------*
       6250-PROCESS-CHECK-DESC.

           IF STMT-IN-DESC-40 NOT EQUAL SPACES
              MOVE ZEROES TO N-STMT-DESC-LEN
              INSPECT STMT-IN-DESC-40 TALLYING N-STMT-DESC-LEN
              FOR CHARACTERS BEFORE INITIAL THREE-SPACE
           END-IF.

      **   DISPLAY 'STMT :'STMT-IN-DESC-40(N-STMT-DESC-LEN :1)

           IF STMT-IN-DESC-40(N-STMT-DESC-LEN :1) = 'S'
              SET SINGLE-TRANS     TO TRUE
           END-IF.

           IF STMT-IN-DESC-40(N-STMT-DESC-LEN :1) = 'B'
              SET BLULK-TRANS      TO TRUE
           END-IF.

      **   DISPLAY 'STMT-IN-DESC-40 :'STMT-IN-DESC-40
      **   DISPLAY 'N-STMT-DESC-LEN :'N-STMT-DESC-LEN
      **   DISPLAY 'STMT 6:'STMT-IN-DESC-40(N-STMT-DESC-LEN - 6:7)
      **   DISPLAY 'STMT 8:'STMT-IN-DESC-40(N-STMT-DESC-LEN - 8:7)

           IF STMT-IN-DC-CODE  = 'C '
      **      AND STMT-IN-DESC-40(N-STMT-DESC-LEN - 6:7)
              AND STMT-IN-DESC-40(N-STMT-DESC-LEN - 8:7)
                                       = 'REVERSE'
              SET GEN-DETAIL       TO TRUE
           END-IF.

           IF STMT-IN-DC-CODE  = 'C '
      **      AND STMT-IN-DESC-40(N-STMT-DESC-LEN - 6:7)
              AND STMT-IN-DESC-40(N-STMT-DESC-LEN - 8:7)
                                   NOT = 'REVERSE'
              SET NOT-GEN-DETAIL   TO TRUE
           END-IF.

       6250-EXIT.    EXIT.

       9999-CLOSE-RTN.
      *** CLOSE FILE
           CLOSE  ACCT-IN-FL.
           IF  ACC-PROF-STAT  NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'        UPON CONSOLE
               DISPLAY 'CLOSE ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE ACCPROF ERROR,CODE  = '   ACC-PROF-STAT.

           CLOSE  STMT-IN-FL.
           IF  STMT-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'        UPON CONSOLE
               DISPLAY 'CLOSE STMTINF ERROR,CODE  = '    STMT-IN-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE STMTINF ERROR,CODE  = '    STMT-IN-STAT.

           CLOSE  DETL-IO-FL.
           IF  DETL-IO-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'        UPON CONSOLE
               DISPLAY 'CLOSE DETLINF ERROR,CODE  = '    DETL-IO-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE DETLINF ERROR,CODE  = '    DETL-IO-STAT.

           CLOSE  STMT-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'         UPON CONSOLE
               DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT.

           CLOSE  PRTF-OUT-FL.
           IF  PRTF-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG06 ***'         UPON CONSOLE
               DISPLAY 'CLOSE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'CLOSE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT.

           STOP   RUN.

       9999-EXIT.    EXIT.

      *-------------------- END PROGRAM -------------------*
/*
//LKED.SYSLIB   DD  DSN=&LIBPRFX..SCEELKED,DISP=SHR
//              DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD,DISP=SHR
//LKED.SYSLMOD  DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD(STMMCG06),DISP=SHR
//LKED.SYSPRINT DD  SYSOUT=X
/*
/*