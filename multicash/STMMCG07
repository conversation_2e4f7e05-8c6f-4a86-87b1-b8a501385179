//STMMCG07 JOB STMMCG07,'STMMCG07',MSGCLASS=X,CLASS=7,
//     NOTIFY=&SYSUID
//STEP01   EXEC IGYWCL
//SYSPRINT DD   SYSOUT=X
//<PERSON><PERSON><PERSON><PERSON>    DD   *
      *------------------------*
       IDENTIFICATION DIVISION.
      *------------------------*
       PROGRAM-ID.   STMMCG07.
      *----------------------------------------------------------------*
      * PROGRAM NAME: STMMCG07
      * AUTHOR      : ITTICHOTE CH.
      * STARTED DATE: 01/04/2022
      *----------------------------------------------------------------*
      * DESCRIPTION : THIS PROGRAM WILL READ STMT FILE AND ACCT PROFILE
      *               AND DETAIL STMT FILE FOR FILLERING ONLY ACCOUNT
      *               THEN WRITE THE OUTPUT FILE INCLUDE DETAIL.
      *               GENERATE OUTWARD DETAIL.
      *------------------------------------------------------------*
       ENVIRONMENT DIVISION.
      *---------------------*
       CONFIGURATION SECTION.
       SOURCE-COMPUTER. IBM-4341.
       OBJECT-COMPUTER. IBM-4341.
       SPECIAL-NAMES.   C01 IS  NEXT-PAGE.
      *----------------------*
       INPUT-OUTPUT SECTION.
      *----------------------*
       FILE-CONTROL.

      *** READ ACCOUNT PROFILE (PS INPUT)
            SELECT ACCT-IN-FL ASSIGN    TO  ACCPROF
                   FILE STATUS          IS  ACC-PROF-STAT.

      *** READ STMT FILE (VSAM INPUT)
            SELECT STMT-IN-FL  ASSIGN   TO  STMTINF
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  STMT-CNTL-KEY
                   FILE STATUS          IS  STMT-IN-STAT.

      *** READ DETAIL FILE (VSAM INPUT)
            SELECT DETL-IN-FL  ASSIGN   TO  DETLINF
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  DETL-CNTL-KEY
                   FILE STATUS          IS  DETL-IO-STAT.

      *** WRITE STMT FILE (PS OUTPUT)
            SELECT STMT-OUT-FL ASSIGN   TO  STMTOUTF
                   FILE STATUS          IS  STMT-OUT-STAT.

      *** WRITE PRTF FILE (PS OUTPUT)
            SELECT PRTF-OUT-FL ASSIGN   TO  PRTFOUTF
                   FILE STATUS          IS  PRTF-OUT-STAT.

      *--------------*
       DATA DIVISION.
      *--------------*
       FILE SECTION.

       FD ACCT-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   200 CHARACTERS
           DATA   RECORD     IS ACCT-IN-REC.
       01 ACCT-IN-REC.
          05 FILLER                 PIC  X(16).
          05 ACCT-NO                PIC  X(10).
          05 FILLER                 PIC  X(03).
          05 ACCT-NAME              PIC  X(50).
          05 FILLER                 PIC  X(71).
          05 ACCT-MCASH-FMG         PIC  X(01).
          05 ACCT-PAYMENT-FLG       PIC  X(01).
          05 ACCT-LCC-FLG           PIC  X(01).
          05 ACCT-BPAY-FLG          PIC  X(01).
          05 ACCT-EBPP-FLG          PIC  X(01).
          05 ACCT-P2P-FLG           PIC  X(01).
          05 ACCT-OR-FLG            PIC  X(01).
          05 FILLER                 PIC  X(43).

       FD STMT-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   2516 CHARACTERS
           DATA   RECORD     IS  STMT-IN-REC.
       01 STMT-IN-REC.
         03 STMT-CNTL-KEY.
          05 STMT-IN-ACCT-KEY   PIC X(10).
          05 STMT-IN-SEQ-KEY    PIC 9(06).
     *** 03 STMT-IN                   PIC  X(2500).
         03 STMT-IN-DETL.
          05 I-INTEBANK.
             07 STM-IN-REC-TYPE         PIC  X(01).
             07 FILLER                  PIC  X(05).
             07 STMT-IN-ACCT-NO         PIC  X(10).
             07 STMT-IN-BANK-ID         PIC  X(03).
             07 STMT-IN-BRNO            PIC  X(04).
             07 STMT-IN-TDATE.
                09 STMT-IN-TDATE-YYYY  PIC  X(04).
                09 STMT-IN-TDATE-MM    PIC  X(02).
                09 STMT-IN-TDATE-DD    PIC  X(02).
             07 STMT-IN-BAL            PIC  9(14)V99.
             07 STMT-IN-BAL-SIGN       PIC  X(01).
             07 STMT-IN-AMT            PIC  9(13)V99.
             07 STMT-IN-DC-CODE        PIC  X(02).
             07 STMT-IN-TCODE          PIC  X(04).
             07 STMT-IN-TERM           PIC  X(04).
             07 STMT-IN-CHQNO          PIC  X(07).
             07 STMT-IN-REF-NUM        PIC  X(25).
             07 FILLER                 PIC  X(02).
             07 STMT-IN-GROUP-ID       PIC  X(05).
             07 STMT-IN-SERIAL-NO      PIC  X(06).
             07 STMT-IN-CHQNO-10       PIC  X(10).
             07 STMT-IN-DESC-40.
                09 STMT-IN-DESC-FOR    PIC  X(16).
                09 FILLER              PIC  X(1).
                09 STMT-IN-DESC-FCHK   PIC  X(3).
                09 FILLER              PIC  X(20).
             07 STMT-IN-SYMBOL         PIC  X(03).
             07 STMT-IN-CHANNEL        PIC  X(04).
          05 I-PAYMENT.
             07 I-STMT-TAMT-ERP        PIC  9(13)V99.
             07 I-STMT-CRDR-ERP        PIC  X(02).
      *RFT-> 07 FILLER                 PIC  X(853).
             07 I-PAYMENT-EXTEND.
      *RFT->    09 I-PAY-COMP-ID            PIC  X(12).
      *RFT->       09 I-RFT-COMP-ID                   PIC  X(15).
                09 I-RFT-COMP-ID            PIC  X(12).
                09 I-PAY-TRANS-DATE         PIC  X(21).
                09 I-PAY-TRANS-AMT          PIC  X(16).
                09 I-PAY-ACTION-FLAG        PIC  X(01).
      *RFT->    09 I-PAY-CN-REF             PIC  X(24).
      *RFT->       09 I-RFT-BATCH-REF                 PIC  X(35).
                09 I-RFT-BATCH-REF          PIC  X(24).
      *RFT->    09 I-PAY-S1-REF             PIC  X(32).
                09 I-RFT-FILE-REF           PIC  X(32).
      *RFT->    09 I-PAY-CUST-REF           PIC  X(40).
      *RFT->       09 I-RFT-TRANS-REF                 PIC  X(32).
                09 I-RFT-TRANS-REF          PIC  X(40).
      *RFT->    09 I-PAY-PROD-CODE          PIC  X(03).
                09 I-RFT-PROD-CODE          PIC  X(03).
      *RFT->    09 I-PAY-VALUE-DATE         PIC  X(08).
                09 I-RFT-VALUE-DATE         PIC  X(08).
      *RFT->    09 I-PAY-DEBIT-ACCT         PIC  X(25).
                09 I-RFT-DR-AC              PIC  X(25).
                09 I-PAY-DB-ACCT-TYPE       PIC  X(02).
                09 I-PAY-DB-BRC-CODE        PIC  X(04).
      *RFT->    09 I-PAY-DB-CURRENCY        PIC  X(03).
                09 I-RFT-DR-CCY             PIC  X(03).
      *RFT->    09 I-PAY-DB-AMT             PIC  X(16).
                09 I-RFT-DR-AMT             PIC  X(16).
      *RFT->    09 I-PAY-DB-FEE-ACCT        PIC  X(25).
                09 I-RFT-FE-DR-AC           PIC  X(25).
                09 I-PAY-DB-FEE-ACCT-TYPE   PIC  X(02).
                09 I-PAY-DB-FEE-BRC-CODE    PIC  X(04).
      *RFT->    09 I-PAY-NO-CREDIT          PIC  X(06).
                09 I-RFT-NO-OF-CR           PIC  X(06).
      *RFT->    09 I-PAY-CR-SEQ             PIC  X(06).
                09 I-RFT-CR-SEQ-NO          PIC  X(06).
      *RFT->    09 I-PAY-CR-ACCT            PIC  X(25).
                09 I-RFT-BENE-AC            PIC  X(25).
      *RFT->    09 I-PAY-CR-AMT             PIC  X(16).
      *RFT->    09 I-RFT-PAY-AMT                      PIC  X(15).
      *         09 I-RFT-PAY-AMT            PIC  X(16).
                09 I-RFT-PAY-AMT            PIC  *************.99.
      *RFT->    09 I-PAY-CR-NET-AMT         PIC  X(16).
      *RFT->    09 I-RFT-NET-PAY-AMT                  PIC X(15).
      *         09 I-RFT-NET-PAY-AMT        PIC  X(16).
                09 I-RFT-NET-PAY-AMT        PIC  *************.99.
                09 I-PAY-CR-BENE-FEE        PIC  X(16).
      *RFT->    09 I-PAY-TRANS-STATUS       PIC  X(01).
                09 I-RFT-PROC-STAT          PIC  X(01).
      *RFT->    09 I-PAY-TRANS-DES          PIC  X(100).
                09 I-RFT-PROC-REMARK        PIC  X(100).
      *RFT->    09 I-PAY-CR-CURRENCY        PIC  X(03).
                09 I-RFT-CR-CCY             PIC  X(03).
      *RFT->    09 I-PAY-RECEIVE-BANK-CD    PIC  X(03).
                09 I-RFT-BENE-BNK-CD        PIC  X(03).
      *RFT->    09 I-PAY-RECEIVE-BANK-NAME  PIC  X(35).
                09 I-RFT-BENE-BNK-NME       PIC  X(35).
      *RFT->    09 I-PAY-RECEIVE-BRC-CD     PIC  X(04).
                09 I-RFT-BENE-BR-CD         PIC  X(04).
      *RFT->    09 I-PAY-RECEIVE-BRC-NAME   PIC  X(35).
                09 I-RFT-BENE-BR-NME        PIC  X(35).
                09 I-PAY-WHT-PRESENT        PIC  X(01).
                09 I-PAY-INV-DET-PRESENT    PIC  X(01).
                09 I-PAY-CR-ADV-REQUIRE     PIC  X(01).
                09 I-PAY-DELIVERY-MODE      PIC  X(01).
                09 I-PAY-PICKUP-LOCATION    PIC  X(04).
                09 I-PAY-WHT-FORM-TYPE      PIC  X(02).
                09 I-PAY-WHT-TAX-NO         PIC  X(14).
                09 I-PAY-WHT-ATT-NO         PIC  X(06).
                09 I-PAY-NO-WHT-DET         PIC  X(02).
                09 I-PAY-TOT-WHT-AMT        PIC  X(16).
                09 I-PAY-NO-INV-DET         PIC  X(06).
                09 I-PAY-TOT-INV-AMT        PIC  X(16).
                09 I-PAY-WHT-PAY-TYPE       PIC  X(01).
                09 I-PAY-WHT-REMARK         PIC  X(40).
                09 I-PAY-WHT-DEDUCT-CODE    PIC  X(08).
                09 I-PAY-WHT-SIGNATORY      PIC  X(01).
                09 I-PAY-BENE-NOTIFI        PIC  X(01).
      *RFT      09 I-PAY-CUST-REF-NO        PIC  X(20).
                09 I-RFT-CUST-REF-NO        PIC  X(20).
                09 I-PAY-CHQ-REF-DOC-TYPE   PIC  X(01).
                09 I-PAY-PAY-TYPE-CODE      PIC  X(03).
                09 I-PAY-SERVICE-TYPE       PIC  X(02).
      *RFT      09 I-PAY-PAYEE-NAME-TH      PIC  X(100).
                09 I-RFT-BENE-NME           PIC  X(100).
                09 I-PAY-PAYEE-NAME-EN      PIC  X(70).
      *RFT->    09 I-PAY-BENE-TAX-ID        PIC  X(10).
                09 I-RFT-PAYEE-TAX-ID       PIC  X(10).
                09 I-PAY-CHQ-NO             PIC  X(08).
      *RFT->    09 I-PAY-WHT-SERIAL-NO      PIC  X(14).
                09 I-RFT-WHT-SEQ-NO         PIC  X(14).
      *RFT->
      *****  07  I-RFT-COMP-NAME    PIC X(35).
      *****  07  I-RFT-REMARK       PIC X(50).
      *RFT->
      **  05 FILLER             PIC  X(155).
          05 I-RFT-EXTEND.
             07  I-RFT-BENE-TAX-ID  PIC X(15).
             07  I-RFT-PP-CYCLE     PIC X(01).
             07  I-RFT-PROXY-TYP    PIC X(03).
             07  I-RFT-TAX-ID       PIC X(13).
             07  I-RFT-MOBILE-NO    PIC X(10).
             07  I-RFT-TRANS-REMARK PIC X(32).
             07  I-RFT-PP-ON-OS     PIC X(01).
             07  I-RFT-BENE-CHG     PIC X(02).
             07  I-RFT-SUB-PROD     PIC X(08).
             07  I-RFT-KEY-REF      PIC X(40).
             07  FILLER             PIC X(30).
          05 I-LCC              PIC  X(519).
          05 FILLER             PIC  X(100).
      *   05 I-BPAY             PIC  X(263).
          05 I-BPAY.
             07  I-STMT-BANK-CD      PIC X(02).
             07  I-STMT-CURR-CD      PIC X(03).
             07  I-STMT-ACCT         PIC 9(11).
             07  I-STMT-R-DATE       PIC X(10).
             07  I-STMT-SEQ-NO       PIC 9(09).
             07  I-STMT-D-TRAN       PIC X(10).
             07  I-STMT-D-DATE       PIC X(10).
             07  I-STMT-D-TIME       PIC X(05).
             07  I-STMT-T-CODE       PIC X(03).
             07  I-STMT-CHANNEL      PIC X(04).
             07  I-STMT-TR           PIC X(02).
             07  I-STMT-BR           PIC X(04).
             07  I-STMT-TERMINAL     PIC X(05).
             07  I-STMT-CHQNO        PIC X(08).
             07  I-STMT-AMT-S        PIC X(01).
             07  I-STMT-AMT          PIC X(16).
             07  I-STMT-BAL          PIC X(17).
             07  I-STMT-DESC         PIC X(40).
             07  I-STMT-BR-OF-AC     PIC X(04).
             07  I-STMT-ACC-TYP      PIC X(02).
             07  I-STMT-CUST-NME     PIC X(50).
             07  I-STMT-REF1         PIC X(20).
             07  I-STMT-REF2         PIC X(20).
             07  I-STMT-STATUS-MONEY PIC X(03).
             07  I-STMT-CH-CODE      PIC X(04).
          05 I-STMT-BR-ENG-NAME      PIC  X(30).
          05 FILLER             PIC  X(112).
          05 I-EPP              PIC  X(133).
          05 I-ADJUST-STATUS    PIC  X(01).
          05 I-ADJUST-DESC      PIC  X(42).
          05 FILLER             PIC  X(100).


       FD DETL-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   1100 CHARACTERS
           DATA   RECORD     IS  DETL-IN-REC.
       01 DETL-IN-REC.
          05 DETL-CNTL-KEY.
             07 DETL-FOR-REF-KEY       PIC X(16).
          05 DETL-DETL-REC.
      **** > 07 DETL-IN                PIC X(1084).
             07 DETL-IN-CUSTREF1       PIC X(35).
             07 DETL-IN-CUSTREF2       PIC X(35).
             07 DETL-IN-BULKREF        PIC X(35).
             07 DETL-IN-FORREF         PIC X(20).
             07 DETL-IN-CHANREF        PIC X(20).
             07 DETL-IN-TRANDATE       PIC X(10).
             07 DETL-IN-SENDNAME1      PIC X(35).
             07 DETL-IN-SENDNAME2      PIC X(35).
             07 DETL-IN-TAXID          PIC X(20).
             07 DETL-IN-REMCUR         PIC X(3).
             07 DETL-IN-REMAMT         PIC *************9.99.
             07 DETL-IN-STATUS         PIC X(2).
             07 DETL-IN-VALDATE        PIC X(10).
             07 DETL-IN-DRACNO         PIC X(20).
             07 DETL-IN-RECNAME1       PIC X(35).
             07 DETL-IN-RECNAME2       PIC X(35).
             07 DETL-IN-RECADDR1       PIC X(35).
             07 DETL-IN-RECADDR2       PIC X(35).
             07 DETL-IN-RECAC          PIC X(35).
             07 DETL-IN-BENBANK        PIC X(35).
             07 DETL-IN-BENBANKDET1    PIC X(35).
             07 DETL-IN-BENBANKDET2    PIC X(35).
             07 DETL-IN-BENBANKDET3    PIC X(35).
             07 DETL-IN-BENBANKDET4    PIC X(35).
             07 DETL-IN-BENBANKDET5    PIC X(35).
             07 DETL-IN-DETPAY1        PIC X(35).
             07 DETL-IN-DETPAY2        PIC X(35).
             07 DETL-IN-DETPAY3        PIC X(35).
             07 DETL-IN-DETPAY4        PIC X(35).
             07 DETL-IN-REMARK         PIC X(200).
             07 DETL-IN-FORCUSTID      PIC X(16).

       FD STMT-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   2500 CHARACTERS
           DATA   RECORD     IS STMT-OUT-REC.
       01 STMT-OUT-REC.
          05 O-INTEBANK.
             07 SWK-REC-TYPE            PIC  X(01).
             07 FILLER                  PIC  X(05).
             07 SWK-STMT-ACCT-NO        PIC  X(10).
             07 SWK-STMT-BANK-ID        PIC  X(03).
             07 SWK-STMT-BRNO           PIC  X(04).
             07 SWK-STMT-TDATE.
                15 SWK-STMT-TDATE-YYYY  PIC  X(04).
                15 SWK-STMT-TDATE-MM    PIC  X(02).
                15 SWK-STMT-TDATE-DD    PIC  X(02).
             07 SWK-STMT-BAMT           PIC  X(16).
             07 SWK-STMT-BAMT-SIGN      PIC  X(01).
             07 SWK-ACCT-NAME           PIC  X(30).
             07 FILLER                  PIC  X(34).
             07 SWK-STMT-SERIAL-NO      PIC  X(06).
             07 SWK-STMT-CHQNO-10       PIC  X(10).
             07 SWK-HEAD-SUM-DR         PIC  9(14)V99.
             07 SWK-HEAD-SUM-DR-TYP     PIC  X(01).
             07 SWK-HEAD-SUM-CR         PIC  9(14)V99.
             07 SWK-HEAD-SUM-CR-TYP     PIC  X(01).
             07 FILLER                  PIC  X(06).
             07 SWK-STMT-SYMBOL         PIC  X(03).
             07 SWK-STMT-CHAN           PIC  X(04).
          05 O-PAYMENT.
             07 SWK-STMT-TAMT-ERP       PIC  9(13)V99.
             07 SWK-STMT-CRDR-ERP       PIC  X(02).
      *RFT-> 07 FILLER                  PIC  X(853).
             07 O-PAYMENT-EXTEND.
      *RFT->    09 O-PAY-COMP-ID            PIC  X(12).
      *RFT->       09 O-RFT-COMP-ID                   PIC  X(15).
                09 O-RFT-COMP-ID            PIC  X(12).
                09 O-PAY-TRANS-DATE         PIC  X(08).
                09 FILLER                   PIC  X(01).
                09 O-PAY-TRANS-TIME         PIC  X(08).
                09 FILLER                   PIC  X(04).
                09 O-PAY-TRANS-AMT          PIC  *************.99.
                09 O-PAY-ACTION-FLAG        PIC  X(01).
      *RFT->    09 O-PAY-CN-REF             PIC  X(24).
      *RFT->       09 O-RFT-BATCH-REF                 PIC  X(35).
                09 O-RFT-BATCH-REF          PIC  X(24).
      *RFT->    09 O-PAY-S1-REF             PIC  X(32).
                09 O-RFT-FILE-REF           PIC  X(32).
      *RFT->    09 O-PAY-CUST-REF           PIC  X(40).
      *RFT->       09 O-RFT-TRANS-REF                 PIC  X(32).
                09 O-RFT-TRANS-REF          PIC  X(40).
      *RFT->    09 O-PAY-PROD-CODE          PIC  X(03).
                09 O-RFT-PROD-CODE          PIC  X(03).
      *RFT->    09 O-PAY-VALUE-DATE         PIC  X(08).
                09 O-RFT-VALUE-DATE         PIC  X(08).
      *RFT->    09 O-PAY-DEBIT-ACCT         PIC  X(25).
                09 O-RFT-DR-AC              PIC  X(25).
                09 O-PAY-DB-ACCT-TYPE       PIC  X(02).
                09 O-PAY-DB-BRC-CODE        PIC  X(04).
      *RFT->    09 O-PAY-DB-CURRENCY        PIC  X(03).
                09 O-RFT-DR-CCY             PIC  X(03).
      *RFT->    09 O-PAY-DB-AMT             PIC  X(16).
                09 O-RFT-DR-AMT             PIC  *************.99.
      *RFT->    09 O-PAY-DB-FEE-ACCT        PIC  X(25).
                09 O-RFT-FE-DR-AC           PIC  X(25).
                09 O-PAY-DB-FEE-ACCT-TYPE   PIC  X(02).
                09 O-PAY-DB-FEE-BRC-CODE    PIC  X(04).
      *RFT->    09 O-PAY-NO-CREDIT          PIC  X(06).
                09 O-RFT-NO-OF-CR           PIC  X(06).
      *RFT->    09 O-PAY-CR-SEQ             PIC  X(06).
                09 O-RFT-CR-SEQ-NO          PIC  X(06).
      *RFT->    09 O-PAY-CR-ACCT            PIC  X(25).
                09 O-RFT-BENE-AC            PIC  X(25).
      *RFT->    09 O-PAY-CR-AMT             PIC  X(16).
      *RFT->    09 O-RFT-PAY-AMT                      PIC  X(15).
      *         09 O-RFT-PAY-AMT            PIC  X(16).
                09 O-RFT-PAY-AMT            PIC  *************.99.
      *RFT->    09 O-PAY-CR-NET-AMT         PIC  X(16).
      *RFT->    09 O-RFT-NET-PAY-AMT                  PIC X(15).
      *         09 O-RFT-NET-PAY-AMT        PIC  X(16).
                09 O-RFT-NET-PAY-AMT        PIC  *************.99.
                09 O-PAY-CR-BENE-FEE        PIC  X(16).
      *RFT->    09 O-PAY-TRANS-STATUS       PIC  X(01).
                09 O-RFT-PROC-STAT          PIC  X(01).
      *RFT->    09 O-PAY-TRANS-DES          PIC  X(100).
                09 O-RFT-PROC-REMARK        PIC  X(100).
      *RFT->    09 O-PAY-CR-CURRENCY        PIC  X(03).
                09 O-RFT-CR-CCY             PIC  X(03).
      *RFT->    09 O-PAY-RECEIVE-BANK-CD    PIC  X(03).
                09 O-RFT-BENE-BNK-CD        PIC  X(03).
      *RFT->    09 O-PAY-RECEIVE-BANK-NAME  PIC  X(35).
                09 O-RFT-BENE-BNK-NME       PIC  X(35).
      *RFT->    09 O-PAY-RECEIVE-BRC-CD     PIC  X(04).
                09 O-RFT-BENE-BR-CD         PIC  X(04).
      *RFT->    09 O-PAY-RECEIVE-BRC-NAME   PIC  X(35).
                09 O-RFT-BENE-BR-NME        PIC  X(35).
                09 O-PAY-WHT-PRESENT        PIC  X(01).
                09 O-PAY-INV-DET-PRESENT    PIC  X(01).
                09 O-PAY-CR-ADV-REQUIRE     PIC  X(01).
                09 O-PAY-DELIVERY-MODE      PIC  X(01).
                09 O-PAY-PICKUP-LOCATION    PIC  X(04).
                09 O-PAY-WHT-FORM-TYPE      PIC  X(02).
                09 O-PAY-WHT-TAX-NO         PIC  X(14).
                09 O-PAY-WHT-ATT-NO         PIC  X(06).
                09 O-PAY-NO-WHT-DET         PIC  X(02).
                09 O-PAY-TOT-WHT-AMT        PIC  X(16).
                09 O-PAY-NO-INV-DET         PIC  X(06).
                09 O-PAY-TOT-INV-AMT        PIC  X(16).
                09 O-PAY-WHT-PAY-TYPE       PIC  X(01).
                09 O-PAY-WHT-REMARK         PIC  X(40).
                09 O-PAY-WHT-DEDUCT-CODE    PIC  X(08).
                09 O-PAY-WHT-SIGNATORY      PIC  X(01).
                09 O-PAY-BENE-NOTIFI        PIC  X(01).
      *RFT      09 O-PAY-CUST-REF-NO        PIC  X(20).
                09 O-RFT-CUST-REF-NO        PIC  X(20).
                09 O-PAY-CHQ-REF-DOC-TYPE   PIC  X(01).
                09 O-PAY-PAY-TYPE-CODE      PIC  X(03).
                09 O-PAY-SERVICE-TYPE       PIC  X(02).
      *RFT      09 O-PAY-PAYEE-NAME-TH      PIC  X(100).
                09 O-RFT-BENE-NME           PIC  X(100).
                09 O-PAY-PAYEE-NAME-EN      PIC  X(70).
      *RFT->    09 O-PAY-BENE-TAX-ID        PIC  X(10).
                09 O-RFT-PAYEE-TAX-ID       PIC  X(10).
                09 O-PAY-CHQ-NO             PIC  X(08).
      *RFT->    09 O-PAY-WHT-SERIAL-NO      PIC  X(14).
                09 O-RFT-WHT-SEQ-NO         PIC  X(14).
      *RFT->
      *****  07  O-RFT-COMP-NAME    PIC X(35).
      *****  07  O-RFT-REMARK       PIC X(50).
      *RFT->
      **  05 FILLER             PIC  X(155).
          05 O-RFT-EXTEND.
             07  O-RFT-BENE-TAX-ID  PIC X(15).
             07  O-RFT-PP-CYCLE     PIC X(01).
             07  O-RFT-PROXY-TYP    PIC X(03).
             07  O-RFT-TAX-ID       PIC X(13).
             07  O-RFT-MOBILE-NO    PIC X(10).
             07  O-RFT-TRANS-REMARK PIC X(32).
             07  O-RFT-PP-ON-OS     PIC X(01).
             07  O-RFT-BENE-CHG     PIC X(02).
             07  O-RFT-SUB-PROD     PIC X(08).
             07  O-RFT-KEY-REF      PIC X(40).
             07  FILLER             PIC X(30).
          05 O-LCC              PIC  X(519).
          05 FILLER             PIC  X(100).
      *   05 O-BPAY             PIC  X(263).
          05 O-BPAY.
             07  O-STMT-BANK-CD      PIC X(02).
             07  O-STMT-CURR-CD      PIC X(03).
             07  O-STMT-ACCT         PIC 9(11).
             07  O-STMT-R-DATE       PIC X(10).
             07  O-STMT-SEQ-NO       PIC 9(09).
             07  O-STMT-D-TRAN       PIC X(10).
             07  O-STMT-D-DATE       PIC X(10).
             07  O-STMT-D-TIME       PIC X(05).
             07  O-STMT-T-CODE       PIC X(03).
             07  O-STMT-CHANNEL      PIC X(04).
             07  O-STMT-TR           PIC X(02).
             07  O-STMT-BR           PIC X(04).
             07  O-STMT-TERMINAL     PIC X(05).
             07  O-STMT-CHQNO        PIC X(08).
             07  O-STMT-AMT-S        PIC X(01).
             07  O-STMT-AMT          PIC X(16).
             07  O-STMT-BAL          PIC X(17).
             07  O-STMT-DESC         PIC X(40).
             07  O-STMT-BR-OF-AC     PIC X(04).
             07  O-STMT-ACC-TYP      PIC X(02).
             07  O-STMT-CUST-NME     PIC X(50).
             07  O-STMT-REF1         PIC X(20).
             07  O-STMT-REF2         PIC X(20).
             07  O-STMT-STATUS-MONEY PIC X(03).
             07  O-STMT-CH-CODE      PIC X(04).
          05 O-STMT-BR-ENG-NAME      PIC  X(30).
          05 FILLER             PIC  X(112).
          05 O-EPP              PIC  X(133).
          05 O-ADJUST-STATUS    PIC  X(01).
          05 O-ADJUST-DESC      PIC  X(42).
          05 FILLER             PIC  X(100).

       FD PRTF-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   132 CHARACTERS
           DATA   RECORD     IS PRTF-OUT-REC.
       01 PRTF-OUT-REC.
          05 FILLER             PIC X(132).

      *------------------------*
       WORKING-STORAGE SECTION.
      *------------------------*
       77 SW-OPENINFL                  PIC X(01)   VALUE 'Y'.
          88 ERR-OPENINFL                          VALUE 'N'.
          88 SUCCESS-OPENINFL                      VALUE 'Y'.

       77 SW-ACCTINF                   PIC X(01)   VALUE 'N'.
          88 EOF-ACCTINF                           VALUE 'Y'.
          88 NOT-EOF-ACCTINF                       VALUE 'N'.
       77 SW-STMTINF                   PIC X(01)   VALUE 'N'.
          88 EOF-STMTINF                           VALUE 'Y'.
          88 NOT-EOF-STMTINF                       VALUE 'N'.

       77 SW-ACCT-OR-FLG              PIC X(01)   VALUE 'N'.
          88 GEN-OR-STMTINF                       VALUE 'Y'.
          88 NOT-GEN-OR-STMTINF                   VALUE 'N'.

       77 SW-STMTINF-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-STMTINF                         VALUE 'Y'.
          88 NOT-FOUND-STMTINF                     VALUE 'N'.
       77 SW-STMTINF-CHANGE            PIC X(01)   VALUE 'N'.
          88 CHANGE-STMTINF                        VALUE 'Y'.
          88 NOT-CHANGE-STMTINF                    VALUE 'N'.

       77 SW-DETLINF                   PIC X(01)   VALUE 'N'.
          88 EOF-DETLINF                           VALUE 'Y'.
          88 NOT-EOF-DETLINF                       VALUE 'N'.
       77 SW-DETLINF-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-DETLINF                         VALUE 'Y'.
          88 NOT-FOUND-DETLINF                     VALUE 'N'.
       77 SW-DETLINF-CHANGE            PIC X(01)   VALUE 'N'.
          88 CHANGE-DETLINF                        VALUE 'Y'.
          88 NOT-CHANGE-DETLINF                    VALUE 'N'.

       01  WK-FILE-STATUS.
           03  ACC-PROF-STAT       PIC  X(02).
           03  STMT-IN-STAT        PIC  X(02).
           03  DETL-IO-STAT        PIC  X(02).
           03  STMT-OUT-STAT       PIC  X(02).
           03  PRTF-OUT-STAT       PIC  X(02).
       01  WK-DATE-X               PIC  9(08).
       01  WK-DATE-Y.
           03  WK-Y4               PIC  9(04).
           03  WK-MM               PIC  9(02).
           03  WK-DD               PIC  9(02).

       01  WK-INT-DATE-X           PIC  9(08).
       01  WK-INT-DATE-Y.
           03  FILLER              PIC  9(02) VALUE ZEROS.
           03  WK-INT-D6           PIC  9(06).

       01 TAIL-IN-REC.
          05 TAIL-CNTL-KEY.
             07 TAIL-IN-ACCT-KEY   PIC X(10).
             07 TAIL-IN-SEQ-KEY    PIC 9(06).
          05 TAIL-IN-REC-TYP           PIC  X(01).
          05 FILLER                    PIC  X(05).
          05 TAIL-IN-ACCT-NO           PIC  X(10).
          05 FILLER                    PIC  X(15).
          05 TAIL-IN-BAL               PIC  9(14)V99.
          05 TAIL-IN-BAL-SIGN          PIC  X(01).
          05 TAIL-IN-AMT               PIC  9(14)V99.
          05 TAIL-IN-DC-CODE           PIC  X(01).
          05 TAIL-ALL-SEQ-NO           PIC  9(06).
          05 FILLER                    PIC  X(41).
          05 TAIL-IN-SEQ-NO            PIC  9(06).
          05 TAIL-IN-CHG-NO10          PIC  9(10).
          05 TAIL-IN-DESC40            PIC  X(40).
          05 TAIL-IN-SYMBOL            PIC  X(03).
          05 TAIL-IN-CHANNEL           PIC  X(04).
          05 FILLER                    PIC  X(350).

      *---------------------------------------------------------*
       PROCEDURE DIVISION.
      *-------------------*
       0000-MAIN-PROCESS.

           PERFORM  1000-INITIAL-RTN   THRU  1000-EXIT.
           PERFORM  2000-MAIN-PROCESS  THRU  2000-EXIT.
           PERFORM  9999-CLOSE-RTN     THRU  9999-EXIT.

       0000-EXIT.    EXIT.

      *-------------------------------------------------------*
       1000-INITIAL-RTN.

           SET SUCCESS-OPENINFL TO TRUE.

           OPEN INPUT  ACCT-IN-FL.

           IF  ACC-PROF-STAT  NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'       UPON CONSOLE
               DISPLAY 'OPEN ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN INPUT  STMT-IN-FL.
           IF  STMT-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'       UPON CONSOLE
               DISPLAY 'OPEN STMTINF ERROR,CODE  = '    STMT-IN-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN STMTINF ERROR,CODE  = '    STMT-IN-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN I-O    DETL-IN-FL.
           IF  DETL-IO-STAT NOT = '00' AND
               DETL-IO-STAT NOT = '35'
               DISPLAY '*** PROGRAM STMMCG07 ***'       UPON CONSOLE
               DISPLAY 'OPEN DETLINF ERROR,CODE  = '    DETL-IO-STAT
                                                        UPON CONSOLE
               DISPLAY 'OPEN DETLINF ERROR,CODE  = '    DETL-IO-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN OUTPUT STMT-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'        UPON CONSOLE
               DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
                                                         UPON CONSOLE
               DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

           OPEN OUTPUT PRTF-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'        UPON CONSOLE
               DISPLAY 'OPEN PRTFOUTF ERROR,CODE  = ' PRTF-OUT-STAT
                                                         UPON CONSOLE
               DISPLAY 'OPEN PRTFOUTF ERROR,CODE  = ' PRTF-OUT-STAT
               SET ERR-OPENINFL TO TRUE
               GO TO  1000-EXIT.

       1000-EXIT.    EXIT.
      *-------------------------------------------------------*
       2000-MAIN-PROCESS.
           PERFORM 2100-READ-ACCTINF-RTN  THRU 2100-EXIT.
           PERFORM UNTIL EOF-ACCTINF
              PERFORM 3000-PROCESS-GEN-STMT-RTN  THRU 3000-EXIT
              PERFORM 2100-READ-ACCTINF-RTN  THRU 2100-EXIT
           END-PERFORM.
            GO TO  2000-EXIT.
       2000-EXIT.    EXIT.
      *-------------------------------------------------------*
       2100-READ-ACCTINF-RTN.

           SET NOT-GEN-OR-STMTINF  TO TRUE.

           READ ACCT-IN-FL AT END
                SET EOF-ACCTINF TO TRUE.

           IF ACCT-OR-FLG = 'Y'
              SET GEN-OR-STMTINF  TO TRUE.

       2100-EXIT.    EXIT.
      *-------------------------------------------------------*
       2200-READ-STMTINF-RTN.

           SET NOT-EOF-STMTINF TO TRUE.
           READ STMT-IN-FL NEXT AT END
                SET EOF-STMTINF  TO TRUE
                GO TO 2200-EXIT.
           IF STMT-IN-STAT NOT = '00' AND
              STMT-IN-STAT NOT = '10'
              DISPLAY '*** PROGRAM STMMCG07 ***'  UPON CONSOLE
              DISPLAY 'READ STMTINF IN ERROR,CODE = ' STMT-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'READ STMTINF IN ERROR,CODE = ' STMT-IN-STAT
              SET EOF-STMTINF TO TRUE
           END-IF.

           IF ACCT-NO NOT = STMT-IN-ACCT-KEY
              SET EOF-STMTINF       TO TRUE.

       2200-EXIT.    EXIT.
      *-------------------------------------------------------*
       2300-READ-DETLINF-RTN.

           SET NOT-FOUND-DETLINF TO TRUE

           READ DETL-IN-FL NEXT AT END
                SET EOF-DETLINF  TO TRUE
                GO TO 2300-EXIT.
           IF DETL-IO-STAT NOT = '00' AND
              DETL-IO-STAT NOT = '10'
              DISPLAY '*** PROGRAM STMMCG07 ***'  UPON CONSOLE
              DISPLAY 'READ DETLINF IN ERROR,CODE = ' DETL-IO-STAT
                                                  UPON CONSOLE
              DISPLAY 'READ DETLINF IN ERROR,CODE = ' DETL-IO-STAT
              SET EOF-DETLINF TO TRUE
           END-IF.

      **   IF STMT-IN-ACCT-KEY = DETL-BILL-AC-KEY AND
      **      STMT-IN-DR-AC6   = DETL-ACC-NO6-KEY
           IF STMT-IN-DESC-FOR = DETL-FOR-REF-KEY
              NEXT SENTENCE
           ELSE
              SET EOF-DETLINF       TO TRUE
           END-IF.

       2300-EXIT.    EXIT.
      *-------------------------------------------------------*
       3000-PROCESS-GEN-STMT-RTN.
           MOVE ACCT-NO   TO  STMT-IN-ACCT-KEY
           MOVE '000000'  TO  STMT-IN-SEQ-KEY
      **   DISPLAY 'START GEN-STMT: ' STMT-IN-ACCT-KEY,
      **                              STMT-IN-SEQ-KEY
           SET   NOT-EOF-STMTINF     TO TRUE
           START STMT-IN-FL KEY IS GREATER THAN STMT-CNTL-KEY
              INVALID KEY
              DISPLAY 'NO STATEMENT TRANSACTION FOR THIS A/C '
                       STMT-IN-ACCT-KEY
              SET NOT-FOUND-STMTINF TO TRUE
              GO TO 3000-EXIT.

           PERFORM 2200-READ-STMTINF-RTN  THRU 2200-EXIT
           PERFORM UNTIL EOF-STMTINF

      *    DISPLAY 'A/C - NO ' ACCT-NO ,':',ACCT-OR-FLG

              IF GEN-OR-STMTINF
                 AND STMT-IN-CHANNEL = 'FOR '
                 AND STMT-IN-DC-CODE = 'D '
                 AND STMT-IN-DESC-FCHK = 'F/O'
                 PERFORM 4000-PROCESS-GEN-DETL-STMT-RTN  THRU 4000-EXIT
              ELSE
                 PERFORM 5500-GEN-DATA-FOR-OUTPUT  THRU 5500-EXIT
                 PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
              END-IF
              PERFORM 2200-READ-STMTINF-RTN  THRU 2200-EXIT
           END-PERFORM.
       3000-EXIT.    EXIT.
      *-------------------------------------------------------*
       4000-PROCESS-GEN-DETL-STMT-RTN.

           MOVE STMT-IN-DESC-FOR TO  DETL-CNTL-KEY

           DISPLAY 'FIND FOR DETL: ' DETL-CNTL-KEY

           SET     NOT-EOF-DETLINF     TO TRUE
           SET     NOT-FOUND-DETLINF   TO TRUE
           START DETL-IN-FL KEY IS EQUAL DETL-CNTL-KEY
              INVALID KEY
              DISPLAY 'NO OUTWARD DETAIL STMT TRANSACTION FOR THIS A/C '
                       STMT-IN-ACCT-KEY
              SET NOT-FOUND-DETLINF TO TRUE
              MOVE  STMT-IN-DETL    TO STMT-OUT-REC
              MOVE  'A'             TO O-ADJUST-STATUS
              MOVE  'ERP033:PMT Debit Adjust Not found Key'
                                             TO O-ADJUST-DESC
              PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
              GO TO 4000-EXIT.

           PERFORM 2300-READ-DETLINF-RTN  THRU 2300-EXIT
           PERFORM UNTIL EOF-DETLINF

              IF  STMT-IN-DESC-FOR = DETL-FOR-REF-KEY
                  PERFORM 5250-GEN-DETL-DATA-FOR-OUTPUT  THRU 5250-EXIT
                  PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
                  SET  FOUND-DETLINF   TO TRUE
                  SET  EOF-DETLINF     TO TRUE
              ELSE
                  PERFORM 2300-READ-DETLINF-RTN  THRU 2300-EXIT
              END-IF
           END-PERFORM.
           IF EOF-DETLINF  AND NOT-FOUND-DETLINF
              MOVE  STMT-IN-DETL    TO STMT-OUT-REC
              MOVE  'A'             TO O-ADJUST-STATUS
              MOVE  'ERP033:PMT Debit Adjust Not found Key'
                                             TO O-ADJUST-DESC
              PERFORM 5000-WRITE-OUTPUT-STMT-RTN  THRU 5000-EXIT
           END-IF.
       4000-EXIT.    EXIT.
      *-------------------------------------------------------*
       5000-WRITE-OUTPUT-STMT-RTN.

           WRITE STMT-OUT-REC.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'         UPON CONSOLE
               DISPLAY 'WRITE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'WRITE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT.

       5000-EXIT.    EXIT.
      *-------------------------------------------------------*
       5250-GEN-DETL-DATA-FOR-OUTPUT.

           MOVE  STMT-IN-DETL         TO STMT-OUT-REC.
           MOVE  DETL-IN-FORCUSTID    TO O-RFT-COMP-ID.
           MOVE  DETL-IN-TRANDATE(4:2) TO O-PAY-TRANS-DATE(1:2).
           MOVE  '/'                   TO O-PAY-TRANS-DATE(3:1).
           MOVE  DETL-IN-TRANDATE(1:2) TO O-PAY-TRANS-DATE(4:2).
           MOVE  '/'                   TO O-PAY-TRANS-DATE(6:1).
           MOVE  DETL-IN-TRANDATE(9:2) TO O-PAY-TRANS-DATE(7:2).
           MOVE  '00:00:00'            TO O-PAY-TRANS-TIME.
           MOVE  STMT-IN-AMT           TO O-PAY-TRANS-AMT.
           MOVE  STMT-IN-DC-CODE(1:1)  TO O-PAY-ACTION-FLAG.
           MOVE  DETL-IN-FORREF       TO O-RFT-BATCH-REF.
           MOVE  DETL-IN-CUSTREF2     TO O-RFT-TRANS-REF.
           MOVE  'OR '                TO O-RFT-PROD-CODE.
     ***   MOVE  DETL-IN-VALDATE      TO O-RFT-VALUE-DATE.
           MOVE  DETL-IN-VALDATE(4:2) TO O-RFT-VALUE-DATE(1:2).
           MOVE  '/'                  TO O-RFT-VALUE-DATE(3:1).
           MOVE  DETL-IN-VALDATE(1:2) TO O-RFT-VALUE-DATE(4:2).
           MOVE  '/'                  TO O-RFT-VALUE-DATE(6:1).
           MOVE  DETL-IN-VALDATE(9:2) TO O-RFT-VALUE-DATE(7:2).
           MOVE  DETL-IN-DRACNO       TO O-RFT-DR-AC.
           MOVE  'THB'                TO O-RFT-DR-CCY.
           MOVE  STMT-IN-AMT          TO O-RFT-DR-AMT.
           MOVE  DETL-IN-RECAC        TO O-RFT-BENE-AC.
           MOVE  DETL-IN-REMAMT       TO O-RFT-PAY-AMT.
           MOVE  DETL-IN-REMAMT       TO O-RFT-NET-PAY-AMT.

           MOVE  'J'                  TO O-RFT-PROC-STAT.
           IF    DETL-IN-STATUS = 'SC'
                 MOVE  'I'            TO O-RFT-PROC-STAT.
           IF    DETL-IN-STATUS = 'CC'
                 MOVE  'C'            TO O-RFT-PROC-STAT.

           MOVE  DETL-IN-REMCUR       TO O-RFT-CR-CCY.
      ***  MOVE  DETL-IN-BENBANK      TO O-RFT-BENE-BNK-NME.
           MOVE  DETL-IN-BENBANKDET1    TO O-RFT-BENE-BNK-NME.
           MOVE  DETL-IN-CUSTREF2(1:20) TO O-RFT-CUST-REF-NO.
           MOVE  DETL-IN-RECNAME1  TO O-RFT-BENE-NME.
      ***  MOVE  DETL-IN-RECNAME2  TO O-RFT-BENE-NME(36:35).
           MOVE  DETL-IN-RECNAME1  TO O-PAY-PAYEE-NAME-EN.
      ***  MOVE  DETL-IN-RECNAME2  TO O-PAY-PAYEE-NAME-EN(36:35).
           MOVE  DETL-IN-TAXID        TO O-RFT-BENE-TAX-ID.
           MOVE  DETL-IN-CUSTREF1     TO O-RFT-TRANS-REMARK.
      ***  MOVE  DETL-DETL-REC(1:150) TO SOUT-XXXX-DETL.

       5250-EXIT.    EXIT.
      *-------------------------------------------------------*
       5500-GEN-DATA-FOR-OUTPUT.

           MOVE  STMT-IN-DETL    TO STMT-OUT-REC.
      ***  MOVE  SPACE           TO SOUT-XXXX-DETL.

       5500-EXIT.    EXIT.
      *-------------------------------------------------------*

       9999-CLOSE-RTN.
      *** CLOSE FILE
           CLOSE  ACCT-IN-FL.
           IF  ACC-PROF-STAT  NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'        UPON CONSOLE
               DISPLAY 'CLOSE ACCPROF ERROR,CODE  = '   ACC-PROF-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE ACCPROF ERROR,CODE  = '   ACC-PROF-STAT.

           CLOSE  STMT-IN-FL.
           IF  STMT-IN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'        UPON CONSOLE
               DISPLAY 'CLOSE STMTINF ERROR,CODE  = '    STMT-IN-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE STMTINF ERROR,CODE  = '    STMT-IN-STAT.

           CLOSE  DETL-IN-FL.
           IF  DETL-IO-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'        UPON CONSOLE
               DISPLAY 'CLOSE DETLINF ERROR,CODE  = '    DETL-IO-STAT
                                                         UPON CONSOLE
               DISPLAY 'CLOSE DETLINF ERROR,CODE  = '    DETL-IO-STAT.

           CLOSE  STMT-OUT-FL.
           IF  STMT-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'         UPON CONSOLE
               DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = '    STMT-OUT-STAT.

           CLOSE  PRTF-OUT-FL.
           IF  PRTF-OUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMMCG07 ***'         UPON CONSOLE
               DISPLAY 'CLOSE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT
                                                          UPON CONSOLE
               DISPLAY 'CLOSE PRTFOUTF ERROR,CODE  = '    PRTF-OUT-STAT.

           STOP   RUN.

       9999-EXIT.    EXIT.

      *-------------------- END PROGRAM -------------------*
/*
//LKED.SYSLIB   DD  DSN=&LIBPRFX..SCEELKED,DISP=SHR
//              DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD,DISP=SHR
//LKED.SYSLMOD  DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD(STMMCG07),DISP=SHR
//LKED.SYSPRINT DD  SYSOUT=X
/*
/*