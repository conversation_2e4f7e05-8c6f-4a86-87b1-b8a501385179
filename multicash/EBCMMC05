//EBCMMC05 JOB EBCMMC05,'EBCMMC05',MSGCLASS=X,CLASS=7,                  00010012
//         MSGLEVEL=(1,1),REGION=8M,COND=(8,LT)                         00020000
//*--------------------------------------------------------------------*00030015
//         EXEC BCMDATE1                                                00040015
//*--------------------------------------------------------------------*00580011
//*--  MERGE STMT. & BILL PAYMENT DETAIL                             --*00581011
//*    RUN AFTER JOB : EBCMBP01                                       -*00581111
//*    WAIT FOR FILE                                                  -*00581211
//*             PSPBCM.INF.BCM.T951.DRDBFILE.&DAYDATE.V1.TM1          -*00581311
//*--------------------------------------------------------------------*00581511
//STEP01   EXEC PGM=IEFBR14                                             00590002
//FILE001  DD DSN=PSPBCM.EB.BCM.P140.PS285.DRDBFILE.SORT,               00600022
//         SPACE=(CYL,(30,20),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)        00610001
/*                                                                      00620001
//STEP02   EXEC PGM=IEFBR14                                             00630002
//FILE001  DD DSN=PSPBCM.EB.BCM.P140.PS285.DRDBFILE.SORT,               00640022
//           DISP=(NEW,CATLG,DELETE),                                   00650001
//           DCB=(RECFM=FB,LRECL=285,BLKSIZE=0,DSORG=PS),               00660001
//           SPACE=(CYL,(30,20),RLSE),UNIT=3390                         00670001
/*                                                                      00680001
//STEP03   EXEC PGM=SORT                                                00690002
//SORTIN   DD DSN=PSPBCM.INF.BCM.T951.DRDBFILE.&DAYDATE.V1.TM1,DISP=SHR 00701022
//SORTOUT  DD DSN=PSPBCM.EB.BCM.P140.PS285.DRDBFILE.SORT,DISP=SHR       00710022
//SYSOUT   DD SYSOUT=X                                                  00720001
//SYSIN    DD *                                                         00730001
    SORT   FIELDS=(7,30,A),                                             00740001
           FORMAT=CH,EQUALS                                             00750001
    RECORD TYPE=F,LENGTH=285                                            00760001
    OPTION  EQUALS                                                      00770001
   END                                                                  00780001
/*                                                                      00790001
//STEP04   EXEC PGM=IEFBR14                                             01091202
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS285.DETBPAY,                    01092022
//         SPACE=(CYL,(30,20),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)        01093000
/*                                                                      01094000
//STEP05   EXEC PGM=IEFBR14                                             01095002
//FILE001  DD DSN=PSPBCM.BCM.BCM.P140.PS285.DETBPAY,                    01096022
//           DISP=(NEW,CATLG,DELETE),                                   01097000
//           DCB=(RECFM=FB,LRECL=285,BLKSIZE=0,DSORG=PS),               01098000
//           SPACE=(CYL,(30,20),RLSE),UNIT=3390                         01099000
/*                                                                      01099100
//*----------------------------------------------------------------*    01100000
//STEP06  EXEC PGM=STMBDD08                                             01110002
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  01120022
//STMTIN   DD DSN=PSPBCM.EB.BCM.P140.PS285.DRDBFILE.SORT,DISP=SHR       01130022
//ACCTIN   DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       01140022
//STMTOUT  DD DSN=PSPBCM.BCM.BCM.P140.PS285.DETBPAY,DISP=SHR            01150022
/*                                                                      01160000
//STEP07   EXEC PGM=IDCAMS,REGION=8M                                    01170003
//SYSPRINT DD SYSOUT=*                                                  01180003
//SYSIN    DD *                                                         01190003
   DELETE  PVBBCM.BCM.BCM.P140.PS285.DETBPAY -                          01200022
           PURGE                                                        01210003
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.P140.PS285.DETBPAY) -           01220022
           CYL(50 50)                                 -                 01230003
           RECSZ(285 285)                             -                 01240003
           VOL(JP2165 JP2166)                         -                 01250003
           INDEXED                                    -                 01260003
           KEYS(16 0))                                -                 01270004
   DATA    (NAME(PVBBCM.BCM.BCM.P140.PS285.DETBPAY.DATA)) -             01280022
   INDEX   (NAME(PVBBCM.BCM.BCM.P140.PS285.DETBPAY.INDEX))              01290022
/*                                                                      01300003
//STEP08   EXEC PGM=IDCAMS                                              01310003
//INPUT    DD DSN=PSPBCM.BCM.BCM.P140.PS285.DETBPAY,DISP=SHR            01320022
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.P140.PS285.DETBPAY,DISP=SHR            01330022
//SYSOUT   DD SYSOUT=X                                                  01340003
//SYSPRINT DD SYSOUT=X                                                  01350003
//SYSIN    DD *                                                         01360003
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 01370003
/*                                                                      01380003
//STEP09   EXEC PGM=IEFBR14                                             01420021
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS550.GENSTMT.DETL,                    01430022
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01440006
/*                                                                      01450006
//STEP10   EXEC PGM=IEFBR14                                             01460021
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS550.GENSTMT.DETL,                    01470022
//           DISP=(NEW,CATLG,DELETE),                                   01480006
//           DCB=(RECFM=FB,LRECL=550,BLKSIZE=0,DSORG=PS),               01490006
//           SPACE=(CYL,(50,100),RLSE),UNIT=3390                        01500006
/*                                                                      01510006
//STEP11   EXEC PGM=IEFBR14                                             01520021
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.BPAY,             01530022
//         SPACE=(TRK,(1,1),RLSE),UNIT=SYSDA,DISP=(MOD,DELETE)          01540006
/*                                                                      01550006
//STEP12   EXEC PGM=IEFBR14                                             01560021
//FILE001  DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.BPAY,             01570022
//           DISP=(NEW,CATLG,DELETE),                                   01580006
//           DCB=(RECFM=FB,LRECL=132,BLKSIZE=0,DSORG=PS),               01590006
//           SPACE=(CYL,(1,5),RLSE),UNIT=3390                           01600006
/*                                                                      01610006
//STEP13   EXEC PGM=STMMCG02                                            01620021
//STEPLIB  DD DSN=LIBRBCM.PRODENV.AP.BTCHLOAD,DISP=SHR                  01630023
//SYSCOUNT DD SYSOUT=X                                                  01640006
//SYSOUT   DD SYSOUT=X                                                  01650006
//SYSPRINT DD SYSOUT=X                                                  01660006
//*==================================================                   01670006
//ACCPROF  DD DSN=PSPBCM.BCM.BCM.P140.ERPACCT.MCASH.SORT,DISP=SHR       01680022
//STMTINF  DD DSN=PVBBCM.BCM.BCM.PS550.INTBDES.STMTBPDT,DISP=SHR        01690022
//DETLINF  DD DSN=PVBBCM.BCM.BCM.P140.PS285.DETBPAY,DISP=SHR            01700022
//STMTOUTF DD DSN=PSPBCM.BCM.BCM.PS550.GENSTMT.DETL,DISP=SHR            01710022
//PRTFOUTF DD DSN=PSPBCM.BCM.BCM.PS132.REPORT.GENSTMT.BPAY,DISP=SHR     01720022
/*                                                                      01730006
//STEP14   EXEC PGM=IDCAMS,REGION=8M                                    01760721
//SYSPRINT DD SYSOUT=*                                                  01760808
//SYSIN    DD *                                                         01760908
   DELETE  PVBBCM.BCM.BCM.PS550.GENSTMT.STMTBPDT -                      01761023
           PURGE                                                        01761108
   DEFINE  CLUSTER (NAME(PVBBCM.BCM.BCM.PS550.GENSTMT.STMTBPDT) -       01761222
           CYL(50 50)                                 -                 01761308
           RECSZ(550 550)                             -                 01761408
           VOL(JP2165 JP2166)                         -                 01761508
           INDEXED                                    -                 01761608
           KEYS(16 0))                                -                 01761708
   DATA    (NAME(PVBBCM.BCM.BCM.PS550.GENSTMT.STMTBPDT.DATA)) -         01761822
   INDEX   (NAME(PVBBCM.BCM.BCM.PS550.GENSTMT.STMTBPDT.INDEX))          01761922
/*                                                                      01762008
//STEP15   EXEC PGM=IDCAMS                                              01762121
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS550.GENSTMT.DETL,DISP=SHR            01762222
//OUTPUT   DD DSN=PVBBCM.BCM.BCM.PS550.GENSTMT.STMTBPDT,DISP=SHR        01762322
//SYSOUT   DD SYSOUT=X                                                  01762408
//SYSPRINT DD SYSOUT=X                                                  01762508
//SYSIN    DD *                                                         01762608
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 01762708
/*                                                                      01762808
//STEPBK   EXEC PGM=IDCAMS                                              01762920
//INPUT    DD DSN=PSPBCM.BCM.BCM.PS550.GENSTMT.DETL,DISP=SHR            01763022
//OUTPUT   DD DSN=PSPBCM.BCM.BCM.PS550.GSTMT.&DAYDATE.V1.TM2,           01763122
//         UNIT=3390,DISP=(NEW,CATLG),SPACE=(CYL,(50,100),RLSE),        01763218
//         DCB=(RECFM=FB,LRECL=550,BLKSIZE=0)                           01763318
//SYSOUT   DD SYSOUT=X                                                  01763418
//SYSPRINT DD SYSOUT=X                                                  01763518
//SYSIN    DD *                                                         01763618
  REPRO  INFILE(INPUT)  OUTFILE(OUTPUT)                                 01763718
/*                                                                      01763818
//                                                                      01763908
//                                                                      01764008
//                                                                      01764108
//                                                                      01764208
//                                                                      01764308