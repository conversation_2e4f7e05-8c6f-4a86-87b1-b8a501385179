//STMMCG01 JOB STMMCG01,'STMMCG01',<PERSON><PERSON><PERSON><PERSON>=X,CLASS=7,
//     NOTIFY=&SYSUID
//STEP01   EXEC IGYWCL
//SYSPRINT DD SYSOUT=X
//COBOL.SYSLIB DD DSN=&LIBPRFX..SCEESAMP,DISP=SHR
//             DD DSN=LIBRBCM.USERACC.AP.BTCHLOAD,DISP=SHR
//SYSIN    DD   *
      *------------------------*
       IDENTIFICATION DIVISION.
      *------------------------*
       PROGRAM-ID.   STMMCG01.
      *----------------------------------------------------------------*
      * PROGRAM NAME: STMMCG01
      * AUTHOR      : ITTICHOTE CH.
      * STARTED DATE: 01/02/2017
      * DESCRIPTION : THIS PROGRAM GENERATE OUTPUT FILE 525 BYTES
      *               INTERBANK STMT.(STMT. DESC.) 175 BYTE  +
      *               HISTORICAL STMT.(BILL PAYMENT ) 350 BYTE
      *----------------------------------------------------------------*
       ENVIRONMENT DIVISION.
      *---------------------*
       CONFIGURATION SECTION.
       SOURCE-COMPUTER. IBM-4341.
       OBJECT-COMPUTER. IBM-4341.
       SPECIAL-NAMES.   C01 IS  NEXT-PAGE.
      *----------------------*
       INPUT-OUTPUT SECTION.
      *----------------------*
       FILE-CONTROL.

      *THIS FILE IS DATE INPUT
      *     SELECT DATE-IN-FL ASSIGN TO DATEINFL.

      *** READ INTERBANK STMT (PS INPUT)
            SELECT INTB-IN-FL ASSIGN    TO  INTBINF
                   FILE STATUS          IS  INTB-IN-STAT.

      *** READ HISTORICAL STMT (VSAM INPUT)
            SELECT STMT-IN-FL ASSIGN    TO  STMTINF
                   ORGANIZATION         IS  INDEXED
                   ACCESS MODE          IS  DYNAMIC
                   RECORD KEY           IS  STMT-CNTL-REC
                   FILE STATUS          IS  STMT-IN-STAT.

      *** WRITE ALL STATEMENT (PS OUTPUT)
            SELECT STMT-OUT-FL ASSIGN   TO  STMTOUTF
                   FILE STATUS          IS  STMT-OUT-STAT.

      *** WRITE EXCEPTION REPORT (PS OUTPUT)
            SELECT XCPT-OUT-FL ASSIGN   TO  XCPTOUTF
                   FILE STATUS          IS  XCPT-OUT-STAT.

      *--------------*
       DATA DIVISION.
      *--------------*
       FILE SECTION.

       FD INTB-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   175 CHARACTERS
           DATA   RECORD     IS INTB-IN-REC.
       01 INTB-IN-REC.
          05 INTB-IN-REC-TYP           PIC  X(01).
          05 FILLER                    PIC  X(05).
          05 INTB-IN-ACCT-NO           PIC  X(10).
          05 FILLER                    PIC  X(15).
          05 INTB-IN-BAL               PIC  X(16).
          05 INTB-IN-BAL-SIGN          PIC  X(01).
          05 INTB-IN-AMT               PIC  X(15).
          05 INTB-IN-DC-CODE           PIC  X(02).
          05 FILLER                    PIC  X(47).
          05 INTB-IN-SEQ-NO            PIC  9(06).
          05 FILLER                    PIC  X(57).

       FD STMT-IN-FL
           RECORD CONTAINS   350 CHARACTERS
           DATA   RECORD     IS STMT-IN-REC.
       01 STMT-IN-REC.
          05 STMT-DETL-REC.
             10 STMT-BANK-CD             PIC X(02).
             10 FILLER                   PIC X(01).
             10 STMT-CURR-CD             PIC X(03).
             10 FILLER                   PIC X(01).
             10 STMT-ACCT                PIC 9(11).
             10 FILLER                   PIC X(01).
             10 STMT-R-DATE              PIC X(10).
             10 FILLER                   PIC X(01).
             10 STMT-SEQ-NO              PIC 9(09).
             10 FILLER                   PIC X(01).
             10 STMT-D-TRAN              PIC X(10).
             10 FILLER                   PIC X(01).
             10 STMT-D-DATE              PIC X(10).
             10 FILLER                   PIC X(01).
             10 STMT-D-TIME              PIC X(05).
             10 FILLER                   PIC X(01).
             10 STMT-T-CODE              PIC X(03).
             10 FILLER                   PIC X(01).
             10 STMT-CHANNEL             PIC X(04).
             10 FILLER                   PIC X(01).
             10 STMT-TR                  PIC X(02).
             10 FILLER                   PIC X(01).
             10 STMT-BR                  PIC X(04).
             10 FILLER                   PIC X(01).
             10 STMT-TERMINAL            PIC X(05).
             10 FILLER                   PIC X(01).
             10 STMT-CHQNO               PIC X(08).
             10 FILLER                   PIC X(01).
             10 STMT-AMT-S               PIC X(01).
             10 FILLER                   PIC X(01).
             10 STMT-AMT                 PIC X(16).
             10 FILLER                   PIC X(01).
             10 STMT-BAL                 PIC X(17).
             10 FILLER                   PIC X(01).
             10 STMT-DESC                PIC X(40).
             10 FILLER                   PIC X(23).
             10 FILLER                   PIC X(01).
             10 STMT-BR-OF-AC            PIC X(04).
             10 FILLER                   PIC X(01).
             10 STMT-ACC-TYP             PIC X(02).
             10 FILLER                   PIC X(01).
             10 STMT-CUST-NME            PIC X(50).
             10 FILLER                   PIC X(01).
             10 STMT-REF1                PIC X(20).
             10 FILLER                   PIC X(01).
             10 STMT-REF2                PIC X(20).
             10 FILLER                   PIC X(01).
             10 STMT-STATUS-MONEY        PIC X(03).
             10 FILLER                   PIC X(01).
             10 STMT-CH-CODE             PIC X(04).
             10 FILLER                   PIC X(01).
             10 STMT-AVL-BAL             PIC X(17).
             10 FILLER                   PIC X(01).
             10 FILLER                   PIC X(01).
             10 FILLER                   PIC X(03).
          05 STMT-CNTL-REC.
             10 STMT-KEY-ACCT-NO    PIC  X(10).
             10 STMT-KEY-SEQ        PIC  X(06).
          05 FILLER                 PIC  X(01).

       FD  STMT-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   525 CHARACTERS
           DATA   RECORD     IS STMT-OUT-REC.
       01  STMT-OUT-REC.
           05  INTB-OUT-DATA              PIC X(175).
           05  STMT-OUT-DATA              PIC X(350).

       FD  XCPT-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           DATA   RECORD     IS XCPT-OUT-REC.
       01  XCPT-OUT-REC               PIC X(132).

      *------------------------*
       WORKING-STORAGE SECTION.
      *------------------------*
       77 SW-OPENINFL                  PIC X(01)   VALUE 'Y'.
          88 ERR-OPENINFL                          VALUE 'N'.
          88 SUCCESS-OPENINFL                      VALUE 'Y'.
       77 SW-INTBINF                   PIC X(01)   VALUE 'N'.
          88 EOF-INTBINF                           VALUE 'Y'.
          88 NOT-EOF-INTBINF                       VALUE 'N'.
       77 SW-STMTINF-EOF               PIC X(01)   VALUE 'N'.
          88 EOF-STMTINF                           VALUE 'Y'.
          88 NOT-EOF-STMTINF                       VALUE 'N'.
       77 SW-STMTINF-FOUND             PIC X(01)   VALUE 'Y'.
          88 FOUND-STMTINF                         VALUE 'Y'.
          88 NOT-FOUND-STMTINF                     VALUE 'N'.
       77 SW-XCPTOUTF                  PIC X(01)   VALUE 'N'.
          88 FOUND-XCPTOUTF                        VALUE 'Y'.
          88 NOT-FOUND-XCPTOUTF                    VALUE 'N'.
       77 SW-XCPTOUTF-ERR              PIC X(01)   VALUE 'N'.
          88 ERROR-XCPTOUTF                        VALUE 'Y'.
          88 NOT-ERROR-XCPTOUTF                    VALUE 'N'.
      *------------------------------------------------------*

       01 WK-FILE-STATUS.
          05 INTB-IN-STAT           PIC  X(02).
          05 STMT-IN-STAT           PIC  X(02).
          05 STMT-OUT-STAT          PIC  X(02).
          05 XCPT-OUT-STAT          PIC  X(02).

       01  END-CONTROL1.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(11)   VALUE 'STMMCG01.C1'.
           03  FILLER              PIC  X(20)   VALUE SPACE.
           03  FILLER              PIC  X(49)   VALUE
           '               E N D   C O N T R O L             '.
           03  FILLER              PIC  X(17)   VALUE SPACE.
           03  FILLER              PIC  X(14)   VALUE SPACE.
           03  FILLER              PIC  X(11)   VALUE
           'PAGE      :'.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  END-CONTROL1-PAGE   PIC  X(08)   VALUE '       1'.
           03  FILLER              PIC  X(01)   VALUE SPACE.

       01  END-CONTROL2.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  FILLER              PIC  X(20)   VALUE SPACE.
           03  FILLER              PIC  X(49)   VALUE
           '   MATCHING INTERBANK STMT & HISTORICAL STMT.    '.
           03  FILLER              PIC  X(31)   VALUE SPACE.
           03  FILLER              PIC  X(11)   VALUE
           'RUN DATE  :'.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  END-CONTROL1-DATE   PIC  X(08)   VALUE SPACE.
           03  FILLER              PIC  X(01)   VALUE SPACE.

       01  END-CONTROL3.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  FILLER              PIC  X(08)   VALUE SPACE.
           03  FILLER              PIC  X(49)   VALUE SPACE.
           03  FILLER              PIC  X(17)   VALUE SPACE.
           03  FILLER              PIC  X(26)   VALUE SPACE.
           03  FILLER              PIC  X(11)   VALUE
           'RUN TIME  :'.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  END-CONTROL1-TIME   PIC  X(08)   VALUE SPACE.
           03  FILLER              PIC  X(01)   VALUE SPACE.

       01  END-CONTROL4.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(35)   VALUE SPACE.
           03  FILLER              PIC  X(47)   VALUE
           '     RECORDS                                   '.
           03  FILLER              PIC  X(47)   VALUE SPACE.
           03  FILLER              PIC  X(03)   VALUE SPACE.

       01  END-CONTROL5.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(14)   VALUE SPACE.
           03  FILLER              PIC  X(15)   VALUE
           'INPUT  INTBINF '.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  PRT-INP-INTBINF     PIC  ZZZ,ZZ9 VALUE ZERO.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  FILLER              PIC  X(07)   VALUE SPACE.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  FILLER              PIC  X(07)   VALUE SPACE.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  FILLER              PIC  X(07)   VALUE SPACE.
           03  FILLER              PIC  X(48)   VALUE SPACE.

       01  END-CONTROL-51.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(14)   VALUE SPACE.
           03  FILLER              PIC  X(15)   VALUE
           '         HEADER'.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  HEAD-INP            PIC  ZZZ,ZZ9 VALUE ZERO.
           03  FILLER              PIC  X(104)   VALUE SPACE.

       01  END-CONTROL-52.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(14)   VALUE SPACE.
           03  FILLER              PIC  X(15)   VALUE
           '         DETAIL'.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  DETAIL-INP          PIC  ZZZ,ZZ9 VALUE ZERO.
           03  FILLER              PIC  X(104)   VALUE SPACE.

       01  END-CONTROL-53.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(14)   VALUE SPACE.
           03  FILLER              PIC  X(15)   VALUE
           '         TAILER'.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  TAILER-INP          PIC  ZZZ,ZZ9 VALUE ZERO.
           03  FILLER              PIC  X(104)   VALUE SPACE.

       01  END-CONTROL-54.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(14)   VALUE SPACE.
           03  FILLER              PIC  X(15)   VALUE
           '       STMTINF '.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  PRT-INP-STMTINF     PIC  ZZZ,ZZ9 VALUE ZERO.
           03  FILLER              PIC  X(104)   VALUE SPACE.

       01  END-CONTROL6.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(14)   VALUE SPACE.
           03  FILLER              PIC  X(15)   VALUE
           'OUTPUT STMTOUTF'.
           03  FILLER              PIC  X(11)   VALUE SPACE.
           03  PRT-OUT-STMTOUTF    PIC  ZZZ,ZZ9 VALUE ZERO.
           03  FILLER              PIC  X(104)  VALUE SPACE.

       01  END-CONTROL7.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(83)   VALUE SPACE.
           03  FILLER              PIC  X(26)   VALUE
           'PROGRAMMED BY ITTICHOTE CH'.
           03  FILLER              PIC  X(23)   VALUE SPACE.

       01  END-CONTROL8.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(83)   VALUE SPACE.
           03  FILLER              PIC  X(26)   VALUE
           'PROGRAM-ID    STMMCG01    '.
           03  FILLER              PIC  X(23)   VALUE SPACE.

       01  END-CONTROL9.
           03  FILLER              PIC  X(01)   VALUE SPACE.
           03  FILLER              PIC  X(83)   VALUE SPACE.
           03  FILLER              PIC  X(26)   VALUE
           'DATE-WRITTEN  01/02/2017  '.
           03  FILLER              PIC  X(23)   VALUE SPACE.

       01 FOUND-STMT-REC            PIC 9(06)   VALUE ZEROS.
       01 NOT-FOUND-STMT-REC        PIC 9(06)   VALUE ZEROS.
       01 INPUT-STMT-REC            PIC 9(06)   VALUE ZEROS.
       01 INPUT-INTB-REC            PIC 9(06)   VALUE ZEROS.
       01 OUTPUT-STMT-REC           PIC 9(06)   VALUE ZEROS.
       01 HEADER-INTB-REC           PIC 9(06)   VALUE ZEROS.
       01 DETAIL-INTB-REC           PIC 9(06)   VALUE ZEROS.
       01 TAILER-INTB-REC           PIC 9(06)   VALUE ZEROS.
       01 INTB-IN-DET-SEQ           PIC 9(06)   VALUE ZEROS.
       01 CURRENT-DATE              PIC X(08)   VALUE SPACE.
       01 TIME-OF-DAY               PIC 9(06)   VALUE ZEROS.
       01 TODAY-DATE.
          03 TODAY-DATE-YYYY       PIC 9999.
          03 TODAY-DATE-MM         PIC 99.
          03 TODAY-DATE-DD         PIC 99.

       01 TODAY-TIME.
          03 TODAY-TIME-HH         PIC 99.
          03 TODAY-TIME-MM         PIC 99.
          03 TODAY-TIME-SS         PIC 99.

       01 TIME-FOR-END.
          05 RPT-T-HH     PIC XX.
          05 FILLER       PIC X VALUE ':'.
          05 RPT-T-MM     PIC XX.
          05 FILLER       PIC X VALUE ':'.
          05 RPT-T-SS     PIC XX.

       01 DATE-FOR-END.
          05 RPT-D-DD     PIC XX.
          05 FILLER       PIC X VALUE '/'.
          05 RPT-D-MM     PIC XX.
          05 FILLER       PIC X VALUE '/'.
          05 RPT-D-YY     PIC XX.

      *------------------------------------------------------*
       PROCEDURE DIVISION.
      *-------------------*
       000-MAIN-PROCESS.

           PERFORM  1000-OPENFL-RTN       THRU 1000-EXIT.
           PERFORM  2000-MAIN-PROCESS-RTN THRU 2000-EXIT.
           PERFORM  9999-CLOSEFL-RTN      THRU 9999-EXIT.
           STOP   RUN.

       000-EXIT.    EXIT.
      *-------------------------------------------------------*
       1000-OPENFL-RTN.

           SET SUCCESS-OPENINFL TO TRUE.

      *    OPEN INPUT DATE-IN-FL.

           OPEN INPUT INTB-IN-FL.
           IF INTB-IN-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCG01 ***'  UPON CONSOLE
              DISPLAY 'OPEN INTBINF IN ERROR,CODE = ' INTB-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'OPEN INTBINF IN ERROR,CODE = ' INTB-IN-STAT
              SET ERR-OPENINFL TO TRUE
           END-IF.

           OPEN INPUT STMT-IN-FL.
           DISPLAY 'OPEN STMTINF = ' STMT-IN-STAT
           IF STMT-IN-STAT NOT = '00' AND
              STMT-IN-STAT NOT = '35'
              DISPLAY '*** PROGRAM STMMCG01 ***'  UPON CONSOLE
              DISPLAY 'OPEN STMTINF IN ERROR,CODE = ' STMT-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'OPEN STMTINF IN ERROR,CODE = ' STMT-IN-STAT
              SET ERR-OPENINFL TO TRUE
           END-IF.

           OPEN OUTPUT  STMT-OUT-FL.
           IF STMT-OUT-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCG01 ***'      UPON CONSOLE
              DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
                                                      UPON CONSOLE
              DISPLAY 'OPEN STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
              SET ERR-OPENINFL TO TRUE
           END-IF.

           OPEN OUTPUT  XCPT-OUT-FL.
           IF XCPT-OUT-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCG01 ***'      UPON CONSOLE
              DISPLAY 'OPEN XCPTOUTF ERROR,CODE  = ' XCPT-OUT-STAT
                                                      UPON CONSOLE
              DISPLAY 'OPEN XCPTOUTF ERROR,CODE  = ' XCPT-OUT-STAT
              SET ERR-OPENINFL TO TRUE
           END-IF.

       1000-EXIT. EXIT.
      *-------------------------------------------------------*
       2000-MAIN-PROCESS-RTN.

           MOVE FUNCTION CURRENT-DATE(1:8) TO CURRENT-DATE.
           MOVE   CURRENT-DATE    TO   TODAY-DATE.
           ACCEPT TODAY-TIME      FROM TIME.

           PERFORM 2100-READ-INTBINF-RTN  THRU 2100-EXIT.
           PERFORM UNTIL EOF-INTBINF
              IF INTB-IN-REC-TYP = '1' OR '3'
                 PERFORM 2300-WRITE-STMTOUTF-RTN  THRU 2300-EXIT
                 PERFORM 2100-READ-INTBINF-RTN    THRU 2100-EXIT
              END-IF
              MOVE INTB-IN-ACCT-NO   TO STMT-KEY-ACCT-NO
              COMPUTE INTB-IN-DET-SEQ  = INTB-IN-SEQ-NO - 1
              MOVE INTB-IN-DET-SEQ   TO STMT-KEY-SEQ
              PERFORM 2200-READ-STMTINF-RTN  THRU 2200-EXIT
              PERFORM 2300-WRITE-STMTOUTF-RTN  THRU 2300-EXIT
              PERFORM 2100-READ-INTBINF-RTN  THRU 2100-EXIT
           END-PERFORM.

           PERFORM 2400-WRITE-XCPTOUTF-RTN  THRU 2400-EXIT.

       2000-EXIT. EXIT.
      *-------------------------------------------------------*
       2100-READ-INTBINF-RTN.

           READ INTB-IN-FL AT END
                SET EOF-INTBINF TO TRUE.
           IF NOT-EOF-INTBINF
              ADD 1               TO   INPUT-INTB-REC.

       2100-EXIT. EXIT.
      *-------------------------------------------------------*
       2200-READ-STMTINF-RTN.

           READ STMT-IN-FL
           END-READ.
           IF STMT-IN-STAT = '00'
              ADD  1                TO INPUT-STMT-REC
              SET FOUND-STMTINF     TO TRUE.
           IF STMT-IN-STAT = '23'
              SET NOT-FOUND-STMTINF TO TRUE.

       2200-EXIT. EXIT.
      *-------------------------------------------------------*
       2300-WRITE-STMTOUTF-RTN.

           MOVE SPACE        TO    STMT-OUT-REC.
           MOVE INTB-IN-REC  TO INTB-OUT-DATA
           IF INTB-IN-REC-TYP = '1'
              ADD   1               TO HEADER-INTB-REC
           END-IF.
           IF INTB-IN-REC-TYP = '3'
              IF NOT-EOF-INTBINF
                 ADD   1            TO TAILER-INTB-REC
              END-IF
           END-IF.
           IF INTB-IN-REC-TYP = '2'
              ADD   1               TO DETAIL-INTB-REC
              IF FOUND-STMTINF
                 MOVE STMT-IN-REC      TO STMT-OUT-DATA
                 ADD   1               TO FOUND-STMT-REC
              END-IF
              IF NOT-FOUND-STMTINF
                 DISPLAY 'NOT-FOUND-STMT : ' STMT-CNTL-REC
                 ADD   1               TO NOT-FOUND-STMT-REC
              END-IF
           END-IF.
           IF NOT-EOF-INTBINF
              ADD    1      TO   OUTPUT-STMT-REC
              WRITE  STMT-OUT-REC.

       2300-EXIT. EXIT.
      *-------------------------------------------------------*
       2400-WRITE-XCPTOUTF-RTN.

           MOVE  TODAY-DATE-DD         TO  RPT-D-DD.
           MOVE  TODAY-DATE-MM         TO  RPT-D-MM.
           MOVE  TODAY-DATE-YYYY(3:2)  TO  RPT-D-YY.
           MOVE  TODAY-TIME-HH         TO  RPT-T-HH.
           MOVE  TODAY-TIME-MM         TO  RPT-T-MM.
           MOVE  TODAY-TIME-SS         TO  RPT-T-SS.

           MOVE  DATE-FOR-END          TO  END-CONTROL1-DATE.
           MOVE  TIME-FOR-END          TO  END-CONTROL1-TIME.

           MOVE    '       1'          TO      END-CONTROL1-PAGE.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL1.
      *                                AFTER   NEW-PAGE.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL2
                                       AFTER   ADVANCING 2.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL3
                                       AFTER   ADVANCING 2.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL4
                                       AFTER   ADVANCING 2.
           MOVE    INPUT-INTB-REC      TO      PRT-INP-INTBINF.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL5
                                       AFTER   ADVANCING 2.
           MOVE    HEADER-INTB-REC     TO      HEAD-INP.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL-51
                                       AFTER   ADVANCING 2.
           MOVE    DETAIL-INTB-REC     TO      DETAIL-INP.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL-52
                                       AFTER   ADVANCING 2.
           MOVE    TAILER-INTB-REC     TO      TAILER-INP.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL-53
                                       AFTER   ADVANCING 2.
           MOVE    INPUT-STMT-REC      TO      PRT-INP-STMTINF.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL-54
                                       AFTER   ADVANCING 2.
           MOVE    OUTPUT-STMT-REC     TO      PRT-OUT-STMTOUTF.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL6
                                       AFTER   ADVANCING 2.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL7
                                       AFTER   ADVANCING 2.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL8
                                       AFTER   ADVANCING 2.
           WRITE   XCPT-OUT-REC        FROM    END-CONTROL9
                                       AFTER   ADVANCING 1.

       2400-EXIT. EXIT.
      *-------------------------------------------------------*
       9999-CLOSEFL-RTN.

      *** CLOSE FILE

           CLOSE INTB-IN-FL.
           IF INTB-IN-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCG01 ***'  UPON CONSOLE
              DISPLAY 'CLOSE INTBINF IN ERROR,CODE = ' INTB-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'CLOSE INTBINF IN ERROR,CODE = ' INTB-IN-STAT
           END-IF.

           CLOSE STMT-IN-FL.
           IF STMT-IN-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCG01 ***'  UPON CONSOLE
              DISPLAY 'CLOSE STMTINF IN ERROR,CODE = ' STMT-IN-STAT
                                                  UPON CONSOLE
              DISPLAY 'CLOSE STMTINF IN ERROR,CODE = ' STMT-IN-STAT
           END-IF.

           CLOSE STMT-OUT-FL.
           IF STMT-OUT-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCG01 ***'      UPON CONSOLE
              DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
                                                      UPON CONSOLE
              DISPLAY 'CLOSE STMTOUTF ERROR,CODE  = ' STMT-OUT-STAT
           END-IF.

           CLOSE XCPT-OUT-FL.
           IF XCPT-OUT-STAT NOT = '00'
              DISPLAY '*** PROGRAM STMMCG01 ***'      UPON CONSOLE
              DISPLAY 'CLOSE XCPTOUTF ERROR,CODE  = ' XCPT-OUT-STAT
                                                      UPON CONSOLE
              DISPLAY 'CLOSE XCPTOUTF ERROR,CODE  = ' XCPT-OUT-STAT
           END-IF.

       9999-EXIT.    EXIT.
      *-------------------- END PROGRAM -------------------*
/*
//LKED.SYSLIB   DD  DSN=&LIBPRFX..SCEELKED,DISP=SHR
//              DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD,DISP=SHR
//LKED.SYSLMOD  DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD(STMMCG01),DISP=SHR
//LKED.SYSPRINT DD  SYSOUT=X
/*