//STMBDD07 JOB STMBDD07,'STMBDD07',MSGCLASS=X,CLASS=A,
//     NOTIFY=&SYSUID
//STEP01   EXEC IGYWCL
//SYSPRINT DD   SYSOUT=X
//<PERSON><PERSON><PERSON><PERSON>    DD   *
      *------------------------*
       IDENTIFICATION DIVISION.
      *------------------------*
       PROGRAM-ID.   STMBDD07.
      *----------------------------------------------------------------*
      * PROGRAM NAME: STMBDD07
      * AUTHOR      : ITTICHOTE CH.
      * STARTED DATE: 31/01/2017
      * DESCRIPTION : THIS PROGRAM WILL READ ACCT NO FILE AND STMT FILE
      *               FROM FNF, FILTER ONLY BCM STMT AND WRITE FILE
      *               MODIFY PROGRAM FROM "STMBDD01"
      *----------------------------------------------------------------*
       ENVIRONMENT DIVISION.
      *---------------------*
       CONFIGURATION SECTION.
       SOURCE-COMPUTER. IBM-4341.
       OBJECT-COMPUTER. IBM-4341.
       SPECIAL-NAMES.   C01 IS  NEXT-PAGE.
      *----------------------*
       INPUT-OUTPUT SECTION.
      *----------------------*
       FILE-CONTROL.

      *** READ PS STMT IN FILE FROM SAFE
            SELECT STMT-IN-FL ASSIGN    TO  STMTIN
                   FILE STATUS          IS  STMTIN-STAT.

      *** READ SAM ACCOUNT NO FILE
            SELECT ACCT-IN-FL ASSIGN    TO  ACCTIN
                   FILE STATUS          IS  ACCTIN-STAT.

      *** WRITE PS STMT OUT
            SELECT STMT-OUT-FL ASSIGN   TO  STMTOUT
                   FILE STATUS          IS  STMTOUT-STAT.

      *--------------*
       DATA DIVISION.
      *--------------*
       FILE SECTION.

       FD  STMT-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   175 CHARACTERS
           DATA   RECORD     IS STMT-IN-REC.
       01  STMT-IN-REC.
           03  STMT-REC-TYPE            PIC X(01).
           03  FILLER                   PIC X(05).
           03  STMT-ACCT                PIC 9(10).
           03  FILLER                   PIC X(159).

       FD  ACCT-IN-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   200 CHARACTERS
           DATA   RECORD     IS ACCT-IN-REC.
       01  ACCT-IN-REC.
           05 FILLER               PIC X(16).
           05 ACCT-IN-ACCT-NO      PIC X(10).
           05 FILLER               PIC X(174).

       FD  STMT-OUT-FL
           LABEL  RECORDS    ARE STANDARD
           RECORD CONTAINS   175 CHARACTERS
           DATA   RECORD     IS STMT-OUT-REC.
       01  STMT-OUT-REC             PIC X(175).

      *------------------------*
       WORKING-STORAGE SECTION.
      *------------------------*
       77  I                             PIC 9(09) VALUE 0.
       77  I-FIND                        PIC 9(01) VALUE 0.
           88  ACCT-GREATER                        VALUE 1.
           88  ACCT-EQUAL                          VALUE 2.
       77  WK-NO                         PIC 9(09) VALUE 0.
       77  COMP-ACCT-10D                 PIC 9(10) VALUE 0.
       77  COMP-INFO-ACCT                PIC 9(10) VALUE 0.
      *------------------------------------------------------*
       01  EOF                     PIC  X(01)   VALUE 'N'.
       01  CNT-IN                  PIC  9(06)   VALUE  0.
       01  CNT-OUT                 PIC  9(06)   VALUE  0.
       01  CNT-INV                 PIC  9(06)   VALUE  0.
       01  WK-FILE-STATUS.
           03  STMTIN-STAT         PIC  X(02).
           03  ACCTIN-STAT         PIC  X(02).
           03  STMTOUT-STAT        PIC  X(02).
      *------------------------------------------------------*

       01  WK-ACCT-10D.
           03  WK-BB-10               PIC  9(03).
           03  WK-TT-10               PIC  9(01).
           03  WK-SS-10               PIC  9(05).
           03  WK-CHKD-10             PIC  9(01).

      *---------------------------------------------------------*
       PROCEDURE DIVISION.
      *-------------------*
       000-MAIN-PROCESS.

           PERFORM  100-INITIAL-RTN   THRU  100-EXIT.
           PERFORM  200-MAIN-PROCESS  THRU  200-EXIT
                    UNTIL EOF = 'Y'.
           PERFORM  999-CLOSE-RTN     THRU  999-EXIT.

       000-EXIT.    EXIT.
      *-------------------------------------------------------*
       100-INITIAL-RTN.

           OPEN INPUT  STMT-IN-FL.

           IF  STMTIN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'      UPON CONSOLE
               DISPLAY 'OPEN STMT FL IN ERROR,CODE  = ' STMTIN-STAT
                                                       UPON CONSOLE
               DISPLAY 'OPEN STMT FL IN ERROR,CODE  = ' STMTIN-STAT
               MOVE  'Y' TO  EOF
               GO TO  100-EXIT.

           OPEN INPUT ACCT-IN-FL.
           IF  ACCTIN-STAT   NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'     UPON CONSOLE
               DISPLAY 'OPEN ACCT FL IN ERROR,CODE  = ' ACCTIN-STAT
                                                      UPON CONSOLE
               DISPLAY 'OPEN ACCT FL IN ERROR,CODE  = ' ACCTIN-STAT
               MOVE  'Y' TO  EOF
               GO TO  100-EXIT.

           OPEN OUTPUT  STMT-OUT-FL.
           IF  STMTOUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'      UPON CONSOLE
               DISPLAY 'OPEN STMT FL OUT ERROR,CODE  = ' STMTOUT-STAT
                                                       UPON CONSOLE
               DISPLAY 'OPEN STMT FL OUT ERROR,CODE  = ' STMTOUT-STAT
               MOVE  'Y' TO  EOF
               GO TO  100-EXIT.

           MOVE ZEROES TO COMP-INFO-ACCT.
       100-EXIT.    EXIT.
      *-------------------------------------------------------*
       200-MAIN-PROCESS.

           MOVE 0 TO I-FIND.
           READ STMT-IN-FL
                AT END MOVE 'Y' TO EOF GO TO 200-EXIT.
           IF  STMTIN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'      UPON CONSOLE
               DISPLAY 'READ ACCT FL IN ERROR,CODE  = ' STMTIN-STAT
                                                       UPON CONSOLE
               DISPLAY 'READ ACCT FL IN ERROR,CODE  = ' STMTIN-STAT
               MOVE  'Y' TO  EOF
               GO TO  200-EXIT.
           ADD  1            TO CNT-IN.
           MOVE STMT-ACCT    TO WK-ACCT-10D.
           MOVE WK-ACCT-10D  TO COMP-ACCT-10D.
           EVALUATE TRUE
           WHEN COMP-ACCT-10D  < COMP-INFO-ACCT
                GO TO 200-EXIT
           WHEN COMP-ACCT-10D  = COMP-INFO-ACCT
                SET ACCT-EQUAL TO TRUE
           WHEN COMP-ACCT-10D  > COMP-INFO-ACCT
                PERFORM READ-ACCT-IN-FL UNTIL I-FIND > 0
           END-EVALUATE.
           IF  ACCT-GREATER
               GO TO 200-EXIT.
           MOVE STMT-IN-REC     TO STMT-OUT-REC.
           WRITE STMT-OUT-REC.
           IF  STMTOUT-STAT   NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'     UPON CONSOLE
               DISPLAY 'WRITE STMT FL ERROR,CODE  = ' STMTOUT-STAT
                                                      UPON CONSOLE
               DISPLAY 'WRITE STMT FL ERROR,CODE  = ' STMTOUT-STAT
               MOVE  'Y' TO  EOF
               GO TO  200-EXIT.

           ADD 1             TO CNT-OUT.
       200-EXIT.    EXIT.
      *-------------------------------------------------------*
       READ-ACCT-IN-FL.
           READ ACCT-IN-FL
                AT END GO TO 999-CLOSE-RTN.
           IF  ACCTIN-STAT   NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'     UPON CONSOLE
               DISPLAY 'READ ACCT FL IN ERROR,CODE  = ' ACCTIN-STAT
                                                      UPON CONSOLE
               DISPLAY 'READ ACCT FL IN ERROR,CODE  = ' ACCTIN-STAT
               GO TO  999-CLOSE-RTN.
           MOVE ACCT-IN-ACCT-NO TO WK-ACCT-10D.
           MOVE ACCT-IN-ACCT-NO TO COMP-INFO-ACCT.
           EVALUATE TRUE
           WHEN COMP-INFO-ACCT < COMP-ACCT-10D
                GO TO READ-ACCT-IN-FL
           WHEN COMP-INFO-ACCT = COMP-ACCT-10D
                SET ACCT-EQUAL TO TRUE
           WHEN COMP-INFO-ACCT > COMP-ACCT-10D
                SET ACCT-GREATER TO TRUE
           END-EVALUATE.
       READ-ACCT-IN-FL-EXIT. EXIT.
      *-------------------------------------------------------*
       999-CLOSE-RTN.

           DISPLAY  'TOTAL READ INPUT       =  ' CNT-IN.
           DISPLAY  'TOTAL WRITE OUTPUT     =  ' CNT-OUT.
      *** CLOSE FILE
           CLOSE  STMT-IN-FL.
           IF  STMTIN-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'       UPON CONSOLE
               DISPLAY 'CLOSE ACCT FL IN ERROR,CODE  = ' STMTIN-STAT
                                                        UPON CONSOLE
               DISPLAY 'CLOSE ACCT FL IN ERROR,CODE  = ' STMTIN-STAT.

           CLOSE  ACCT-IN-FL.
           IF  ACCTIN-STAT   NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'      UPON CONSOLE
               DISPLAY 'CLOSE ACCT IN ERROR,CODE  = ' ACCTIN-STAT
                                                       UPON CONSOLE
               DISPLAY 'CLOSE ACCT IN ERROR,CODE  = ' ACCTIN-STAT.

           CLOSE  STMT-OUT-FL.
           IF  STMTOUT-STAT NOT = '00'
               DISPLAY '*** PROGRAM STMBDD07 ***'       UPON CONSOLE
               DISPLAY 'CLOSE STMT FL OUT ERROR,CODE  = ' STMTOUT-STAT
                                                        UPON CONSOLE
               DISPLAY 'CLOSE STMT FL OUT ERROR,CODE  = ' STMTOUT-STAT.
           STOP   RUN.

       999-EXIT.    EXIT.

      *-------------------- END PROGRAM -------------------*
/*
//LKED.SYSLIB   DD  DSN=&LIBPRFX..SCEELKED,DISP=SHR
//              DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD,DISP=SHR
//LKED.SYSLMOD  DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD(STMBDD07),DISP=SHR
//LKED.SYSPRINT DD  SYSOUT=X
/*
/*