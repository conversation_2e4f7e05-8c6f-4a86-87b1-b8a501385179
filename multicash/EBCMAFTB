//EBCMAFTB JOB (********,********,TPB),
//         'CONTROLM',MSGCLASS=X,CLASS=O,REGION=1M
//*-------------------------------------------------------------*
//*   JOB DELETE & ADD CONTROL-M JOB TRIGGER                    *
//*   JOB  : EBCMMC09                                           *
//*   FROM FILE : PSPBCM.BCM.ERP.MCASH.STMT.&DAYDATE.V1.TM2     *
//*   TO   FILE : /SERVERDATA/BCM/                              *
//*               ERP_INTERBANK_EPPLCCBP_yyyymmdd_DAILY.txt     *
//*             : /xi_spool/PRD/GENERAL/STM/SCB_FTP_FILE/       *
//*               ERP_INTERBANK_EPPLCCBP.txt                    *
//*   CONDITION : EBCMMC09_ENDED_OK                             *
//*-------------------------------------------------------------*
//COND     EXEC  PGM=IOACND,REGION=4M
//STEPLIB  DD DISP=SHR,DSN=BMC.BASE.LOADLIB
//PRTDBG   DD SYSOUT=X
//SYSPRINT DD DUMMY
//SYSUDUMP DD SYSOUT=X
//DAPRINT  DD SYSOUT=X
//DAPARM   DD DISP=SHR,DSN=BMC.IOA.PARM
//         DD DISP=SHR,DSN=BMC.IOA.IOAENV
//DACNDIN  DD *
//         EXEC  BCMAFTBA
/*
//COND     EXEC  PGM=IOACND,REGION=4M
//STEPLIB  DD DISP=SHR,DSN=BMC.BASE.LOADLIB
//PRTDBG   DD SYSOUT=X
//SYSPRINT DD DUMMY
//SYSUDUMP DD SYSOUT=X
//DAPRINT  DD SYSOUT=X
//DAPARM   DD DISP=SHR,DSN=BMC.IOA.PARM
//         DD DISP=SHR,DSN=BMC.IOA.IOAENV
//DACNDIN  DD *
//         EXEC  BCMAFTBB
/*
//