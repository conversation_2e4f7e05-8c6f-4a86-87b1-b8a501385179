library 'lib-build-jenkinsk8s-docker@v5.6.0-update_argocd_yaml'
k8s_pod_build(
        //type of pod: docker, maven, nodejs
        pod_type: 'docker',
        pod_yaml: get_pod_template('docker', '23.0.1'),

        build_path: './',
        overide_command: 'docker build',
        email_receiver: '<EMAIL>',

        //*************Artifact/ Image Registry***********************
        registry_creds_id: 'harbor_nonprod',
        docker_registry: [
                url: 'harbordev.se.scb.co.th',
                image_name: 'ap2131-reportengine/ecr/report-engine-bizo-batch'
        ] 
)