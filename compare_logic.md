We have project multicast 2 project. the project  spring-boot-project is existing from cobol and we migrate manual to report-engine-bizo-batch.
1. spring-boot-project the project convert from COBOL to java spring boot. with Multicast_Report_Migration_Handbook_v2.md
   Step in cobol  
    Step 1 : Program
    EBCMMC01
    EBCMMC02 -> STMBDD06
    EBCMMC03 -> STMBDD07
    EBCMMC04 -> STMMCG01

    Step 2 : Program
    EBCMMC05 -> STMBDD08 , STMMCG02
    
    Step 3 : Program
    EBCMMC06 -> STMMCG03
    
    Step 4 : Program
    EBCMMC07 -> STMMCG04
    
    Step 5 : Program
    EBCMMC71 -> STMMCG06
    
    Step 6 : Program
    EBCMMC08 -> STMMCG05
    
    Step 7 : Program
    EBCMMC09 -> STMMCREF , STMMCG07
    
    Step 8 : Program
    EBCMAFTB

2. report-engine-bizo-batch the project is new project.
   step1 -> PrepareDataFunctionService.java
   step2 -> BillPaymentService.java
   step3 -> EppFunctionService.java
   step 4 ->LocalCollectTransactionService.java
   step 5 -> PromptPayService.java
   step 6 -> IpsFunctionService.java
   step 7 ->ForFcrFunctionService.java
   step 8 -> ReFormatService.java

We found code report-engine-bizo-batch project have some logic is missing or incorrect compare to spring-boot-project.

Please Ignore input file because existing code cobol is get source data from files. But report-engine-bizo-batch get source from database.

We need the output with summary and detail file case logic extract data, merge or transform, mapping field for output file is missing or incorrect.