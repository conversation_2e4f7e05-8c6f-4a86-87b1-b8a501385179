We have project multicast 2 project. The project cobol (/multicash) and we migrate manual to report-engine-bizo-batch.
1. Original cobol program in folder /multicash. with Multicast_Report_Migration_Handbook_v2.md
   Step in cobol  
    Step 1 : Program
    EBCMMC01
    EBCMMC02 -> STMBDD06
    EBCMMC03 -> STMBDD07
    EBCMMC04 -> STMMCG01

    Step 2 : Program
    EBCMMC05 -> STMBDD08 , STMMCG02
    
    Step 3 : Program
    EBCMMC06 -> STMMCG03
    
    Step 4 : Program
    EBCMMC07 -> STMMCG04
    
    Step 5 : Program
    EBCMMC71 -> STMMCG06
    
    Step 6 : Program
    EBCMMC08 -> STMMCG05
    
    Step 7 : Program
    EBCMMC09 -> STMMCREF , STMMCG07
    
    Step 8 : Program
    EBCMAFTB

2. report-engine-bizo-batch the project is new project.
   step1 -> PrepareDataFunctionService.java
   step2 -> BillPaymentService.java
   step3 -> EppFunctionService.java
   step 4 -> LocalCollectTransactionService.java
   step 5 -> PromptPayService.java
   step 6 -> IpsFunctionService.java
   step 7 -> ForFcrFunctionService.java
   step 8 -> ReFormatService.java

We found code report-engine-bizo-batch project have some logic is missing or incorrect compare to spring-boot-project.

Please Ignore input file because existing code cobol is get source data from files. But report-engine-bizo-batch get source from database.


Please compare 2 project keep spring-boot-project and cobol is main project. Compare to report-engine-bizo-batch project. For looking the extract data, merge or transform logic and mapping field is missing or incorrect with step by step.
Compare task as below:
- Logic sorting data.
- Logic merge data.
- Logic transform data.
- Logic filter statement data.
- Logic mapping field to output file.

Generate the output file "summary_compare_muticast.md" show the summary detail is missing or incorrect step by step and show code.