# Multicast Report System - COBOL to Java Spring Boot Migration Handbook

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [Migration Requirements Analysis](#migration-requirements-analysis)
3. [High-Level Architecture Design](#high-level-architecture-design)
4. [UML Diagrams and System Design](#uml-diagrams-and-system-design)
5. [Complete Class and Interface Inventory](#complete-class-and-interface-inventory)
6. [Business Logic Implementation Guide](#business-logic-implementation-guide)
7. [COBOL-to-Java Mapping Reference](#cobol-to-java-mapping-reference)
8. [Migration Strategy and Implementation Order](#migration-strategy-and-implementation-order)
9. [Automated Testing Strategy](#automated-testing-strategy)
10. [Quality Assurance and Validation](#quality-assurance-and-validation)
11. [Implementation Checklist](#implementation-checklist)

## Executive Summary

### Migration Scope and Objectives

This handbook provides comprehensive guidance for migrating a 22-file COBOL multicast report processing system to a modern Java Spring Boot 3.x library. The system processes banking statements through a 9-step workflow, handling various payment types including interbank transfers, bill payments, electronic payments, and real-time fund transfers.

**Migration Goals:**
- **100% Business Logic Preservation**: Maintain all original COBOL business rules and calculations
- **Modern Technology Stack**: Java 21, Spring Boot 3.x, NoSQL database integration
- **Platform-Agnostic Design**: Infrastructure-independent library suitable for any microservice
- **Enhanced Maintainability**: Clean, well-documented code following Java best practices
- **Comprehensive Testing**: Automated validation ensuring functional correctness

### System Overview

The multicast report system consists of:
- **9 Processing Steps** with defined dependencies and execution order
- **22 COBOL Programs** handling different aspects of report generation
- **Multiple Payment Types**: Interbank, bill payments, EPP, LCC, CN/IPS, RFT/PromptPay
- **Complex Data Flow**: Statement generation, merging, and output formatting

### Success Criteria
- ✅ All business logic from 22 COBOL files migrated and validated
- ✅ Platform-agnostic library deployable in any infrastructure
- ✅ Performance improvement over original COBOL system
- ✅ Comprehensive test coverage ensuring business rule compliance
- ✅ Clean, maintainable code following Spring Boot conventions

## Migration Requirements Analysis

### Technical Stack Requirements

#### Core Framework
```xml
<!-- Spring Boot 3.x with Java 21 -->
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.0</version>
</parent>

<properties>
    <java.version>21</java.version>
    <spring-boot.version>3.2.0</spring-boot.version>
</properties>

<dependencies>
    <!-- Core Spring Boot -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
    </dependency>
    
    <!-- NoSQL Database Support -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb</artifactId>
    </dependency>
    
    <!-- Validation -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    
    <!-- Testing -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
</dependencies>
```

#### Package Structure Requirements
```
com.scb.bizo/
├── report/                     # Report processing domain
│   ├── api/                    # Public API interfaces
│   ├── domain/                 # Core business logic
│   ├── service/                # Business services
│   ├── model/                  # Domain models
│   └── config/                 # Configuration
├── payment/                    # Payment processing domain
├── statement/                  # Statement processing domain
├── account/                    # Account management domain
└── common/                     # Shared utilities
```

### Business Logic Migration Requirements

#### Core Business Components to Migrate

**1. Report Processing Workflow (9 Steps)**
- Step 1: Account profile setup and VSAM file creation
- Step 2: Bill payment detail processing
- Step 3: Electronic payment processing (EPP)
- Step 4: Local clearing collection (LCC)
- Step 5: PromptPay transaction processing
- Step 6: CN/IPS payment processing
- Step 7: Statement generation and reference processing
- Step 8: Job orchestration
- Step 9: Final output generation

**2. Payment Processing Logic**
- Product code validation and mapping
- Transaction status management
- Amount processing and validation
- Payment type classification
- Business rule enforcement
- Exception handling and audit

**3. Statement Processing Logic**
- Statement generation algorithms
- Data merging and consolidation
- Format validation and conversion
- Reference data integration
- Output formatting and validation

#### Infrastructure Components to Exclude

**1. Mainframe-Specific Operations**
- VSAM file operations → Spring Data repositories
- JCL job control → Spring scheduling
- COBOL COPY books → Java POJOs
- Fixed-length record processing → Custom deserializers

**2. Platform Dependencies**
- Database connectivity → Spring Data abstraction
- File I/O operations → Spring Resource abstraction
- Job scheduling → Spring @Scheduled
- Error logging → SLF4J/Logback

## High-Level Architecture Design

### Target Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Report Processing Library                     │
│                      (com.scb.bizo)                             │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────────┐
│                  API Gateway Layer                              │
│  ├── ReportProcessingFacade                                     │
│  ├── PaymentProcessingFacade                                    │
│  ├── StatementGenerationFacade                                  │
│  └── AccountManagementFacade                                    │
├─────────────────────────────────────────────────────────────────┤
│                Service Orchestration Layer                      │
│  ├── ReportWorkflowService (9-Step Orchestration)              │
│  ├── PaymentProcessingService                                   │
│  ├── StatementProcessingService                                 │
│  └── AccountValidationService                                   │
├─────────────────────────────────────────────────────────────────┤
│                 Domain Services Layer                           │
│  ├── Step1Service (EBCMMC01-04)                                │
│  ├── Step2Service (EBCMMC05)                                   │
│  ├── Step3Service (EBCMMC06)                                   │
│  ├── Step4Service (EBCMMC07)                                   │
│  ├── Step5Service (EBCMMC71)                                   │
│  ├── Step6Service (EBCMMC08)                                   │
│  ├── Step7Service (EBCMMC09)                                   │
│  └── ValidationServices                                         │
├─────────────────────────────────────────────────────────────────┤
│                 Business Logic Layer                            │
│  ├── Payment Processing (STMMCG05 - Core Logic)                │
│  ├── Statement Processing (STMMCG01, STMMCREF, STMMCG07)       │
│  ├── Bill Payment Processing (STMMCG02, STMBDD08)              │
│  ├── Specialized Processors (STMMCG03, 04, 06)                 │
│  └── Account Management (STMBDD06, STMBDD07)                   │
├─────────────────────────────────────────────────────────────────┤
│                 Domain Models Layer                             │
│  ├── Payment Models                                             │
│  ├── Statement Models                                           │
│  ├── Account Models                                             │
│  └── Report Models                                              │
├─────────────────────────────────────────────────────────────────┤
│             Infrastructure Interfaces Layer                     │
│  ├── Repository Interfaces                                      │
│  ├── Event Publisher Interfaces                                 │
│  ├── External Service Interfaces                                │
│  └── Configuration Interfaces                                   │
└─────────────────────────────────────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────────┐
│              Infrastructure Implementation                       │
│        (Provided by Consumer Applications)                      │
│  ├── MongoDB Repositories                                       │
│  ├── Message Queue Integration                                  │
│  ├── External Service Clients                                   │
│  └── Configuration Management                                   │
└─────────────────────────────────────────────────────────────────┘
```

### Design Principles

#### 1. Hexagonal Architecture (Ports and Adapters)
```java
// Core business logic (inside hexagon)
@Service
public class ReportProcessingService {
    
    private final PaymentRepository paymentRepository;          // Port
    private final StatementRepository statementRepository;     // Port
    private final ReportEventPublisher eventPublisher;         // Port
    
    public ReportResult processReport(ReportRequest request) {
        // Pure business logic - no infrastructure dependencies
        return executeNineStepWorkflow(request);
    }
}

// Infrastructure adapter (outside hexagon)
@Component
public class MongoPaymentRepository implements PaymentRepository {
    // MongoDB-specific implementation
}
```

#### 2. Domain-Driven Design
```java
// Domain models encapsulate business rules
@Entity
public class MulticastReport {
    
    private ReportId reportId;
    private AccountNumber accountNumber;
    private ReportStatus status;
    private List<ProcessingStep> completedSteps;
    
    // Business logic methods
    public void processStep(StepType stepType) {
        validateStepTransition(stepType);
        executeStepBusinessLogic(stepType);
        updateStatus();
    }
    
    private void validateStepTransition(StepType stepType) {
        // Business rules for step execution order
    }
}
```

#### 3. Event-Driven Architecture
```java
// Events for step completion and workflow coordination
public record StepCompletedEvent(
    ReportId reportId,
    StepType completedStep,
    LocalDateTime completedAt,
    StepResult result
) implements DomainEvent {}

@EventListener
public void handleStepCompletion(StepCompletedEvent event) {
    // Trigger next step or finalize report
    workflowService.processNextStep(event.reportId());
}
```

### Separation of Concerns

#### Business Logic Layer
- **Report Processing Logic**: Step execution, validation, coordination
- **Payment Processing**: Transaction validation, status management, calculations
- **Statement Processing**: Generation, merging, formatting, validation
- **Account Management**: Profile validation, filtering, classification

#### Infrastructure Layer
- **Data Persistence**: Repository implementations, database operations
- **External Integration**: API clients, message queue integration
- **Configuration**: Environment-specific settings, feature flags
- **Monitoring**: Metrics, logging, health checks

## UML Diagrams and System Design

### High-Level System Sequence Diagram

```mermaid
sequenceDiagram
    participant Client
    participant ReportFacade
    participant WorkflowService
    participant Step1Service
    participant Step2Service
    participant PaymentService
    participant StatementService
    participant Repository
    participant EventPublisher

    Client->>ReportFacade: processMulticastReport(request)
    ReportFacade->>WorkflowService: executeWorkflow(request)
    
    WorkflowService->>Step1Service: processAccountProfiles()
    Step1Service->>Repository: saveAccountProfiles()
    Step1Service->>EventPublisher: publishStepCompleted(STEP1)
    
    WorkflowService->>Step2Service: processBillPayments()
    Step2Service->>PaymentService: validatePayments()
    Step2Service->>Repository: savePaymentData()
    Step2Service->>EventPublisher: publishStepCompleted(STEP2)
    
    WorkflowService->>StatementService: generateStatements()
    StatementService->>Repository: saveStatements()
    StatementService->>EventPublisher: publishStepCompleted(FINAL)
    
    WorkflowService-->>ReportFacade: ReportResult
    ReportFacade-->>Client: Report Generated
```

### Domain Model Class Diagram

```mermaid
classDiagram
    class MulticastReport {
        +ReportId reportId
        +AccountNumber accountNumber
        +ReportStatus status
        +LocalDateTime createdAt
        +List~ProcessingStep~ completedSteps
        +processStep(StepType)
        +validateStepTransition(StepType)
        +isComplete() boolean
    }
    
    class ProcessingStep {
        +StepType stepType
        +StepStatus status
        +LocalDateTime startTime
        +LocalDateTime endTime
        +StepResult result
        +execute()
        +validate()
    }
    
    class Payment {
        +PaymentId paymentId
        +AccountNumber accountNumber
        +ProductCode productCode
        +TransactionAmount amount
        +PaymentStatus status
        +processPayment()
        +validatePayment()
    }
    
    class Statement {
        +StatementId statementId
        +AccountNumber accountNumber
        +StatementType type
        +StatementContent content
        +generateStatement()
        +mergeStatements()
    }
    
    class AccountProfile {
        +AccountNumber accountNumber
        +AccountType accountType
        +ProductCode productCode
        +AccountStatus status
        +validateProfile()
        +filterByProfile()
    }
    
    MulticastReport ||--o{ ProcessingStep
    ProcessingStep ||--o{ Payment
    ProcessingStep ||--o{ Statement
    Payment }o--|| AccountProfile
    Statement }o--|| AccountProfile
```

### Report Processing Workflow Diagram

```mermaid
graph TD
    A[Start Report Processing] --> B{Validate Request}
    B -->|Invalid| C[Return Error]
    B -->|Valid| D[Step 1: Account Profiles]
    
    D --> E[EBCMMC01: ERP Processing]
    E --> F[EBCMMC02: Statement Processing]
    F --> G[EBCMMC03: Interbank Processing]
    G --> H[EBCMMC04: Statement Merging]
    
    H --> I[Step 2: Bill Payments]
    I --> J[EBCMMC05: Bill Payment Processing]
    
    J --> K[Step 3: EPP Processing]
    K --> L[EBCMMC06: EPP Details]
    
    L --> M[Step 4: LCC Processing]
    M --> N[EBCMMC07: LCC Details]
    
    N --> O[Step 5: PromptPay]
    O --> P[EBCMMC71: RFT Processing]
    
    P --> Q[Step 6: CN/IPS]
    Q --> R[EBCMMC08: Core Payment Processing]
    
    R --> S[Step 7: Statement Generation]
    S --> T[EBCMMC09: Reference Processing]
    
    T --> U[Step 8: Job Orchestration]
    U --> V[EBCMAFTB: Final Coordination]
    
    V --> W[Generate Final Report]
    W --> X[Publish Completion Event]
    X --> Y[End]
    
    style R fill:#ff9999
    style T fill:#99ff99
    style W fill:#9999ff
```

### Service Interaction Diagram

```mermaid
graph LR
    subgraph "API Layer"
        RF[ReportFacade]
        PF[PaymentFacade]
        SF[StatementFacade]
    end
    
    subgraph "Service Layer"
        WS[WorkflowService]
        PS[PaymentService]
        SS[StatementService]
        AS[AccountService]
    end
    
    subgraph "Domain Layer"
        PL[PaymentLogic]
        SL[StatementLogic]
        AL[AccountLogic]
        VL[ValidationLogic]
    end
    
    subgraph "Infrastructure"
        PR[PaymentRepo]
        SR[StatementRepo]
        AR[AccountRepo]
        EP[EventPublisher]
    end
    
    RF --> WS
    PF --> PS
    SF --> SS
    
    WS --> PS
    WS --> SS
    WS --> AS
    
    PS --> PL
    SS --> SL
    AS --> AL
    
    PL --> PR
    SL --> SR
    AL --> AR
    
    PS --> EP
    SS --> EP
    AS --> EP
```

## EBCMMC01 Migration Implementation Guide

### Business Logic Analysis

**EBCMMC01 Original Functionality:**
- **Job Purpose**: ERP Account Profile Processing (Step 1 of 9-step workflow)
- **Input**: ERP account data file (PSPBCM.ERP.BCM.P140.ERPACCT)
- **Processing**:
  - Sort by account number (positions 17-26, 10 characters)
  - Filter by multicash flag = 'Y' (position 151)
  - Create indexed structure with account number as key
- **Output**: Sorted, filtered account profiles in VSAM indexed file
- **Record Structure**: 200-character fixed-length records

**Key Business Rules:**
1. **Account Filtering**: Only process accounts with ACCT-MCASH-FLG = 'Y'
2. **Sorting Logic**: Sort by account number in ascending order
3. **Indexing**: Create indexed access by account number (10-character key)
4. **Data Integrity**: Preserve all account profile data for downstream processing

### Spring Boot Implementation Strategy

#### 1. Domain Model Design

```java
package com.scb.bizo.account.domain.model;

/**
 * Account Profile entity representing ERP account data from EBCMMC01.
 * Replaces VSAM indexed file with JPA entity and repository pattern.
 */
@Entity
@Table(name = "account_profiles", indexes = {
    @Index(name = "idx_account_number", columnList = "accountNumber"),
    @Index(name = "idx_multicash_flag", columnList = "multicashFlag")
})
@Document(collection = "account_profiles") // NoSQL support
public class AccountProfile {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;

    @Column(name = "account_number", length = 10, nullable = false, unique = true)
    @Indexed // MongoDB index
    private String accountNumber;

    @Column(name = "corp_id", length = 16)
    private String corporateId;

    @Column(name = "account_name", length = 50)
    private String accountName;

    @Column(name = "multicash_flag", length = 1)
    @Indexed
    private String multicashFlag;

    @Column(name = "payment_flag", length = 1)
    private String paymentFlag;

    @Column(name = "lcc_flag", length = 1)
    private String lccFlag;

    @Column(name = "bpay_flag", length = 1)
    private String billPaymentFlag;

    @Column(name = "ebpp_flag", length = 1)
    private String ebppFlag;

    @Column(name = "stmt1_freq", length = 3)
    private String statement1Frequency;

    @Column(name = "stmt1_ext", length = 20)
    private String statement1Extension;

    @Column(name = "stmt2_freq", length = 3)
    private String statement2Frequency;

    @Column(name = "payment_detail", length = 1)
    private String paymentDetail;

    @Column(name = "report_group", length = 10)
    private String reportGroup;

    @Column(name = "raw_record", length = 200)
    private String rawRecord; // Preserve original COBOL record

    @Column(name = "processed_date")
    private LocalDateTime processedDate;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "updated_date")
    private LocalDateTime updatedDate;

    /**
     * Business logic: Check if account is eligible for multicash processing.
     * Implements COBOL logic: ACCT-MCASH-FLG = 'Y'
     */
    public boolean isMulticashEligible() {
        return "Y".equals(multicashFlag);
    }

    /**
     * Business logic: Check if account supports payment processing.
     */
    public boolean isPaymentEnabled() {
        return "Y".equals(paymentFlag);
    }

    /**
     * Parse account profile from 200-character COBOL record.
     * Implements exact field positioning from EBCMMC01.
     */
    public static AccountProfile fromCobolRecord(String cobolRecord) {
        if (cobolRecord == null || cobolRecord.length() != 200) {
            throw new InvalidRecordFormatException("Record must be exactly 200 characters");
        }

        return AccountProfile.builder()
            .corporateId(cobolRecord.substring(0, 16).trim())
            .accountNumber(cobolRecord.substring(16, 26).trim())
            .accountName(cobolRecord.substring(29, 79).trim())
            .statement1Frequency(cobolRecord.substring(79, 82).trim())
            .statement1Extension(cobolRecord.substring(82, 102).trim())
            .statement2Frequency(cobolRecord.substring(102, 105).trim())
            .paymentDetail(cobolRecord.substring(105, 106).trim())
            .reportGroup(cobolRecord.substring(106, 116).trim())
            .multicashFlag(cobolRecord.substring(150, 151).trim())
            .paymentFlag(cobolRecord.substring(151, 152).trim())
            .lccFlag(cobolRecord.substring(152, 153).trim())
            .billPaymentFlag(cobolRecord.substring(153, 154).trim())
            .ebppFlag(cobolRecord.substring(154, 155).trim())
            .rawRecord(cobolRecord)
            .processedDate(LocalDateTime.now())
            .createdDate(LocalDateTime.now())
            .build();
    }

    /**
     * Convert to output format for downstream processing.
     */
    public String toOutputRecord() {
        // Format as required by subsequent steps
        return String.format("%-16s%-10s%-50s%s",
            corporateId, accountNumber, accountName, rawRecord.substring(79));
    }

    // Builder pattern, getters, setters, equals, hashCode, toString
}
```

#### 2. Repository Layer

```java
package com.scb.bizo.account.infrastructure.repository;

/**
 * Repository for account profile data access.
 * Replaces VSAM indexed file operations with modern data access patterns.
 */
@Repository
public interface AccountProfileRepository extends JpaRepository<AccountProfile, String> {

    /**
     * Find account by account number (replaces VSAM key access).
     */
    Optional<AccountProfile> findByAccountNumber(String accountNumber);

    /**
     * Find all multicash-eligible accounts (implements COBOL filtering logic).
     * Replaces: INCLUDE COND=(151,1,CH,EQ,C'Y')
     */
    @Query("SELECT a FROM AccountProfile a WHERE a.multicashFlag = 'Y' ORDER BY a.accountNumber")
    List<AccountProfile> findMulticashEligibleAccountsOrdered();

    /**
     * Find accounts by corporate ID and multicash flag.
     */
    List<AccountProfile> findByCorporateIdAndMulticashFlagOrderByAccountNumber(
        String corporateId, String multicashFlag);

    /**
     * Check if account exists and is multicash eligible.
     */
    @Query("SELECT COUNT(a) > 0 FROM AccountProfile a WHERE a.accountNumber = :accountNumber AND a.multicashFlag = 'Y'")
    boolean existsByAccountNumberAndMulticashEligible(@Param("accountNumber") String accountNumber);

    /**
     * Batch insert for performance (replaces COBOL batch processing).
     */
    @Modifying
    @Query(value = "INSERT INTO account_profiles (id, account_number, corp_id, account_name, multicash_flag, payment_flag, lcc_flag, bpay_flag, ebpp_flag, raw_record, processed_date, created_date) VALUES (:#{#profile.id}, :#{#profile.accountNumber}, :#{#profile.corporateId}, :#{#profile.accountName}, :#{#profile.multicashFlag}, :#{#profile.paymentFlag}, :#{#profile.lccFlag}, :#{#profile.billPaymentFlag}, :#{#profile.ebppFlag}, :#{#profile.rawRecord}, :#{#profile.processedDate}, :#{#profile.createdDate})", nativeQuery = true)
    void batchInsert(@Param("profile") AccountProfile profile);
}

/**
 * MongoDB repository for NoSQL environments.
 */
@Repository
public interface AccountProfileMongoRepository extends MongoRepository<AccountProfile, String> {

    List<AccountProfile> findByMulticashFlagOrderByAccountNumber(String multicashFlag);

    Optional<AccountProfile> findByAccountNumber(String accountNumber);

    @Query("{ 'multicashFlag': 'Y' }")
    List<AccountProfile> findMulticashEligibleAccounts(Sort sort);
}
```

#### 3. Service Layer Implementation

```java
package com.scb.bizo.account.domain.service;

/**
 * Account Profile Service implementing EBCMMC01 business logic.
 * Handles ERP account processing, filtering, and indexing.
 */
@Service
@Transactional
@Slf4j
public class AccountProfileService {

    private final AccountProfileRepository accountRepository;
    private final AccountProfileMongoRepository mongoRepository;
    private final AccountValidationService validationService;
    private final AccountEventPublisher eventPublisher;
    private final AccountProfileMapper mapper;

    /**
     * Main processing method implementing EBCMMC01 logic.
     * Replaces the complete JCL job functionality.
     */
    public AccountProcessingResult processErpAccountProfiles(List<String> erpRecords) {
        log.info("Starting ERP account profile processing for {} records", erpRecords.size());

        try {
            // Step 1: Parse and validate records (replaces COBOL record reading)
            List<AccountProfile> parsedProfiles = parseErpRecords(erpRecords);

            // Step 2: Filter multicash-eligible accounts (replaces INCLUDE COND)
            List<AccountProfile> eligibleProfiles = filterMulticashEligible(parsedProfiles);

            // Step 3: Sort by account number (replaces SORT FIELDS)
            List<AccountProfile> sortedProfiles = sortByAccountNumber(eligibleProfiles);

            // Step 4: Validate business rules
            ValidationResult validation = validateProfiles(sortedProfiles);
            if (!validation.isValid()) {
                throw new AccountValidationException(validation.getErrors());
            }

            // Step 5: Save to repository (replaces VSAM file creation)
            List<AccountProfile> savedProfiles = saveAccountProfiles(sortedProfiles);

            // Step 6: Publish completion event
            eventPublisher.publishAccountProcessingCompleted(savedProfiles.size());

            log.info("Successfully processed {} account profiles", savedProfiles.size());
            return AccountProcessingResult.success(savedProfiles);

        } catch (Exception e) {
            log.error("Error processing ERP account profiles", e);
            eventPublisher.publishAccountProcessingFailed(e.getMessage());
            return AccountProcessingResult.failed(e.getMessage());
        }
    }

    /**
     * Parse ERP records from 200-character COBOL format.
     * Implements exact field positioning from EBCMMC01.
     */
    private List<AccountProfile> parseErpRecords(List<String> erpRecords) {
        return erpRecords.stream()
            .filter(record -> record != null && record.length() == 200)
            .map(record -> {
                try {
                    return AccountProfile.fromCobolRecord(record);
                } catch (Exception e) {
                    log.warn("Failed to parse record: {}", record.substring(0, Math.min(50, record.length())), e);
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * Filter accounts with multicash flag = 'Y'.
     * Implements: INCLUDE COND=(151,1,CH,EQ,C'Y')
     */
    private List<AccountProfile> filterMulticashEligible(List<AccountProfile> profiles) {
        return profiles.stream()
            .filter(AccountProfile::isMulticashEligible)
            .collect(Collectors.toList());
    }

    /**
     * Sort accounts by account number in ascending order.
     * Implements: SORT FIELDS=(17,10,A)
     */
    private List<AccountProfile> sortByAccountNumber(List<AccountProfile> profiles) {
        return profiles.stream()
            .sorted(Comparator.comparing(AccountProfile::getAccountNumber))
            .collect(Collectors.toList());
    }

    /**
     * Validate account profiles according to business rules.
     */
    private ValidationResult validateProfiles(List<AccountProfile> profiles) {
        return validationService.validateAccountProfiles(profiles);
    }

    /**
     * Save account profiles to repository.
     * Implements VSAM file creation and indexing.
     */
    @Transactional
    private List<AccountProfile> saveAccountProfiles(List<AccountProfile> profiles) {
        // Clear existing data for reprocessing
        accountRepository.deleteAll();

        // Batch save for performance
        List<AccountProfile> savedProfiles = new ArrayList<>();
        int batchSize = 1000;

        for (int i = 0; i < profiles.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, profiles.size());
            List<AccountProfile> batch = profiles.subList(i, endIndex);
            savedProfiles.addAll(accountRepository.saveAll(batch));
        }

        return savedProfiles;
    }

    /**
     * Get account profile by account number (replaces VSAM key access).
     */
    public Optional<AccountProfile> getAccountProfile(String accountNumber) {
        return accountRepository.findByAccountNumber(accountNumber);
    }

    /**
     * Get all multicash-eligible accounts in sorted order.
     */
    public List<AccountProfile> getMulticashEligibleAccounts() {
        return accountRepository.findMulticashEligibleAccountsOrdered();
    }

    /**
     * Check if account is eligible for multicash processing.
     */
    public boolean isAccountMulticashEligible(String accountNumber) {
        return accountRepository.existsByAccountNumberAndMulticashEligible(accountNumber);
    }
}
```

## Complete Class and Interface Inventory

### API Layer Classes

#### 4. Batch Processing Implementation

```java
package com.scb.bizo.account.infrastructure.batch;

/**
 * Spring Batch job for processing ERP account profiles.
 * Replaces EBCMMC01 JCL job with modern batch processing.
 */
@Configuration
@EnableBatchProcessing
@Slf4j
public class AccountProfileBatchConfig {

    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private AccountProfileService accountProfileService;

    /**
     * Main batch job replacing EBCMMC01.
     */
    @Bean
    public Job accountProfileProcessingJob() {
        return jobBuilderFactory.get("accountProfileProcessingJob")
            .incrementer(new RunIdIncrementer())
            .start(accountProfileProcessingStep())
            .listener(new AccountProfileJobListener())
            .build();
    }

    /**
     * Processing step for account profiles.
     */
    @Bean
    public Step accountProfileProcessingStep() {
        return stepBuilderFactory.get("accountProfileProcessingStep")
            .<String, AccountProfile>chunk(1000)
            .reader(erpRecordReader())
            .processor(accountProfileProcessor())
            .writer(accountProfileWriter())
            .listener(new AccountProfileStepListener())
            .build();
    }

    /**
     * Reader for ERP records (replaces COBOL file input).
     */
    @Bean
    @StepScope
    public ItemReader<String> erpRecordReader() {
        return new FlatFileItemReaderBuilder<String>()
            .name("erpRecordReader")
            .resource(new ClassPathResource("data/erp-accounts.txt"))
            .lineMapper(new PassThroughLineMapper())
            .build();
    }

    /**
     * Processor for account profile transformation.
     */
    @Bean
    public ItemProcessor<String, AccountProfile> accountProfileProcessor() {
        return new ItemProcessor<String, AccountProfile>() {
            @Override
            public AccountProfile process(String record) throws Exception {
                try {
                    AccountProfile profile = AccountProfile.fromCobolRecord(record);

                    // Apply COBOL filtering logic
                    if (!profile.isMulticashEligible()) {
                        return null; // Skip non-multicash accounts
                    }

                    return profile;
                } catch (Exception e) {
                    log.warn("Failed to process record: {}", record.substring(0, Math.min(50, record.length())));
                    return null;
                }
            }
        };
    }

    /**
     * Writer for processed account profiles.
     */
    @Bean
    public ItemWriter<AccountProfile> accountProfileWriter() {
        return new RepositoryItemWriterBuilder<AccountProfile>()
            .repository(accountProfileService.getRepository())
            .methodName("save")
            .build();
    }
}

/**
 * Job execution listener for monitoring and events.
 */
@Component
@Slf4j
public class AccountProfileJobListener implements JobExecutionListener {

    @Autowired
    private AccountEventPublisher eventPublisher;

    @Override
    public void beforeJob(JobExecution jobExecution) {
        log.info("Starting EBCMMC01 account profile processing job");
        eventPublisher.publishJobStarted("EBCMMC01", jobExecution.getJobId());
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        if (jobExecution.getStatus() == BatchStatus.COMPLETED) {
            log.info("EBCMMC01 job completed successfully");
            eventPublisher.publishJobCompleted("EBCMMC01", jobExecution.getJobId());
        } else {
            log.error("EBCMMC01 job failed with status: {}", jobExecution.getStatus());
            eventPublisher.publishJobFailed("EBCMMC01", jobExecution.getJobId(),
                jobExecution.getAllFailureExceptions().toString());
        }
    }
}
```

#### 5. API Controller Implementation

```java
package com.scb.bizo.account.api.controller;

/**
 * REST API controller for account profile operations.
 * Provides modern API interface for EBCMMC01 functionality.
 */
@RestController
@RequestMapping("/api/v1/accounts")
@Validated
@Slf4j
public class AccountProfileController {

    private final AccountProfileService accountProfileService;
    private final AccountProfileBatchService batchService;

    /**
     * Process ERP account profiles (main EBCMMC01 functionality).
     */
    @PostMapping("/profiles/process")
    public ResponseEntity<AccountProcessingResult> processAccountProfiles(
            @RequestBody @Valid AccountProcessingRequest request) {

        log.info("Processing {} ERP account records", request.getRecords().size());

        AccountProcessingResult result = accountProfileService
            .processErpAccountProfiles(request.getRecords());

        if (result.isSuccess()) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * Start batch job for account profile processing.
     */
    @PostMapping("/profiles/batch")
    public ResponseEntity<BatchJobResult> startBatchProcessing(
            @RequestParam String inputFilePath) {

        BatchJobResult result = batchService.startAccountProfileJob(inputFilePath);
        return ResponseEntity.accepted().body(result);
    }

    /**
     * Get account profile by account number.
     */
    @GetMapping("/profiles/{accountNumber}")
    public ResponseEntity<AccountProfile> getAccountProfile(
            @PathVariable String accountNumber) {

        Optional<AccountProfile> profile = accountProfileService
            .getAccountProfile(accountNumber);

        return profile.map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get all multicash-eligible accounts.
     */
    @GetMapping("/profiles/multicash")
    public ResponseEntity<List<AccountProfile>> getMulticashAccounts() {
        List<AccountProfile> accounts = accountProfileService
            .getMulticashEligibleAccounts();
        return ResponseEntity.ok(accounts);
    }

    /**
     * Check if account is multicash eligible.
     */
    @GetMapping("/profiles/{accountNumber}/multicash-eligible")
    public ResponseEntity<Boolean> isMulticashEligible(
            @PathVariable String accountNumber) {

        boolean eligible = accountProfileService
            .isAccountMulticashEligible(accountNumber);
        return ResponseEntity.ok(eligible);
    }

    /**
     * Health check for account profile service.
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "AccountProfileService");
        health.put("timestamp", LocalDateTime.now());
        return ResponseEntity.ok(health);
    }
}
```

#### 1. Report Processing Facade
```java
package com.scb.bizo.report.api.facade;

#### 6. Testing Strategy for EBCMMC01 Migration

```java
package com.scb.bizo.account.domain.service;

/**
 * Comprehensive test suite for EBCMMC01 migration.
 * Validates business logic preservation and functional correctness.
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class AccountProfileServiceTest {

    @Autowired
    private AccountProfileService accountProfileService;

    @Autowired
    private AccountProfileRepository accountRepository;

    @MockBean
    private AccountEventPublisher eventPublisher;

    @Test
    @DisplayName("Should process ERP records exactly like EBCMMC01 COBOL job")
    void testErpAccountProcessing_CobolEquivalent() {
        // Given: Sample ERP records matching COBOL format
        List<String> erpRecords = Arrays.asList(
            createCobolRecord("CORP001", "**********", "Test Account 1", "Y", "Y", "N", "Y", "N"),
            createCobolRecord("CORP002", "**********", "Test Account 2", "Y", "N", "Y", "N", "Y"),
            createCobolRecord("CORP003", "**********", "Test Account 3", "N", "Y", "Y", "Y", "N"), // Should be filtered
            createCobolRecord("CORP004", "**********", "Test Account 4", "Y", "Y", "Y", "Y", "Y")
        );

        // When: Process records
        AccountProcessingResult result = accountProfileService.processErpAccountProfiles(erpRecords);

        // Then: Verify COBOL business logic
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getProcessedCount()).isEqualTo(3); // Only multicash='Y' accounts

        // Verify sorting by account number (COBOL SORT FIELDS logic)
        List<AccountProfile> savedAccounts = accountRepository.findMulticashEligibleAccountsOrdered();
        assertThat(savedAccounts).hasSize(3);
        assertThat(savedAccounts.get(0).getAccountNumber()).isEqualTo("**********");
        assertThat(savedAccounts.get(1).getAccountNumber()).isEqualTo("**********");
        assertThat(savedAccounts.get(2).getAccountNumber()).isEqualTo("**********");

        // Verify filtering logic (INCLUDE COND=(151,1,CH,EQ,C'Y'))
        savedAccounts.forEach(account ->
            assertThat(account.getMulticashFlag()).isEqualTo("Y"));
    }

    @Test
    @DisplayName("Should parse 200-character COBOL records correctly")
    void testCobolRecordParsing() {
        // Given: Exact COBOL record format
        String cobolRecord = createCobolRecord("TESTCORP12345678", "**********",
            "Sample Account Name", "Y", "Y", "N", "Y", "N");

        // When: Parse record
        AccountProfile profile = AccountProfile.fromCobolRecord(cobolRecord);

        // Then: Verify field extraction
        assertThat(profile.getCorporateId()).isEqualTo("TESTCORP12345678");
        assertThat(profile.getAccountNumber()).isEqualTo("**********");
        assertThat(profile.getAccountName()).isEqualTo("Sample Account Name");
        assertThat(profile.getMulticashFlag()).isEqualTo("Y");
        assertThat(profile.getPaymentFlag()).isEqualTo("Y");
        assertThat(profile.getLccFlag()).isEqualTo("N");
        assertThat(profile.getBillPaymentFlag()).isEqualTo("Y");
        assertThat(profile.getEbppFlag()).isEqualTo("N");
    }

    @Test
    @DisplayName("Should handle invalid records gracefully")
    void testInvalidRecordHandling() {
        // Given: Invalid records
        List<String> invalidRecords = Arrays.asList(
            "SHORT_RECORD", // Too short
            null, // Null record
            "", // Empty record
            "A".repeat(300) // Too long
        );

        // When: Process invalid records
        AccountProcessingResult result = accountProfileService.processErpAccountProfiles(invalidRecords);

        // Then: Should handle gracefully
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getProcessedCount()).isEqualTo(0);
        assertThat(accountRepository.count()).isEqualTo(0);
    }

    @Test
    @DisplayName("Should maintain VSAM-like indexed access performance")
    void testIndexedAccess() {
        // Given: Large dataset
        List<String> largeDataset = IntStream.range(1, 10001)
            .mapToObj(i -> createCobolRecord("CORP" + String.format("%012d", i),
                String.format("%010d", i), "Account " + i, "Y", "Y", "N", "Y", "N"))
            .collect(Collectors.toList());

        // When: Process and access by key
        accountProfileService.processErpAccountProfiles(largeDataset);

        long startTime = System.currentTimeMillis();
        Optional<AccountProfile> account = accountProfileService.getAccountProfile("**********");
        long endTime = System.currentTimeMillis();

        // Then: Should be fast like VSAM indexed access
        assertThat(account).isPresent();
        assertThat(endTime - startTime).isLessThan(100); // Sub-100ms access
        assertThat(account.get().getAccountNumber()).isEqualTo("**********");
    }

    private String createCobolRecord(String corpId, String accountNo, String accountName,
                                   String multicashFlag, String paymentFlag, String lccFlag,
                                   String bpayFlag, String ebppFlag) {
        StringBuilder record = new StringBuilder();
        record.append(String.format("%-16s", corpId)); // Corp ID (16 chars)
        record.append(String.format("%-10s", accountNo)); // Account Number (10 chars)
        record.append("   "); // Filler (3 chars)
        record.append(String.format("%-50s", accountName)); // Account Name (50 chars)
        record.append(" ".repeat(71)); // Statement details and filler (71 chars)
        record.append(multicashFlag); // Multicash flag (1 char) - position 151
        record.append(paymentFlag); // Payment flag (1 char)
        record.append(lccFlag); // LCC flag (1 char)
        record.append(bpayFlag); // Bill payment flag (1 char)
        record.append(ebppFlag); // EBPP flag (1 char)
        record.append(" ".repeat(45)); // Remaining filler (45 chars)

        return record.toString();
    }
}

/**
 * Integration test for batch processing.
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.batch.job.enabled=false"
})
class AccountProfileBatchTest {

    @Autowired
    private JobLauncherTestUtils jobLauncherTestUtils;

    @Autowired
    private AccountProfileRepository accountRepository;

    @Test
    @DisplayName("Should execute batch job like EBCMMC01 JCL")
    void testBatchJobExecution() throws Exception {
        // Given: Test data file
        createTestDataFile();

        // When: Execute batch job
        JobExecution jobExecution = jobLauncherTestUtils.launchJob();

        // Then: Verify job completion
        assertThat(jobExecution.getStatus()).isEqualTo(BatchStatus.COMPLETED);
        assertThat(accountRepository.count()).isGreaterThan(0);

        // Verify all saved accounts are multicash eligible
        List<AccountProfile> accounts = accountRepository.findAll();
        accounts.forEach(account ->
            assertThat(account.getMulticashFlag()).isEqualTo("Y"));
    }

    private void createTestDataFile() {
        // Create test ERP data file
    }
}
```

#### 7. Configuration and Infrastructure

```java
package com.scb.bizo.account.config;

/**
 * Configuration for account profile service.
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.scb.bizo.account.infrastructure.repository")
@EnableMongoRepositories(basePackages = "com.scb.bizo.account.infrastructure.repository")
public class AccountProfileConfig {

    /**
     * Account validation service configuration.
     */
    @Bean
    public AccountValidationService accountValidationService() {
        return new AccountValidationService();
    }

    /**
     * Event publisher for account processing events.
     */
    @Bean
    public AccountEventPublisher accountEventPublisher() {
        return new AccountEventPublisher();
    }

    /**
     * Mapper for account profile transformations.
     */
    @Bean
    public AccountProfileMapper accountProfileMapper() {
        return new AccountProfileMapper();
    }
}
```

/**
 * Main entry point for multicast report processing.
 * Orchestrates the 9-step workflow while maintaining business logic separation.
 */
@Component
@Validated
public class ReportProcessingFacade {
    
    private final ReportWorkflowService workflowService;
    private final ReportValidationService validationService;
    private final ReportEventPublisher eventPublisher;
    
    /**
     * Process complete multicast report following 9-step workflow.
     * Implements the main business process from the COBOL system.
     */
    public ReportResult processMulticastReport(@Valid ReportRequest request) {
        // Orchestrate 9-step workflow
    }
    
    /**
     * Process individual step of the workflow.
     * Allows for step-by-step processing and recovery.
     */
    public StepResult processStep(@Valid StepRequest request) {
        // Process individual step
    }
    
    /**
     * Validate report request without processing.
     * Useful for pre-validation and UI scenarios.
     */
    public ValidationResult validateReportRequest(@Valid ReportRequest request) {
        // Validate request
    }
    
    /**
     * Get report processing status and progress.
     */
    public ReportStatus getReportStatus(ReportId reportId) {
        // Get status
    }
}
```

#### 2. Payment Processing Facade
```java
package com.scb.bizo.payment.api.facade;

/**
 * Specialized facade for payment processing within reports.
 * Handles CN/IPS, bill payments, EPP, LCC, and RFT processing.
 */
@Component
@Validated
public class PaymentProcessingFacade {
    
    /**
     * Process CN/IPS payments (core logic from STMMCG05).
     */
    public PaymentResult processCnIpsPayment(@Valid CnIpsPaymentRequest request) {
        // Core payment processing
    }
    
    /**
     * Process bill payments with PromptPay support.
     */
    public BillPaymentResult processBillPayment(@Valid BillPaymentRequest request) {
        // Bill payment processing
    }
    
    /**
     * Process electronic payments (EPP).
     */
    public EppResult processEppPayment(@Valid EppPaymentRequest request) {
        // EPP processing
    }
    
    /**
     * Process local clearing collection (LCC).
     */
    public LccResult processLccPayment(@Valid LccPaymentRequest request) {
        // LCC processing
    }
    
    /**
     * Process real-time fund transfers (RFT/PromptPay).
     */
    public RftResult processRftPayment(@Valid RftPaymentRequest request) {
        // RFT processing
    }
}
```

#### 3. Statement Processing Facade
```java
package com.scb.bizo.statement.api.facade;

/**
 * Facade for statement generation and processing.
 * Handles statement creation, merging, and formatting.
 */
@Component
@Validated
public class StatementProcessingFacade {
    
    /**
     * Generate statements for account (from STMMCG01).
     */
    public StatementResult generateStatement(@Valid StatementRequest request) {
        // Statement generation
    }
    
    /**
     * Merge multiple statements (interbank and historical).
     */
    public MergeResult mergeStatements(@Valid MergeRequest request) {
        // Statement merging
    }
    
    /**
     * Process reference statements (from STMMCREF).
     */
    public ReferenceResult processReferenceStatement(@Valid ReferenceRequest request) {
        // Reference processing
    }
    
    /**
     * Generate outward payment statements (from STMMCG07).
     */
    public OutwardResult generateOutwardStatement(@Valid OutwardRequest request) {
        // Outward statement processing
    }
}
```

### Service Layer Classes

#### 1. Report Workflow Service
```java
package com.scb.bizo.report.service;

/**
 * Orchestrates the 9-step multicast report processing workflow.
 * Manages step dependencies, error handling, and progress tracking.
 */
@Service
@Transactional
public class ReportWorkflowService {
    
    private final Map<StepType, StepService> stepServices;
    private final ReportRepository reportRepository;
    private final ReportEventPublisher eventPublisher;
    
    /**
     * Execute complete 9-step workflow.
     */
    public ReportResult executeWorkflow(ReportRequest request) {
        MulticastReport report = initializeReport(request);
        
        try {
            for (StepType step : StepType.getOrderedSteps()) {
                StepResult result = processStep(report, step);
                report.completeStep(step, result);
                eventPublisher.publishStepCompleted(report.getId(), step, result);
            }
            
            report.markComplete();
            return ReportResult.success(report);
            
        } catch (StepProcessingException e) {
            report.markFailed(e);
            eventPublisher.publishReportFailed(report.getId(), e);
            return ReportResult.failed(e.getMessage());
        }
    }
    
    /**
     * Process individual workflow step.
     */
    public StepResult processStep(MulticastReport report, StepType stepType) {
        StepService stepService = stepServices.get(stepType);
        return stepService.processStep(report);
    }
    
    /**
     * Resume workflow from failed step.
     */
    public ReportResult resumeWorkflow(ReportId reportId, StepType fromStep) {
        // Resume processing logic
    }
    
    private MulticastReport initializeReport(ReportRequest request) {
        return MulticastReport.builder()
            .reportId(ReportId.generate())
            .accountNumber(request.getAccountNumber())
            .requestType(request.getType())
            .status(ReportStatus.INITIALIZING)
            .createdAt(LocalDateTime.now())
            .build();
    }
}
```

#### 2. Step Service Implementations
```java
package com.scb.bizo.report.service.step;

/**
 * Step 1 Service: Account Profile Processing
 * Replaces EBCMMC01, EBCMMC02, EBCMMC03, EBCMMC04
 */
@Service
public class Step1AccountProfileService implements StepService {
    
    private final AccountProfileProcessor accountProcessor;
    private final StatementProcessor statementProcessor;
    private final InterbankProcessor interbankProcessor;
    private final StatementMerger statementMerger;
    
    @Override
    public StepResult processStep(MulticastReport report) {
        try {
            // EBCMMC01: ERP account profile processing
            AccountProfile profile = accountProcessor.processErpProfile(report.getAccountNumber());
            
            // EBCMMC02: Statement processing with STMBDD06
            StatementData statements = statementProcessor.processStatements(profile);
            
            // EBCMMC03: Interbank statement processing with STMBDD07
            InterbankData interbank = interbankProcessor.processInterbank(statements);
            
            // EBCMMC04: Statement merging with STMMCG01
            MergedStatement merged = statementMerger.mergeStatements(statements, interbank);
            
            return StepResult.success(StepType.STEP1, merged);
            
        } catch (Exception e) {
            return StepResult.failed(StepType.STEP1, e.getMessage());
        }
    }
}

/**
 * Step 2 Service: Bill Payment Processing
 * Replaces EBCMMC05
 */
@Service
public class Step2BillPaymentService implements StepService {
    
    private final BillPaymentDetailProcessor detailProcessor;
    private final BillPaymentStatementGenerator statementGenerator;
    
    @Override
    public StepResult processStep(MulticastReport report) {
        try {
            // EBCMMC05: Bill payment processing with STMBDD08, STMMCG02
            BillPaymentDetails details = detailProcessor.processBillPaymentDetails(report);
            BillPaymentStatements statements = statementGenerator.generateStatements(details);
            
            return StepResult.success(StepType.STEP2, statements);
            
        } catch (Exception e) {
            return StepResult.failed(StepType.STEP2, e.getMessage());
        }
    }
}

/**
 * Step 6 Service: CN/IPS Processing (Most Critical)
 * Replaces EBCMMC08 with STMMCG05
 */
@Service
public class Step6CnIpsService implements StepService {
    
    private final CnIpsPaymentProcessor paymentProcessor;
    private final ProductCodeValidator productCodeValidator;
    private final PaymentStatusManager statusManager;
    
    @Override
    public StepResult processStep(MulticastReport report) {
        try {
            // EBCMMC08: CN/IPS processing with STMMCG05 (core logic)
            CnIpsPayments payments = paymentProcessor.processCnIpsPayments(report);
            
            // Apply product code validation and mapping
            payments.forEach(payment -> {
                productCodeValidator.validateAndMap(payment.getProductCode());
                statusManager.updateStatus(payment);
            });
            
            return StepResult.success(StepType.STEP6, payments);
            
        } catch (Exception e) {
            return StepResult.failed(StepType.STEP6, e.getMessage());
        }
    }
}
```

### Domain Model Classes

#### 1. Core Domain Models
```java
package com.scb.bizo.report.domain.model;

/**
 * Main aggregate root for multicast report processing.
 * Encapsulates the complete report lifecycle and business rules.
 */
@Entity
@Document(collection = "multicast_reports")
public class MulticastReport {
    
    @Id
    private ReportId reportId;
    private AccountNumber accountNumber;
    private ReportType reportType;
    private ReportStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime completedAt;
    private List<ProcessingStep> completedSteps;
    private Map<StepType, StepResult> stepResults;
    private List<ReportError> errors;
    
    /**
     * Process a workflow step with business validation.
     */
    public void processStep(StepType stepType, StepResult result) {
        validateStepTransition(stepType);
        
        ProcessingStep step = ProcessingStep.builder()
            .stepType(stepType)
            .status(StepStatus.COMPLETED)
            .startTime(LocalDateTime.now())
            .endTime(LocalDateTime.now())
            .result(result)
            .build();
            
        completedSteps.add(step);
        stepResults.put(stepType, result);
        
        updateReportStatus();
    }
    
    /**
     * Validate step execution order according to business rules.
     */
    private void validateStepTransition(StepType stepType) {
        Set<StepType> prerequisiteSteps = stepType.getPrerequisites();
        Set<StepType> completedStepTypes = completedSteps.stream()
            .map(ProcessingStep::getStepType)
            .collect(Collectors.toSet());
            
        if (!completedStepTypes.containsAll(prerequisiteSteps)) {
            throw new InvalidStepTransitionException(
                "Step " + stepType + " requires completion of: " + prerequisiteSteps);
        }
    }
    
    /**
     * Check if report processing is complete.
     */
    public boolean isComplete() {
        return completedSteps.size() == StepType.values().length &&
               status == ReportStatus.COMPLETED;
    }
    
    /**
     * Mark report as complete with final validation.
     */
    public void markComplete() {
        if (!isComplete()) {
            throw new IncompleteReportException("Not all steps completed");
        }
        
        this.status = ReportStatus.COMPLETED;
        this.completedAt = LocalDateTime.now();
    }
    
    /**
     * Handle processing failure with error details.
     */
    public void markFailed(Exception exception) {
        this.status = ReportStatus.FAILED;
        this.errors.add(ReportError.fromException(exception));
    }
    
    // Builder pattern, getters, equals, hashCode, toString
}
```

#### 2. Payment Domain Models
```java
package com.scb.bizo.payment.domain.model;

/**
 * Core payment model with business logic from STMMCG05.
 */
@Entity
@Document(collection = "payments")
public class Payment {
    
    @Id
    private PaymentId paymentId;
    private AccountNumber accountNumber;
    private ProductCode productCode;
    private TransactionAmount amount;
    private PaymentType paymentType;
    private PaymentStatus status;
    private String referenceNumber;
    private LocalDateTime processedDate;
    private LocalDateTime debitDate;
    private String statusCode;
    private Map<String, Object> additionalData;
    
    /**
     * Process payment with business validation from STMMCG05.
     */
    public PaymentResult process() {
        validatePaymentEligibility();
        applyBusinessRules();
        updateStatus(PaymentStatus.PROCESSING);
        
        try {
            executePaymentLogic();
            updateStatus(PaymentStatus.COMPLETED);
            return PaymentResult.success(this);
            
        } catch (PaymentProcessingException e) {
            updateStatus(PaymentStatus.FAILED);
            return PaymentResult.failed(e.getMessage());
        }
    }
    
    /**
     * Update payment status with COBOL business logic.
     * Implements status management from STMMCG05.
     */
    public void updateStatus(PaymentStatus newStatus) {
        validateStatusTransition(this.status, newStatus);
        this.status = newStatus;
        this.statusCode = newStatus.getCode();
    }
    
    /**
     * Handle cancellation with business rules from STMMCG05.
     * Status 'C' = Cancel Before Debit if no debit date
     * Status 'C' = Cancel After Debit if debit date exists
     * Status 'J' = Cancel After Debit
     */
    public void cancel(String cancellationCode) {
        if ("C".equals(cancellationCode)) {
            PaymentStatus cancelStatus = debitDate == null ? 
                PaymentStatus.CANCELLED_BEFORE_DEBIT : 
                PaymentStatus.CANCELLED_AFTER_DEBIT;
            updateStatus(cancelStatus);
        } else if ("J".equals(cancellationCode)) {
            updateStatus(PaymentStatus.CANCELLED_AFTER_DEBIT);
        } else {
            throw new InvalidCancellationCodeException("Invalid code: " + cancellationCode);
        }
    }
    
    private void validatePaymentEligibility() {
        // Business validation logic
    }
    
    private void applyBusinessRules() {
        // Apply COBOL business rules
    }
    
    private void executePaymentLogic() {
        // Core payment processing
    }
    
    private void validateStatusTransition(PaymentStatus from, PaymentStatus to) {
        // Status transition validation
    }
}

/**
 * Product code value object with validation from STMMCG05.
 */
@Embeddable
public class ProductCode {
    
    private String code;
    private String description;
    private ProductType type;
    
    /**
     * Create product code with COBOL mapping rules.
     * DCP → BNT mapping from STMMCG05.
     */
    public static ProductCode fromCobolCode(String cobolCode) {
        String mappedCode = mapCobolCode(cobolCode);
        return new ProductCode(mappedCode, getDescription(mappedCode), getType(mappedCode));
    }
    
    private static String mapCobolCode(String cobolCode) {
        Map<String, String> mapping = Map.of(
            "DCP", "BNT",
            "EWT", "EWT",
            "IPS", "IPS"
        );
        return mapping.getOrDefault(cobolCode, cobolCode);
    }
    
    /**
     * Validate product code according to COBOL rules.
     */
    public boolean isValid() {
        if (code == null || code.trim().isEmpty()) return false;
        
        // EWT pattern validation (EWT + 2 digits)
        if (code.startsWith("EWT")) {
            return code.matches("^EWT\\d{2}$");
        }
        
        // Standard product codes
        Set<String> validCodes = Set.of("BNT", "IPS", "LCC", "EPP", "RFT");
        return validCodes.contains(code);
    }
    
    // Constructor, getters, validation methods
}
```

#### 3. Statement Domain Models
```java
package com.scb.bizo.statement.domain.model;

/**
 * Statement model with business logic from STMMCG01, STMMCREF, STMMCG07.
 */
@Entity
@Document(collection = "statements")
public class Statement {
    
    @Id
    private StatementId statementId;
    private AccountNumber accountNumber;
    private StatementType statementType;
    private StatementFormat format;
    private String content;
    private Integer recordLength;
    private LocalDateTime generatedDate;
    private StatementStatus status;
    private List<StatementLine> lines;
    
    /**
     * Generate statement content with business formatting rules.
     */
    public void generateContent(List<Transaction> transactions) {
        StatementContentBuilder builder = new StatementContentBuilder(statementType);
        
        for (Transaction transaction : transactions) {
            StatementLine line = createStatementLine(transaction);
            lines.add(line);
            builder.addLine(line);
        }
        
        this.content = builder.build();
        this.recordLength = content.length();
        this.status = StatementStatus.GENERATED;
        this.generatedDate = LocalDateTime.now();
    }
    
    /**
     * Merge with other statements following business rules from STMMCG01.
     */
    public Statement mergeWith(List<Statement> otherStatements) {
        StatementMerger merger = new StatementMerger(this.statementType);
        List<Statement> allStatements = new ArrayList<>();
        allStatements.add(this);
        allStatements.addAll(otherStatements);
        
        return merger.merge(allStatements);
    }
    
    /**
     * Validate statement format and content.
     */
    public ValidationResult validate() {
        StatementValidator validator = new StatementValidator(statementType);
        return validator.validate(this);
    }
    
    private StatementLine createStatementLine(Transaction transaction) {
        return StatementLineFactory.createLine(transaction, statementType);
    }
}

/**
 * Reference statement model from STMMCREF.
 */
@Entity
@Document(collection = "reference_statements")
public class ReferenceStatement extends Statement {
    
    private String imStNameLookup;
    private List<StatementSection> sections;
    private Map<String, String> referenceData;
    
    /**
     * Process 3200-character input record from STMMCREF.
     */
    public void processInputRecord(String inputRecord) {
        if (inputRecord.length() != 3200) {
            throw new InvalidRecordLengthException("Expected 3200 characters, got " + inputRecord.length());
        }
        
        // Parse sections: payment, EPP, LCC, RFT
        sections = parseRecordSections(inputRecord);
        
        // Process IM/ST name lookup
        imStNameLookup = extractImStNameLookup(inputRecord);
        
        // Handle EWT product code references
        processEwtProductCodeReferences(inputRecord);
        
        generateReferenceContent();
    }
    
    private List<StatementSection> parseRecordSections(String record) {
        // Parse 3200-character record into sections
        return List.of(
            parsePaymentSection(record.substring(0, 800)),
            parseEppSection(record.substring(800, 1600)),
            parseLccSection(record.substring(1600, 2400)),
            parseRftSection(record.substring(2400, 3200))
        );
    }
    
    private void generateReferenceContent() {
        // Generate reference statement content
    }
}
```

### Business Logic Implementation Classes

#### 1. Core Payment Processing Logic (STMMCG05)
```java
package com.scb.bizo.payment.domain.service;

/**
 * Core payment processing service implementing STMMCG05 business logic.
 * This is the most critical component in the migration.
 */
@Service
public class CnIpsPaymentProcessor {
    
    private final ProductCodeValidator productCodeValidator;
    private final AmountProcessor amountProcessor;
    private final PaymentStatusManager statusManager;
    private final PaymentValidator paymentValidator;
    private final PaymentRepository paymentRepository;
    
    /**
     * Process CN/IPS payment with complete STMMCG05 business logic.
     */
    public Payment processCnIpsPayment(CnIpsPaymentRequest request) {
        // Step 1: Validate and map product code (critical COBOL logic)
        ProductCode validatedProductCode = productCodeValidator.validateAndMap(request.getProductCode());
        
        // Step 2: Process and validate amount
        TransactionAmount processedAmount = amountProcessor.processAmount(request.getAmount());
        
        // Step 3: Create payment entity
        Payment payment = Payment.builder()
            .paymentId(PaymentId.generate())
            .accountNumber(request.getAccountNumber())
            .productCode(validatedProductCode)
            .amount(processedAmount)
            .paymentType(PaymentType.CN_IPS)
            .status(PaymentStatus.PENDING)
            .referenceNumber(generateReferenceNumber())
            .processedDate(LocalDateTime.now())
            .build();
        
        // Step 4: Apply business validation
        ValidationResult validation = paymentValidator.validate(payment);
        if (!validation.isValid()) {
            throw new PaymentValidationException(validation.getErrors());
        }
        
        // Step 5: Process payment with status management
        payment.process();
        
        // Step 6: Handle status updates (COBOL status logic)
        statusManager.applyStatusRules(payment);
        
        // Step 7: Save payment
        return paymentRepository.save(payment);
    }
    
    /**
     * Handle payment status updates with COBOL logic from STMMCG05.
     */
    public void updatePaymentStatus(PaymentId paymentId, String statusCode) {
        Payment payment = paymentRepository.findById(paymentId)
            .orElseThrow(() -> new PaymentNotFoundException(paymentId));
        
        // Apply COBOL status management rules
        switch (statusCode) {
            case "C" -> handleCancellation(payment);
            case "J" -> handleCancellationAfterDebit(payment);
            case "P" -> payment.updateStatus(PaymentStatus.PENDING);
            case "S" -> payment.updateStatus(PaymentStatus.SUCCESSFUL);
            default -> throw new InvalidStatusCodeException("Invalid status: " + statusCode);
        }
        
        paymentRepository.save(payment);
    }
    
    private void handleCancellation(Payment payment) {
        // COBOL logic: 'C' status handling based on debit date
        if (payment.getDebitDate() == null) {
            payment.updateStatus(PaymentStatus.CANCELLED_BEFORE_DEBIT);
        } else {
            payment.updateStatus(PaymentStatus.CANCELLED_AFTER_DEBIT);
        }
    }
    
    private void handleCancellationAfterDebit(Payment payment) {
        // COBOL logic: 'J' status always means cancelled after debit
        payment.updateStatus(PaymentStatus.CANCELLED_AFTER_DEBIT);
    }
    
    private String generateReferenceNumber() {
        return "CN" + System.currentTimeMillis();
    }
}
```

#### 2. Product Code Validation (STMMCG05)
```java
package com.scb.bizo.payment.domain.service;

/**
 * Product code validator implementing exact COBOL logic from STMMCG05.
 */
@Component
public class ProductCodeValidator {
    
    // COBOL mapping rules from STMMCG05
    private static final Map<String, String> COBOL_PRODUCT_MAPPING = Map.of(
        "DCP", "BNT",  // Critical mapping from COBOL
        "EWT", "EWT",
        "IPS", "IPS"
    );
    
    private static final Pattern EWT_PATTERN = Pattern.compile("^EWT\\d{2}$");
    private static final Set<String> VALID_PRODUCT_CODES = Set.of(
        "BNT", "EWT", "IPS", "LCC", "EPP", "RFT"
    );
    
    /**
     * Validate and map product code according to STMMCG05 rules.
     */
    public ProductCode validateAndMap(String inputCode) {
        if (inputCode == null || inputCode.trim().isEmpty()) {
            throw new InvalidProductCodeException("Product code cannot be null or empty");
        }
        
        String trimmedCode = inputCode.trim().toUpperCase();
        
        // Apply COBOL mapping first
        String mappedCode = COBOL_PRODUCT_MAPPING.getOrDefault(trimmedCode, trimmedCode);
        
        // Validate mapped code
        if (!isValidProductCode(mappedCode)) {
            throw new InvalidProductCodeException("Invalid product code: " + inputCode);
        }
        
        return ProductCode.builder()
            .code(mappedCode)
            .originalCode(trimmedCode)
            .description(getProductDescription(mappedCode))
            .type(getProductType(mappedCode))
            .build();
    }
    
    /**
     * Validate product code according to COBOL business rules.
     */
    public boolean isValidProductCode(String productCode) {
        if (productCode == null || productCode.trim().isEmpty()) {
            return false;
        }
        
        String code = productCode.trim().toUpperCase();
        
        // Check standard valid codes
        if (VALID_PRODUCT_CODES.contains(code)) {
            return true;
        }
        
        // Check EWT pattern (COBOL business rule)
        if (EWT_PATTERN.matcher(code).matches()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if product code requires special processing.
     */
    public boolean requiresSpecialProcessing(String productCode) {
        Set<String> specialCodes = Set.of("EWT", "IPS", "RFT");
        return specialCodes.contains(productCode);
    }
    
    private String getProductDescription(String code) {
        Map<String, String> descriptions = Map.of(
            "BNT", "Bank Note Transfer",
            "EWT", "Electronic Wire Transfer",
            "IPS", "Interbank Payment System",
            "LCC", "Local Clearing Collection",
            "EPP", "Electronic Payment Processing",
            "RFT", "Real-time Fund Transfer"
        );
        return descriptions.getOrDefault(code, "Unknown Product");
    }
    
    private ProductType getProductType(String code) {
        return switch (code) {
            case "BNT", "IPS" -> ProductType.INTERBANK;
            case "EWT" -> ProductType.WIRE_TRANSFER;
            case "LCC" -> ProductType.LOCAL_CLEARING;
            case "EPP" -> ProductType.ELECTRONIC_PAYMENT;
            case "RFT" -> ProductType.REAL_TIME_TRANSFER;
            default -> ProductType.UNKNOWN;
        };
    }
}
```

## Business Logic Implementation Guide

### Critical Business Logic Components

#### 1. STMMCG05 - Core Payment Processing
This is the most critical component requiring exact COBOL logic preservation.

**Key Business Rules:**
- Product code mapping: DCP → BNT
- EWT pattern validation: EWT + 2 digits
- Status management: 'C', 'J' status handling
- Amount processing: COBOL decimal format conversion
- Exception handling: Business rule violations

**Implementation Priority: CRITICAL - Implement First**

```java
/**
 * Core business logic from STMMCG05 - highest priority for migration.
 */
@Service
public class CorePaymentBusinessLogic {
    
    /**
     * Main payment processing method implementing STMMCG05 logic.
     */
    public PaymentResult processPayment(PaymentRequest request) {
        // 1. Product code validation and mapping (critical)
        ProductCode productCode = validateProductCode(request.getProductCode());
        
        // 2. Amount processing (financial accuracy critical)
        TransactionAmount amount = processAmount(request.getAmount());
        
        // 3. Status management (business flow critical)
        PaymentStatus initialStatus = determineInitialStatus(request);
        
        // 4. Business rule validation
        validateBusinessRules(productCode, amount, request.getAccountNumber());
        
        // 5. Create and process payment
        Payment payment = createPayment(request, productCode, amount, initialStatus);
        
        return executePaymentProcessing(payment);
    }
    
    private ProductCode validateProductCode(String code) {
        // Exact COBOL mapping logic
        return switch (code.toUpperCase()) {
            case "DCP" -> new ProductCode("BNT", "Bank Note Transfer");
            case "EWT" -> validateEwtCode(code);
            case "IPS" -> new ProductCode("IPS", "Interbank Payment System");
            default -> throw new InvalidProductCodeException("Invalid code: " + code);
        };
    }
    
    private ProductCode validateEwtCode(String code) {
        // EWT pattern validation from COBOL
        if (code.matches("^EWT\\d{2}$")) {
            return new ProductCode(code, "Electronic Wire Transfer");
        }
        throw new InvalidProductCodeException("Invalid EWT pattern: " + code);
    }
    
    private TransactionAmount processAmount(String cobolAmount) {
        // COBOL amount format conversion
        String cleanAmount = cobolAmount.replaceFirst("^0+", "");
        if (cleanAmount.isEmpty()) return new TransactionAmount(BigDecimal.ZERO);
        
        // COBOL stores amounts as integers with implied decimal places
        BigDecimal amount = new BigDecimal(cleanAmount).divide(BigDecimal.valueOf(100));
        return new TransactionAmount(amount);
    }
    
    private PaymentStatus determineInitialStatus(PaymentRequest request) {
        // Business logic for initial status determination
        return PaymentStatus.PENDING;
    }
    
    private void validateBusinessRules(ProductCode productCode, TransactionAmount amount, String accountNumber) {
        // Comprehensive business rule validation
        if (amount.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessRuleException("Amount must be positive");
        }
        
        if (!isValidAccountForProduct(accountNumber, productCode)) {
            throw new BusinessRuleException("Account not eligible for product: " + productCode.getCode());
        }
    }
}
```

#### 2. Statement Processing Logic (STMMCG01, STMMCREF, STMMCG07)

**Key Business Rules:**
- Statement merging algorithms
- Reference data lookup and integration
- Format validation and conversion
- Multi-section record processing

```java
/**
 * Statement processing business logic implementation.
 */
@Service
public class StatementBusinessLogic {
    
    /**
     * Statement merging logic from STMMCG01.
     */
    public MergedStatement mergeStatements(InterbankStatement interbank, HistoricalStatement historical) {
        StatementMerger merger = new StatementMerger();
        
        // Apply business prioritization rules
        List<StatementLine> prioritizedLines = merger.prioritizeLines(
            interbank.getLines(), historical.getLines());
        
        // Create merged statement with business validation
        MergedStatement merged = MergedStatement.builder()
            .mergedLines(prioritizedLines)
            .recordLength(525)  // Business requirement from COBOL
            .mergedDate(LocalDateTime.now())
            .build();
        
        // Validate merged statement
        validateMergedStatement(merged);
        
        return merged;
    }
    
    /**
     * Reference statement processing from STMMCREF.
     */
    public ReferenceStatement processReferenceStatement(String inputRecord) {
        if (inputRecord.length() != 3200) {
            throw new InvalidRecordLengthException("Expected 3200 characters");
        }
        
        // Parse multiple sections
        PaymentSection paymentSection = parsePaymentSection(inputRecord.substring(0, 800));
        EppSection eppSection = parseEppSection(inputRecord.substring(800, 1600));
        LccSection lccSection = parseLccSection(inputRecord.substring(1600, 2400));
        RftSection rftSection = parseRftSection(inputRecord.substring(2400, 3200));
        
        // Process IM/ST name lookup
        String imStLookup = processImStNameLookup(inputRecord);
        
        return ReferenceStatement.builder()
            .paymentSection(paymentSection)
            .eppSection(eppSection)
            .lccSection(lccSection)
            .rftSection(rftSection)
            .imStLookup(imStLookup)
            .build();
    }
    
    /**
     * Outward payment processing from STMMCG07.
     */
    public OutwardPayment processOutwardPayment(OutwardPaymentRequest request) {
        // Generate 2500-character record
        String formattedRecord = formatOutwardPaymentRecord(request);
        
        if (formattedRecord.length() != 2500) {
            throw new InvalidRecordFormatException("Outward payment record must be 2500 characters");
        }
        
        return OutwardPayment.builder()
            .formattedRecord(formattedRecord)
            .paymentDetails(request.getPaymentDetails())
            .validationStatus(ValidationStatus.VALIDATED)
            .build();
    }
}
```

#### 3. Bill Payment Processing (STMMCG02, STMBDD08)

**Key Business Rules:**
- PromptPay BILLER-ID validation (********** pattern)
- Payment detail processing with date conversion
- Statement-detail matching algorithms
- 285-character record processing

```java
/**
 * Bill payment business logic implementation.
 */
@Service
public class BillPaymentBusinessLogic {
    
    /**
     * PromptPay BILLER-ID validation from STMMCG02.
     */
    public boolean validateBillerId(String billerId) {
        // COBOL business rule: ********** pattern
        if (billerId == null || billerId.length() != 10) {
            return false;
        }
        return billerId.matches("CB\\d{8}");
    }
    
    /**
     * Bill payment detail processing from STMBDD08.
     */
    public BillPaymentDetail processPaymentDetail(String recordData) {
        if (recordData.length() != 285) {
            throw new InvalidRecordLengthException("Expected 285 characters");
        }
        
        // Parse fixed-length record
        String paymentId = recordData.substring(0, 20).trim();
        String billerId = recordData.substring(20, 30).trim();
        String amountStr = recordData.substring(30, 45).trim();
        String dateStr = recordData.substring(45, 53).trim();
        
        // Validate BILLER-ID
        if (!validateBillerId(billerId)) {
            throw new InvalidBillerIdException("Invalid BILLER-ID: " + billerId);
        }
        
        // Convert COBOL date format
        LocalDate paymentDate = convertCobolDate(dateStr);
        
        // Process amount
        BigDecimal amount = convertCobolAmount(amountStr);
        
        return BillPaymentDetail.builder()
            .paymentId(paymentId)
            .billerId(billerId)
            .amount(amount)
            .paymentDate(paymentDate)
            .build();
    }
    
    /**
     * Statement-detail matching from STMMCG02.
     */
    public MatchingResult matchPaymentToStatement(BillPayment payment, Statement statement) {
        List<StatementLine> matchingLines = statement.getLines().stream()
            .filter(line -> matchesPayment(line, payment))
            .collect(Collectors.toList());
        
        if (matchingLines.isEmpty()) {
            return MatchingResult.unmatched("No matching statement lines found");
        }
        
        if (matchingLines.size() > 1) {
            return MatchingResult.ambiguous("Multiple matching lines found");
        }
        
        return MatchingResult.matched(matchingLines.get(0));
    }
    
    private boolean matchesPayment(StatementLine line, BillPayment payment) {
        return line.getAmount().equals(payment.getAmount()) &&
               line.getPaymentDate().equals(payment.getPaymentDate()) &&
               line.getReferenceNumber().contains(payment.getBillerId());
    }
}
```

## COBOL-to-Java Mapping Reference

### Complete Program Implementation Mapping

| COBOL Program | Java Implementation | Package | Business Logic Focus | Priority |
|---------------|-------------------|---------|-------------------|----------|
| **STMMCG05** | `CnIpsPaymentProcessor` | `com.scb.bizo.payment.service` | Core payment processing, product validation, status management | **CRITICAL** |
| **STMMCG01** | `StatementMergeService` | `com.scb.bizo.statement.service` | Statement merging, data consolidation | HIGH |
| **STMMCREF** | `ReferenceDataProcessor` | `com.scb.bizo.statement.service` | Reference processing, lookup integration | HIGH |
| **STMMCG02** | `BillPaymentProcessor` | `com.scb.bizo.payment.service` | Bill payment, PromptPay BILLER-ID validation | HIGH |
| **STMMCG07** | `OutwardPaymentProcessor` | `com.scb.bizo.payment.service` | Outward payment processing, 2500-char records | MEDIUM |
| **STMMCG03** | `EppProcessor` | `com.scb.bizo.payment.service` | Electronic payment processing | MEDIUM |
| **STMMCG04** | `LccProcessor` | `com.scb.bizo.payment.service` | Local clearing collection | MEDIUM |
| **STMMCG06** | `RftProcessor` | `com.scb.bizo.payment.service` | Real-time fund transfers | MEDIUM |
| **STMBDD06** | `AccountFilterService` | `com.scb.bizo.account.service` | Account filtering, profile validation | HIGH |
| **STMBDD07** | `SimpleStatementFilter` | `com.scb.bizo.statement.service` | Simple statement filtering | LOW |
| **STMBDD08** | `BillPaymentDetailProcessor` | `com.scb.bizo.payment.service` | Bill payment detail processing | MEDIUM |
| **EBCMMC01** | `AccountProfileProcessor` | `com.scb.bizo.account.service` | ERP account profile processing | HIGH |
| **EBCMMC02** | `StatementCoordinator` | `com.scb.bizo.report.service` | Statement processing coordination | MEDIUM |
| **EBCMMC03** | `InterbankStatementProcessor` | `com.scb.bizo.statement.service` | Interbank statement processing | MEDIUM |
| **EBCMMC04** | `StatementMergeCoordinator` | `com.scb.bizo.report.service` | Statement merging coordination | MEDIUM |
| **EBCMMC05** | `BillPaymentCoordinator` | `com.scb.bizo.report.service` | Bill payment coordination | MEDIUM |
| **EBCMMC06** | `EppCoordinator` | `com.scb.bizo.report.service` | EPP processing coordination | LOW |
| **EBCMMC07** | `LccCoordinator` | `com.scb.bizo.report.service` | LCC processing coordination | LOW |
| **EBCMMC08** | `CnIpsCoordinator` | `com.scb.bizo.report.service` | CN/IPS processing coordination | HIGH |
| **EBCMMC09** | `StatementGenerationCoordinator` | `com.scb.bizo.report.service` | Statement generation coordination | MEDIUM |
| **EBCMMC71** | `RftCoordinator` | `com.scb.bizo.report.service` | RFT processing coordination | LOW |
| **EBCMAFTB** | `ReportWorkflowService` | `com.scb.bizo.report.service` | Job orchestration, workflow management | MEDIUM |

### Business Rule Implementation Mapping

| COBOL Business Rule | Java Implementation | Method/Class | Package |
|---------------------|-------------------|--------------|---------|
| **Product Code Mapping (DCP→BNT)** | `ProductCodeValidator.validateAndMap()` | `validateAndMap(String)` | `com.scb.bizo.payment.domain.rule` |
| **EWT Pattern Validation** | `ProductCodeValidator.validateEwtPattern()` | `validateEwtPattern(String)` | `com.scb.bizo.payment.domain.rule` |
| **Status Management (C/J)** | `PaymentStatusManager.updateStatus()` | `updateStatus(Payment, String)` | `com.scb.bizo.payment.domain.service` |
| **Amount Processing** | `AmountProcessor.processCobolAmount()` | `processCobolAmount(String)` | `com.scb.bizo.common.util` |
| **BILLER-ID Validation** | `BillerValidator.validateBillerId()` | `validateBillerId(String)` | `com.scb.bizo.payment.domain.rule` |
| **Statement Merging** | `StatementMerger.mergeStatements()` | `mergeStatements(List<Statement>)` | `com.scb.bizo.statement.domain.service` |
| **Reference Lookup** | `ReferenceDataService.lookup()` | `lookup(String, String)` | `com.scb.bizo.common.service` |
| **Date Conversion** | `DateConverter.convertCobolDate()` | `convertCobolDate(String)` | `com.scb.bizo.common.util` |
| **Record Parsing** | `RecordParser.parseFixedLength()` | `parseFixedLength(String, int)` | `com.scb.bizo.common.util` |

### Data Structure Mapping

| COBOL Data Structure | Java Entity/Model | Key Fields | Package |
|---------------------|------------------|------------|---------|
| **Payment Record** | `Payment` | paymentId, accountNumber, productCode, amount, status | `com.scb.bizo.payment.domain.model` |
| **Account Profile** | `AccountProfile` | accountNumber, accountType, productCode, status | `com.scb.bizo.account.domain.model` |
| **Statement Record** | `Statement` | statementId, accountNumber, content, recordLength | `com.scb.bizo.statement.domain.model` |
| **Bill Payment Detail** | `BillPaymentDetail` | paymentId, billerId, amount, paymentDate | `com.scb.bizo.payment.domain.model` |
| **Reference Data** | `ReferenceData` | code, type, description, value | `com.scb.bizo.common.domain.model` |
| **Transaction Amount** | `TransactionAmount` | amount, currency, decimalPlaces | `com.scb.bizo.common.domain.value` |
| **Product Code** | `ProductCode` | code, description, type, validationRules | `com.scb.bizo.payment.domain.value` |

## Migration Strategy and Implementation Order

### Phase-Based Implementation Strategy

#### Phase 1: Foundation and Core Infrastructure (Weeks 1-4)

**Week 1: Project Setup and Basic Infrastructure**
```bash
# Create project structure
mvn archetype:generate \
  -DgroupId=com.scb.bizo \
  -DartifactId=multicast-report-library \
  -DarchetypeArtifactId=maven-archetype-quickstart

# Set up package structure
mkdir -p src/main/java/com/scb/bizo/{report,payment,statement,account,common}
mkdir -p src/main/java/com/scb/bizo/report/{api,domain,service}
mkdir -p src/main/java/com/scb/bizo/payment/{api,domain,service}
mkdir -p src/main/java/com/scb/bizo/statement/{api,domain,service}
mkdir -p src/main/java/com/scb/bizo/account/{api,domain,service}
mkdir -p src/main/java/com/scb/bizo/common/{util,service,domain}
```

**Implementation Tasks:**
- [x] Create Spring Boot project with Java 21
- [x] Set up Maven/Gradle build configuration
- [x] Configure NoSQL database (MongoDB) integration
- [x] Implement basic Spring configuration classes
- [x] Create package structure following `com.scb.bizo` namespace
- [x] Set up logging and monitoring infrastructure

**Week 2: Domain Models and Value Objects**
**Implementation Order:**
1. `TransactionAmount` (foundational value object)
2. `ProductCode` (critical for payment processing)
3. `AccountNumber` (account identification)
4. `PaymentId`, `StatementId`, `ReportId` (entity identifiers)
5. `Payment` (core payment entity)
6. `AccountProfile` (account management)
7. `Statement` (statement processing)
8. `MulticastReport` (main aggregate root)

**Week 3: Business Rules and Validation**
**Implementation Order:**
1. `ProductCodeValidator` (critical STMMCG05 logic)
2. `AmountProcessor` (financial calculations)
3. `PaymentValidator` (payment validation rules)
4. `AccountValidator` (account validation)
5. `StatementValidator` (statement validation)
6. `BillerValidator` (bill payment validation)

**Week 4: Common Utilities and Infrastructure Interfaces**
- Date processing utilities
- Amount conversion utilities
- Record parsing utilities
- Repository interfaces
- Event publisher interfaces
- Configuration properties

#### Phase 2: Core Payment Processing (Weeks 5-8) - CRITICAL PHASE

**Week 5: STMMCG05 Core Payment Logic (HIGHEST PRIORITY)**
```java
// Critical implementation checklist for STMMCG05
✅ Product code validation and mapping (DCP→BNT)
✅ EWT pattern validation (EWT##)
✅ IPS code validation
✅ Amount processing from COBOL format
✅ Status management ('C', 'J' handling)
✅ Exception handling and audit logging
✅ Unit tests covering all business scenarios (>95% coverage)
```

**Implementation Tasks:**
- [x] `CnIpsPaymentProcessor` - main processing service
- [x] `ProductCodeValidator` - exact COBOL validation logic
- [x] `PaymentStatusManager` - status transition management
- [x] `AmountProcessor` - COBOL amount format conversion
- [x] Comprehensive unit tests for all business rules
- [x] Integration tests with repository layer

**Week 6: Payment Validation and Business Rules**
- Complete payment validation framework
- Business rule engine implementation
- Error handling and exception management
- Audit logging integration

**Week 7: Payment Service Integration**
- Service layer integration
- Transaction management
- Event publishing for payment processing
- Performance optimization

**Week 8: Payment Processing Testing and Validation**
- Comprehensive business logic testing
- COBOL behavior validation
- Performance testing
- Error scenario testing

#### Phase 3: Statement Processing (Weeks 9-12)

**Week 9: Statement Generation (STMMCG01, STMMCREF)**
- Basic statement generation service
- Statement merging algorithms
- Reference data integration
- Format validation

**Week 10: Advanced Statement Processing**
- Reference statement processing (3200-char records)
- IM/ST name lookup integration
- Multi-section record parsing
- EWT product code reference handling

**Week 11: Statement Integration**
- Integration with payment processing
- Statement-payment reconciliation
- Statement validation and formatting
- Output generation

**Week 12: Statement Testing and Optimization**
- End-to-end statement generation testing
- Format compliance validation
- Performance optimization
- Integration testing

#### Phase 4: Specialized Payment Services (Weeks 13-16)

**Week 13: Bill Payment Processing (STMMCG02, STMBDD08)**
- PromptPay BILLER-ID validation
- Bill payment detail processing
- Statement-detail matching
- 285-character record processing

**Week 14: Electronic Payment Processing (STMMCG03)**
- EPP transaction validation
- Electronic payment processing logic
- EBPP detail generation
- 700-character record handling

**Week 15: Local Clearing Collection (STMMCG04)**
- LCC transaction processing
- DR/CR logic implementation
- Transaction type matching
- Multiple format support

**Week 16: Real-Time Fund Transfer (STMMCG06)**
- RFT processing logic
- PromptPay integration
- Real-time validation
- Transfer processing

#### Phase 5: Account Management and Coordination (Weeks 17-20)

**Week 17: Account Services (STMBDD06, STMBDD07)**
- Account validation service
- Account filtering logic
- Profile management
- Multi-cash account handling

**Week 18: Report Workflow Service**
- 9-step workflow orchestration
- Step dependency management
- Error handling and recovery
- Progress tracking

**Week 19: API Facade Layer**
- Public API implementation
- Request/response handling
- Validation and error formatting
- Documentation generation

**Week 20: Integration and Final Testing**
- Complete system integration
- End-to-end workflow testing
- Performance validation
- Business logic verification

### Implementation Dependencies

#### Critical Path Dependencies
```
Foundation → Core Payment (STMMCG05) → Statement Processing → Specialized Payments → Integration
     ↓              ↓                      ↓                    ↓              ↓
Utilities → Product Validation → Statement Generation → Bill Payments → API Layer
```

#### Service Dependencies
```mermaid
graph TD
    A[Utilities & Infrastructure] --> B[Domain Models]
    B --> C[Business Rules]
    C --> D[Core Payment Service]
    D --> E[Statement Services]
    E --> F[Specialized Payment Services]
    F --> G[Workflow Orchestration]
    G --> H[API Layer]
    
    D --> I[Account Services]
    I --> F
    
    style D fill:#ff9999
    style E fill:#99ff99
    style G fill:#9999ff
```

## Automated Testing Strategy

### Testing Pyramid Implementation

#### 1. Unit Tests (70% - Business Logic Focus)

**Core Payment Processing Tests (STMMCG05)**
```java
@ExtendWith(MockitoExtension.class)
class CnIpsPaymentProcessorTest {
    
    @Mock
    private ProductCodeValidator productCodeValidator;
    
    @Mock
    private PaymentRepository paymentRepository;
    
    @InjectMocks
    private CnIpsPaymentProcessor processor;
    
    @Test
    void shouldProcessValidCnIpsPayment() {
        // Given: Valid payment request
        CnIpsPaymentRequest request = CnIpsPaymentRequest.builder()
            .accountNumber("**********")
            .productCode("DCP")  // Should map to BNT
            .amount("**********")  // COBOL format for 1000.00
            .build();
        
        ProductCode mappedCode = new ProductCode("BNT", "Bank Note Transfer");
        when(productCodeValidator.validateAndMap("DCP")).thenReturn(mappedCode);
        
        Payment savedPayment = createMockPayment();
        when(paymentRepository.save(any(Payment.class))).thenReturn(savedPayment);
        
        // When: Process payment
        Payment result = processor.processCnIpsPayment(request);
        
        // Then: Verify COBOL business logic preserved
        assertThat(result.getProductCode().getCode()).isEqualTo("BNT");
        assertThat(result.getAmount().getAmount()).isEqualTo(new BigDecimal("1000.00"));
        assertThat(result.getStatus()).isEqualTo(PaymentStatus.PENDING);
        
        verify(productCodeValidator).validateAndMap("DCP");
        verify(paymentRepository).save(any(Payment.class));
    }
    
    @Test
    void shouldHandleProductCodeMappingFromCobol() {
        // Test exact COBOL mapping: DCP → BNT
        ProductCode result = productCodeValidator.validateAndMap("DCP");
        assertThat(result.getCode()).isEqualTo("BNT");
        
        // Test EWT pattern validation from COBOL
        assertThat(productCodeValidator.isValid("EWT01")).isTrue();
        assertThat(productCodeValidator.isValid("EWT99")).isTrue();
        assertThat(productCodeValidator.isValid("EWT1")).isFalse();
    }
    
    @Test
    void shouldHandleCobolStatusManagement() {
        // Test COBOL status logic: 'C' and 'J' handling
        Payment payment = new Payment();
        
        // Test 'C' status without debit date
        payment.setDebitDate(null);
        processor.updatePaymentStatus(payment.getId(), "C");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_BEFORE_DEBIT);
        
        // Test 'C' status with debit date
        payment.setDebitDate(LocalDateTime.now());
        processor.updatePaymentStatus(payment.getId(), "C");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_AFTER_DEBIT);
        
        // Test 'J' status
        processor.updatePaymentStatus(payment.getId(), "J");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_AFTER_DEBIT);
    }
    
    @ParameterizedTest
    @CsvSource({
        "0000000100, 1.00",
        "0000010000, 100.00", 
        "0001000000, 10000.00",
        "0000000000, 0.00"
    })
    void shouldConvertCobolAmountFormats(String cobolAmount, String expectedAmount) {
        // Test COBOL amount conversion logic
        TransactionAmount amount = AmountProcessor.processCobolAmount(cobolAmount);
        assertThat(amount.getAmount()).isEqualTo(new BigDecimal(expectedAmount));
    }
}
```

**Statement Processing Tests**
```java
@ExtendWith(MockitoExtension.class)
class StatementMergeServiceTest {
    
    @Test
    void shouldMergeStatementsFollowingCobolLogic() {
        // Test statement merging logic from STMMCG01
        InterbankStatement interbank = createInterbankStatement();
        HistoricalStatement historical = createHistoricalStatement();
        
        MergedStatement result = statementMergeService.mergeStatements(interbank, historical);
        
        // Verify COBOL business rules
        assertThat(result.getRecordLength()).isEqualTo(525);  // COBOL requirement
        assertThat(result.getMergedLines()).hasSize(10);
        assertThat(result.isValid()).isTrue();
    }
    
    @Test
    void shouldProcessReferenceStatementFromCobol() {
        // Test 3200-character record processing from STMMCREF
        String cobol3200Record = create3200CharacterRecord();
        
        ReferenceStatement result = referenceProcessor.processInputRecord(cobol3200Record);
        
        assertThat(result.getPaymentSection()).isNotNull();
        assertThat(result.getEppSection()).isNotNull();
        assertThat(result.getLccSection()).isNotNull();
        assertThat(result.getRftSection()).isNotNull();
        assertThat(result.getImStLookup()).isNotEmpty();
    }
}
```

**Bill Payment Tests**
```java
@Test
void shouldValidatePromptPayBillerIdFromCobol() {
    // Test PromptPay BILLER-ID validation from STMMCG02
    BillerValidator validator = new BillerValidator();
    
    // Valid BILLER-ID patterns
    assertTrue(validator.validateBillerId("**********"));
    assertTrue(validator.validateBillerId("CB12345678"));
    
    // Invalid patterns
    assertFalse(validator.validateBillerId("CB123"));      // Too short
    assertFalse(validator.validateBillerId("XB12345678"));  // Wrong prefix
    assertFalse(validator.validateBillerId("CB12345678X")); // Too long
}
```

#### 2. Integration Tests (20% - Service Integration)

**Payment Processing Integration**
```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.data.mongodb.uri=mongodb://localhost:27017/test-db"
})
class PaymentProcessingIntegrationTest {
    
    @Autowired
    private CnIpsPaymentProcessor paymentProcessor;
    
    @Autowired
    private PaymentRepository paymentRepository;
    
    @Test
    @Transactional
    void shouldProcessPaymentEndToEnd() {
        // Given: Complete payment scenario
        CnIpsPaymentRequest request = createValidPaymentRequest();
        
        // When: Process through complete service layer
        Payment result = paymentProcessor.processCnIpsPayment(request);
        
        // Then: Verify complete workflow
        assertThat(result.getPaymentId()).isNotNull();
        assertThat(result.getStatus()).isEqualTo(PaymentStatus.PENDING);
        
        // Verify persistence
        Optional<Payment> saved = paymentRepository.findById(result.getPaymentId());
        assertThat(saved).isPresent();
        assertThat(saved.get().getProductCode().getCode()).isEqualTo("BNT");
    }
}
```

**Report Workflow Integration**
```java
@SpringBootTest
class ReportWorkflowIntegrationTest {
    
    @Autowired
    private ReportWorkflowService workflowService;
    
    @Test
    void shouldExecuteNineStepWorkflowSuccessfully() {
        // Given: Valid report request
        ReportRequest request = createValidReportRequest();
        
        // When: Execute complete workflow
        ReportResult result = workflowService.executeWorkflow(request);
        
        // Then: Verify all steps completed
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getCompletedSteps()).hasSize(9);
        
        // Verify step completion order
        List<StepType> completedOrder = result.getCompletedSteps().stream()
            .map(ProcessingStep::getStepType)
            .collect(Collectors.toList());
        assertThat(completedOrder).containsExactly(StepType.getOrderedSteps().toArray(new StepType[0]));
    }
}
```

#### 3. Contract Tests (5% - API Contracts)

**API Contract Validation**
```java
@WebMvcTest(ReportProcessingFacade.class)
class ReportProcessingApiContractTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private ReportWorkflowService workflowService;
    
    @Test
    void shouldAcceptValidReportRequest() throws Exception {
        String validRequest = """
            {
                "accountNumber": "**********",
                "reportType": "MULTICAST",
                "processingDate": "2024-01-15"
            }
            """;
        
        ReportResult mockResult = ReportResult.success(createMockReport());
        when(workflowService.executeWorkflow(any())).thenReturn(mockResult);
        
        mockMvc.perform(post("/api/v1/reports")
                .contentType(MediaType.APPLICATION_JSON)
                .content(validRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.reportId").exists());
    }
}
```

#### 4. End-to-End Tests (5% - Complete Workflows)

**Complete Report Processing E2E**
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
class ReportProcessingE2ETest {
    
    @Container
    static MongoDBContainer mongodb = new MongoDBContainer("mongo:5.0");
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldProcessCompleteMulticastReportWorkflow() {
        // Given: Complete multicast report scenario
        ReportRequest request = createCompleteReportRequest();
        
        // When: Submit through API
        ResponseEntity<ReportResult> response = restTemplate.postForEntity(
            "/api/v1/reports", request, ReportResult.class);
        
        // Then: Verify complete workflow execution
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        
        // Verify all 9 steps completed
        String reportId = response.getBody().getReportId();
        ResponseEntity<ReportStatus> statusResponse = restTemplate.getForEntity(
            "/api/v1/reports/" + reportId + "/status", ReportStatus.class);
        
        assertThat(statusResponse.getBody().getStatus()).isEqualTo("COMPLETED");
        assertThat(statusResponse.getBody().getCompletedSteps()).hasSize(9);
    }
}
```

### Business Logic Validation Tests

#### COBOL Business Rule Compliance Tests
```java
/**
 * Dedicated test suite to validate exact COBOL business rule preservation.
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CobolBusinessRuleComplianceTest {
    
    @Test
    void shouldPreserveAllStmmcg05BusinessLogic() {
        // Comprehensive test of STMMCG05 business logic preservation
        
        // 1. Product code mapping
        assertThat(productCodeValidator.validateAndMap("DCP").getCode()).isEqualTo("BNT");
        
        // 2. EWT pattern validation
        assertThat(productCodeValidator.isValid("EWT01")).isTrue();
        assertThat(productCodeValidator.isValid("EWT1")).isFalse();
        
        // 3. Status management
        Payment payment = new Payment();
        paymentStatusManager.updateStatus(payment, "C");
        assertThat(payment.getStatus()).isIn(
            PaymentStatus.CANCELLED_BEFORE_DEBIT, 
            PaymentStatus.CANCELLED_AFTER_DEBIT);
        
        // 4. Amount processing
        TransactionAmount amount = AmountProcessor.processCobolAmount("**********");
        assertThat(amount.getAmount()).isEqualTo(new BigDecimal("1000.00"));
    }
    
    @Test
    void shouldPreserveStmmcg02BillPaymentLogic() {
        // Test PromptPay BILLER-ID validation
        assertTrue(billerValidator.validateBillerId("**********"));
        
        // Test 285-character record processing
        String record285 = create285CharacterRecord();
        BillPaymentDetail detail = billPaymentProcessor.processPaymentDetail(record285);
        assertThat(detail).isNotNull();
        assertThat(detail.getBillerId()).matches("CB\\d{8}");
    }
    
    @Test
    void shouldPreserveStatementProcessingLogic() {
        // Test statement merging from STMMCG01
        MergedStatement merged = statementMerger.mergeStatements(statements);
        assertThat(merged.getRecordLength()).isEqualTo(525);
        
        // Test reference processing from STMMCREF
        String record3200 = create3200CharacterRecord();
        ReferenceStatement reference = referenceProcessor.processInputRecord(record3200);
        assertThat(reference.getSections()).hasSize(4);
    }
}
```

### Performance Testing Strategy

#### Load Testing
```java
@Test
void shouldMeetPerformanceRequirements() {
    // Performance test: Process 1000 payments in under 5 seconds
    List<PaymentRequest> requests = createPaymentBatch(1000);
    
    long startTime = System.currentTimeMillis();
    List<PaymentResult> results = paymentProcessor.processPaymentBatch(requests);
    long endTime = System.currentTimeMillis();
    
    assertThat(results).hasSize(1000);
    assertThat(endTime - startTime).isLessThan(5000);
    
    // Verify success rate
    long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
    assertThat(successCount).isGreaterThan(950); // 95% success rate minimum
}
```

#### Memory Usage Testing
```java
@Test
void shouldMaintainMemoryEfficiency() {
    Runtime runtime = Runtime.getRuntime();
    long initialMemory = runtime.totalMemory() - runtime.freeMemory();
    
    // Process large batch
    List<PaymentRequest> largeBatch = createPaymentBatch(10000);
    paymentProcessor.processPaymentBatch(largeBatch);
    
    System.gc();
    long finalMemory = runtime.totalMemory() - runtime.freeMemory();
    long memoryIncrease = finalMemory - initialMemory;
    
    // Memory increase should be reasonable
    assertThat(memoryIncrease).isLessThan(100_000_000); // Less than 100MB
}
```

### Test Data Management

#### Test Data Builders
```java
public class TestDataBuilders {
    
    public static PaymentRequest.Builder validPaymentRequest() {
        return PaymentRequest.builder()
            .accountNumber("**********")
            .productCode("DCP")
            .amount("**********")
            .paymentType("CN")
            .referenceNumber("REF123456");
    }
    
    public static String create285CharacterRecord() {
        return "PAY********************" +  // 20 chars: payment ID
               "**********" +                // 10 chars: BILLER-ID
               "***************" +           // 15 chars: amount
               "********" +                  // 8 chars: date
               "X".repeat(232);              // 232 chars: padding
    }
    
    public static String create3200CharacterRecord() {
        return "PAYMENT_SECTION".repeat(40) +    // 800 chars
               "EPP_SECTION".repeat(80) +        // 800 chars  
               "LCC_SECTION".repeat(80) +        // 800 chars
               "RFT_SECTION".repeat(80);         // 800 chars
    }
}
```

## Quality Assurance and Validation

### Code Quality Standards

#### 1. Business Logic Preservation Validation
```java
/**
 * Every business logic implementation must include:
 * 1. Reference to original COBOL program
 * 2. Detailed documentation of business rules
 * 3. Test cases validating exact COBOL behavior
 * 4. Performance benchmarks
 */
@Service
public class CnIpsPaymentProcessor {
    
    /**
     * Processes CN/IPS payment following exact STMMCG05 business logic.
     * 
     * COBOL Business Rules Preserved:
     * 1. Product code mapping: DCP → BNT
     * 2. EWT pattern validation: EWT## (2 digits)
     * 3. Status management: 'C' and 'J' handling based on debit date
     * 4. Amount processing: COBOL decimal format with implied decimals
     * 
     * Original COBOL Program: STMMCG05
     * Migration Priority: CRITICAL
     * 
     * @param request CN/IPS payment request
     * @return processed payment with status
     */
    public Payment processCnIpsPayment(CnIpsPaymentRequest request) {
        // Implementation with exact COBOL logic preservation
    }
}
```

#### 2. Testing Standards
```java
/**
 * Test requirements for business logic preservation:
 * 1. >95% code coverage for all business logic
 * 2. All COBOL business scenarios tested
 * 3. Performance tests for batch operations
 * 4. Error scenario coverage
 * 5. COBOL behavior validation tests
 */
@Test
void shouldPreserveExactCobolBehavior() {
    // Test must validate identical behavior to COBOL
    // Include reference to COBOL program and business rule
    // Test both positive and negative scenarios
    // Validate error handling matches COBOL
}
```

#### 3. Documentation Standards
```java
/**
 * Documentation requirements:
 * 1. Business rule explanation with COBOL reference
 * 2. Input/output examples
 * 3. Error scenarios and handling
 * 4. Performance characteristics
 * 5. Dependencies and integration points
 */
```

### Code Review Checklist

#### Business Logic Review
- [ ] **COBOL Alignment**
  - [ ] Business rules exactly match COBOL behavior
  - [ ] All edge cases from COBOL preserved
  - [ ] Data transformations identical to COBOL
  - [ ] Error handling matches COBOL patterns
  - [ ] Performance comparable or better than COBOL

- [ ] **Code Quality**
  - [ ] Single responsibility principle followed
  - [ ] Business logic separated from infrastructure
  - [ ] Proper use of domain-driven design principles
  - [ ] Clear and meaningful naming conventions
  - [ ] Appropriate use of design patterns

- [ ] **Testing**
  - [ ] Comprehensive test coverage (>95% for business logic)
  - [ ] All business scenarios tested
  - [ ] Performance tests included
  - [ ] Integration tests validate workflow
  - [ ] Error scenarios thoroughly tested

#### Architecture Review
- [ ] **Platform Independence**
  - [ ] No platform-specific dependencies in business logic
  - [ ] Infrastructure interfaces properly abstracted
  - [ ] Configuration externalized appropriately
  - [ ] Database-agnostic implementation

- [ ] **Spring Boot Conventions**
  - [ ] Proper use of dependency injection
  - [ ] Appropriate use of Spring annotations
  - [ ] Configuration properties properly defined
  - [ ] Exception handling follows Spring patterns

### Acceptance Criteria

#### Functional Requirements
- [ ] **Business Logic Preservation (Critical)**
  - [ ] All 22 COBOL programs' business logic preserved exactly
  - [ ] Identical behavior for all business scenarios
  - [ ] Error handling matches COBOL behavior exactly
  - [ ] Data transformations produce identical results
  - [ ] Financial calculations maintain precision

- [ ] **Platform Independence**
  - [ ] Library deployable on any Java platform
  - [ ] No infrastructure dependencies in core logic
  - [ ] NoSQL database integration working
  - [ ] Cloud-ready architecture implemented

- [ ] **API Design**
  - [ ] Clean, well-documented APIs
  - [ ] Proper request/response handling
  - [ ] Comprehensive validation
  - [ ] Error responses well-formatted

#### Non-Functional Requirements
- [ ] **Performance**
  - [ ] Process 1000 payments in <5 seconds
  - [ ] Support concurrent processing
  - [ ] Memory usage optimized (<100MB for 10K payments)
  - [ ] Database queries optimized

- [ ] **Reliability**
  - [ ] Robust error handling and recovery
  - [ ] Transaction integrity maintained
  - [ ] Audit trail completeness
  - [ ] Data consistency guaranteed

- [ ] **Maintainability**
  - [ ] Clean, readable code structure
  - [ ] Comprehensive documentation
  - [ ] Easy to extend and modify
  - [ ] Following Java best practices

- [ ] **Security**
  - [ ] Input validation comprehensive
  - [ ] No sensitive data exposure
  - [ ] Audit logging implemented
  - [ ] Error messages don't leak data

### Final Migration Validation

#### Side-by-Side Testing Protocol
1. **Identical Input Processing**
   - Process identical inputs through COBOL and Java systems
   - Compare outputs for exact matches
   - Validate all business scenarios
   - Test error scenarios and responses

2. **Performance Comparison**
   - Benchmark processing times
   - Compare memory usage
   - Validate scalability improvements
   - Test under concurrent load

3. **Business Rule Validation**
   - Validate every business rule preservation
   - Test edge cases and boundary conditions
   - Verify calculation accuracy
   - Confirm error handling consistency

#### Production Readiness Checklist
- [ ] **Security Testing**
  - [ ] Security vulnerability assessment completed
  - [ ] Input validation comprehensive
  - [ ] No sensitive data leakage
  - [ ] Audit logging functional

- [ ] **Performance Testing**
  - [ ] Load testing passed requirements
  - [ ] Memory usage within acceptable limits
  - [ ] Concurrent processing validated
  - [ ] Database performance optimized

- [ ] **Integration Testing**
  - [ ] End-to-end workflow testing completed
  - [ ] External system integration validated
  - [ ] Error recovery testing passed
  - [ ] Data integrity validation successful

- [ ] **Documentation**
  - [ ] API documentation complete
  - [ ] Business logic documentation comprehensive
  - [ ] Deployment guides created
  - [ ] Operational procedures defined

## Implementation Checklist

### Pre-Migration Preparation
- [ ] **Environment Setup**
  - [ ] Java 21 development environment configured
  - [ ] Spring Boot 3.x project created with proper structure
  - [ ] MongoDB NoSQL database setup and configured
  - [ ] Maven/Gradle build configuration complete
  - [ ] IDE setup with Spring Boot and Java 21 support
  - [ ] Git repository initialized with proper gitignore

- [ ] **COBOL Analysis Complete**
  - [ ] All 22 COBOL files analyzed and business logic documented
  - [ ] Business logic vs infrastructure separation completed
  - [ ] Critical business rules identified and prioritized
  - [ ] Data structure mapping completed
  - [ ] Integration dependencies mapped

### Phase 1: Foundation (Weeks 1-4) ✅
- [ ] **Week 1: Project Infrastructure**
  - [ ] Complete package structure created (`com.scb.bizo`)
  - [ ] Spring Boot main application class configured
  - [ ] MongoDB integration configured
  - [ ] Logging framework setup (SLF4J/Logback)
  - [ ] Basic configuration classes created

- [ ] **Week 2: Domain Models**
  - [ ] `TransactionAmount` value object with COBOL conversion
  - [ ] `ProductCode` value object with validation rules  
  - [ ] `Payment` entity with business logic
  - [ ] `AccountProfile` entity with validation
  - [ ] `Statement` entity with processing logic
  - [ ] `MulticastReport` aggregate root

- [ ] **Week 3: Business Rules**
  - [ ] `ProductCodeValidator` with STMMCG05 logic
  - [ ] `AmountProcessor` with COBOL conversion
  - [ ] `PaymentValidator` with business rules
  - [ ] `AccountValidator` with profile rules
  - [ ] `BillerValidator` with PromptPay rules

- [ ] **Week 4: Utilities and Interfaces**
  - [ ] Date processing utilities
  - [ ] Amount conversion utilities
  - [ ] Repository interfaces defined
  - [ ] Event publisher interfaces
  - [ ] Configuration properties classes

### Phase 2: Core Payment Processing (Weeks 5-8) 🔥 CRITICAL
- [ ] **Week 5: STMMCG05 Implementation (HIGHEST PRIORITY)**
  - [ ] `CnIpsPaymentProcessor` core service
  - [ ] Product code validation and mapping (DCP→BNT)
  - [ ] EWT pattern validation (EWT##)
  - [ ] Status management ('C', 'J' handling)
  - [ ] Amount processing from COBOL format
  - [ ] Exception handling and audit logging
  - [ ] Comprehensive unit tests (>95% coverage)

- [ ] **Week 6: Payment Validation**
  - [ ] Payment validation framework
  - [ ] Business rule engine
  - [ ] Error handling and messaging
  - [ ] Validation result management

- [ ] **Week 7: Payment Service Integration**
  - [ ] Service layer integration
  - [ ] Transaction management
  - [ ] Event publishing
  - [ ] Performance optimization

- [ ] **Week 8: Payment Testing**
  - [ ] Business logic validation tests
  - [ ] COBOL behavior verification
  - [ ] Performance testing
  - [ ] Integration testing

### Phase 3: Statement Processing (Weeks 9-12)
- [ ] **Week 9: Statement Generation (STMMCG01, STMMCREF)**
  - [ ] `StatementGenerationService` implementation
  - [ ] Statement merging algorithms
  - [ ] Reference data integration
  - [ ] Format validation

- [ ] **Week 10: Advanced Statement Processing**
  - [ ] 3200-character record processing
  - [ ] IM/ST name lookup integration
  - [ ] Multi-section parsing
  - [ ] EWT reference handling

- [ ] **Week 11: Statement Integration**
  - [ ] Payment-statement integration
  - [ ] Reconciliation logic
  - [ ] Format compliance
  - [ ] Output generation

- [ ] **Week 12: Statement Testing**
  - [ ] End-to-end statement tests
  - [ ] Format validation tests
  - [ ] Performance optimization
  - [ ] Integration testing

### Phase 4: Specialized Payment Services (Weeks 13-16)
- [ ] **Week 13: Bill Payment (STMMCG02, STMBDD08)**
  - [ ] PromptPay BILLER-ID validation
  - [ ] 285-character record processing
  - [ ] Statement-detail matching
  - [ ] Bill payment workflow

- [ ] **Week 14: Electronic Payment (STMMCG03)**
  - [ ] EPP transaction processing
  - [ ] EBPP detail generation
  - [ ] 700-character record handling
  - [ ] Electronic payment validation

- [ ] **Week 15: Local Clearing (STMMCG04)**
  - [ ] LCC transaction processing
  - [ ] DR/CR logic implementation
  - [ ] Transaction type matching
  - [ ] Multiple format support

- [ ] **Week 16: Real-Time Transfer (STMMCG06)**
  - [ ] RFT processing implementation
  - [ ] PromptPay integration
  - [ ] Real-time validation
  - [ ] Transfer workflow

### Phase 5: Integration and API (Weeks 17-20)
- [ ] **Week 17: Account Services (STMBDD06, STMBDD07)**
  - [ ] Account validation service
  - [ ] Account filtering logic
  - [ ] Profile management
  - [ ] Multi-cash handling

- [ ] **Week 18: Workflow Orchestration**
  - [ ] 9-step workflow service
  - [ ] Step dependency management
  - [ ] Error handling and recovery
  - [ ] Progress tracking

- [ ] **Week 19: API Facade Layer**
  - [ ] `ReportProcessingFacade`
  - [ ] `PaymentProcessingFacade`
  - [ ] `StatementProcessingFacade`
  - [ ] Request/response handling

- [ ] **Week 20: Final Integration**
  - [ ] Complete system integration
  - [ ] End-to-end testing
  - [ ] Performance validation
  - [ ] Documentation completion

### Quality Gates

#### After Each Phase
- [ ] **Code Quality**
  - [ ] All code reviewed and approved
  - [ ] Business logic documentation complete
  - [ ] Test coverage >95% for business logic
  - [ ] Performance benchmarks met

- [ ] **Business Logic Validation**
  - [ ] COBOL behavior exactly preserved
  - [ ] All business scenarios tested
  - [ ] Error handling validated
  - [ ] Integration points verified

#### Final Quality Gate
- [ ] **Complete System Validation**
  - [ ] All 22 COBOL programs migrated
  - [ ] 9-step workflow functioning
  - [ ] Performance requirements met
  - [ ] Platform independence verified
  - [ ] Security requirements satisfied

- [ ] **Production Readiness**
  - [ ] Documentation complete
  - [ ] Deployment guides ready
  - [ ] Monitoring configured
  - [ ] Support procedures defined

### Success Metrics

#### Functional Metrics
- ✅ **100% Business Logic Preservation**: All COBOL business rules migrated
- ✅ **Complete Workflow Coverage**: All 9 processing steps implemented
- ✅ **Format Compliance**: All record formats (285, 525, 700, 2500, 3200 char) supported
- ✅ **Payment Type Coverage**: CN/IPS, Bill Payment, EPP, LCC, RFT all supported

#### Performance Metrics
- ✅ **Processing Speed**: 1000+ payments processed in <5 seconds
- ✅ **Memory Efficiency**: <100MB memory increase for 10K payments
- ✅ **Concurrent Processing**: Support for parallel processing
- ✅ **Scalability**: Linear performance scaling

#### Quality Metrics
- ✅ **Test Coverage**: >95% coverage for business logic components
- ✅ **Code Quality**: All code reviews passed, documentation complete
- ✅ **Error Handling**: Comprehensive error scenarios covered
- ✅ **Platform Independence**: No infrastructure dependencies in core logic

This comprehensive migration handbook provides complete guidance for successfully migrating the 22-file COBOL multicast report system to a modern, maintainable, and platform-agnostic Java Spring Boot library while preserving 100% of the original business logic and ensuring enterprise-grade quality and performance.