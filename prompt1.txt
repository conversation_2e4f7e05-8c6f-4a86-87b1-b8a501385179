We are planning to migrate the report code to the modern java springboot project.

The report code consisted of 22 COBOL files from the multicase report in mainframe.
For the report multicash, there are separate batch processes with 9 steps. Each step will have any logic for update report.
Input and output are files.
Step 1 : Program
EBCMMC01
EBCMMC02 --> STMBDD06
EBCMMC03 --> STMBDD07
EBCMMC04 --> STMMCG01

Step 2 : Program
EBCMMC05 --> STMBDD08 , STMMCG02

Step 3 : Program
EBCMMC06 --> STMMCG03

Step 4 : Program
EBCMMC07 --> STMMCG04

Step 5 : Program
EBCMMC71 --> STMMCG06

Step 6 : Program
EBCMMC08 --> STMMCG05

Step 7 : Program
EBCMMC09 --> STMMCREF , STMMCG07

Step 8 : Program
EBCMAFTB

The service step 1 has been completed then call step 2 continue until step 8.

For the migration to the new java spring boot library, see the following requirements:

- Migrate only business logic, exclude the infrastructure or mainframe related code. The library should be platform agnostic and infrastructure independent so it should be used in any microservice or any app that run in any platform, any cloud, any infrastructure.

- Should use modern tech stack: Java 21, Spring Boot 3.x.x MVC with virtual thread, NoSQL DB.

- Follow best practice, easy to maintain, easy to enhance, easy to read and understand by human. Plenty of useful comments in code.

- The package namespace should be com.scb.bizo

Please help to do the following tasks:

1. Read all 22 COBOL files in the /multicash folder, think deeply and capture all code structures, interfaces, models, business logic as well as all functionalities.

2. Think deeply and planning the migration to minimize gap and build errors, think about the order of the migration steps to minimize the migration issue. For example, you might start with crafting the interfaces first and then implement the business logic after all interfaces have been defined to reduce the compile error. Also plan to implement automated test in proper order to guarantee the correctness of functionalities. And AI should write to another markdown file for any knowledge that discovered during the migration on each COBOL file as well.

3. Write a migration guideline and handbook for AI to follow. Should be a markdown file. This handbook should consist of but not limit to:
	- Summary
	- High level architecture and design
	- High level UML and sequence diagrams.
	- List all classes, interfaces, models
	- Business logic for each class
	- Input assume is file at step 1 and output is file at step 8.
	- Relation in service call step by step
	- Mapping table from the new class/model to the original cobol filename, so AI can read or query anytime to understand a context or original business logic for each newly implement class.
	- Checklist for the AI to make sure that AI migrate 100% of all functionalities from the 22 COBOL files and proceed with correct order and correct steps.
	- etc.

	This handbook should be comprehensive and have a completed details that developers can understand clearly and follows.
