# Multicast Report COBOL to Java Spring Boot Migration Handbook

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [Migration Requirements and Scope](#migration-requirements-and-scope)
3. [High-Level Architecture and Design](#high-level-architecture-and-design)
4. [UML Diagrams and System Flow](#uml-diagrams-and-system-flow)
5. [Complete Classes, Interfaces, and Models Inventory](#complete-classes-interfaces-and-models-inventory)
6. [Business Logic Implementation by Class](#business-logic-implementation-by-class)
7. [8-Step Service Orchestration](#8-step-service-orchestration)
8. [COBOL-to-Java Mapping Reference](#cobol-to-java-mapping-reference)
9. [Migration Strategy and Implementation Order](#migration-strategy-and-implementation-order)
10. [Testing Strategy and Quality Assurance](#testing-strategy-and-quality-assurance)
11. [Implementation Checklist](#implementation-checklist)

## Executive Summary

### Migration Overview
This handbook provides comprehensive guidance for migrating a 22-file COBOL multicast report system to a modern Java Spring Boot 3.x library. The system processes reports through 8 sequential batch steps, with file input at Step 1 and file output at Step 8.

**System Characteristics:**
- **22 COBOL Programs**: Processing various aspects of multicast report generation
- **8 Sequential Steps**: Each step must complete before the next begins
- **File-Based Processing**: Input files at Step 1, output files at Step 8
- **Business Logic Focus**: Migrate only core business logic, exclude infrastructure

**Migration Objectives:**
- **100% Business Logic Preservation**: Maintain all original COBOL business rules
- **Platform Independence**: Infrastructure-agnostic library design
- **Modern Technology Stack**: Java 21, Spring Boot 3.x with virtual threads, NoSQL DB
- **Maintainable Architecture**: Clean, well-documented, extensible code
- **Sequential Processing**: Preserve 8-step workflow dependency

### Success Criteria
- ✅ All 22 COBOL files migrated with business logic preserved
- ✅ 8-step sequential workflow maintained and functional
- ✅ Platform-agnostic library deployable anywhere
- ✅ Comprehensive test coverage ensuring correctness
- ✅ Clean, maintainable code following Spring Boot best practices

## Migration Requirements and Scope

### Technical Requirements

#### Core Technology Stack
```xml
<!-- Spring Boot 3.x with Java 21 and Virtual Threads -->
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.0</version>
</parent>

<properties>
    <java.version>21</java.version>
    <spring-boot.version>3.2.0</spring-boot.version>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
</properties>

<dependencies>
    <!-- Spring Boot Starter with Virtual Threads -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
    </dependency>
    
    <!-- Spring Boot MVC -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- NoSQL Database Support -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb</artifactId>
    </dependency>
    
    <!-- Validation Framework -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    
    <!-- Testing Framework -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
</dependencies>
```

#### Virtual Threads Configuration
```java
@Configuration
@EnableAsync
public class VirtualThreadConfiguration {
    
    @Bean
    public Executor taskExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }
    
    @Bean
    public ThreadPoolTaskExecutor virtualThreadTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadFactory(Thread.ofVirtual().factory());
        executor.initialize();
        return executor;
    }
}
```

### Migration Scope Definition

#### ✅ **INCLUDE: Business Logic Components**
1. **Report Processing Logic**
   - Multi-step workflow orchestration
   - Business rule validation and enforcement
   - Data transformation and calculation algorithms
   - Report generation and formatting logic

2. **Data Processing Logic**
   - Record parsing and validation
   - Business calculations and aggregations
   - Data mapping and transformation
   - Format conversion algorithms

3. **Business Validation Rules**
   - Input data validation
   - Business rule enforcement
   - Error detection and handling
   - Data integrity checks

4. **Workflow Management**
   - Step-by-step processing coordination
   - Dependency management between steps
   - Error handling and recovery logic
   - Status tracking and reporting

#### ❌ **EXCLUDE: Infrastructure Components**
1. **File I/O Operations**
   - File reading/writing mechanisms → Spring Resource abstraction
   - Directory scanning and management → File service interfaces
   - File format handling → Custom deserializers/serializers

2. **Database Operations**
   - Direct database connectivity → Spring Data repositories
   - SQL/NoSQL query execution → Repository abstractions
   - Connection pooling → Spring auto-configuration

3. **Job Scheduling and Control**
   - COBOL job control language → Spring @Scheduled
   - Batch job orchestration → Spring Batch framework
   - System resource management → Spring resource management

4. **Platform-Specific Operations**
   - Mainframe system calls → Spring Boot actuators
   - Operating system interactions → Service abstractions
   - Hardware-specific optimizations → JVM optimizations

### Package Structure Requirements
```
com.scb.bizo/
├── report/                     # Main report processing domain
│   ├── api/                    # Public API and controllers
│   │   ├── controller/         # REST controllers
│   │   ├── dto/               # Data transfer objects
│   │   └── facade/            # Business facades
│   ├── domain/                # Core business domain
│   │   ├── model/             # Domain entities and value objects
│   │   ├── service/           # Domain services
│   │   ├── repository/        # Repository interfaces
│   │   └── event/             # Domain events
│   ├── application/           # Application services
│   │   ├── service/           # Application services
│   │   ├── orchestrator/      # Workflow orchestrators
│   │   └── processor/         # Step processors
│   └── infrastructure/        # Infrastructure adapters
│       ├── persistence/       # Data persistence
│       ├── messaging/         # Event publishing
│       └── config/           # Configuration
├── step/                      # Step-specific implementations
│   ├── step1/                # Step 1 processing
│   ├── step2/                # Step 2 processing
│   ├── step3/                # Step 3 processing
│   ├── step4/                # Step 4 processing
│   ├── step5/                # Step 5 processing
│   ├── step6/                # Step 6 processing
│   ├── step7/                # Step 7 processing
│   └── step8/                # Step 8 processing
└── common/                   # Shared utilities and components
    ├── util/                 # Utility classes
    ├── exception/            # Custom exceptions
    ├── validation/           # Validation components
    └── constant/             # Constants and enums
```

## High-Level Architecture and Design

### Target Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Multicast Report Library                     │
│                      (com.scb.bizo)                            │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────────┐
│                  API Gateway Layer                              │
│  ├── ReportController (REST endpoints)                         │
│  ├── ReportFacade (Business facade)                            │
│  ├── ValidationController (Input validation)                   │
│  └── StatusController (Progress tracking)                      │
├─────────────────────────────────────────────────────────────────┤
│                Application Service Layer                        │
│  ├── ReportOrchestrator (8-step workflow coordination)         │
│  ├── FileProcessorService (Input/output file handling)         │
│  ├── ValidationService (Business rule validation)              │
│  └── StatusTrackingService (Progress monitoring)               │
├─────────────────────────────────────────────────────────────────┤
│                Domain Service Layer                             │
│  ├── Step1Service (EBCMMC01, EBCMMC02, EBCMMC03, EBCMMC04)    │
│  ├── Step2Service (EBCMMC05)                                   │
│  ├── Step3Service (EBCMMC06)                                   │
│  ├── Step4Service (EBCMMC07)                                   │
│  ├── Step5Service (EBCMMC71)                                   │
│  ├── Step6Service (EBCMMC08)                                   │
│  ├── Step7Service (EBCMMC09)                                   │
│  └── Step8Service (EBCMAFTB)                                   │
├─────────────────────────────────────────────────────────────────┤
│                Business Logic Layer                             │
│  ├── Payment Processing (STMMCG05 - Core Logic)                │
│  ├── Statement Processing (STMMCG01, STMMCREF, STMMCG07)       │
│  ├── Bill Payment Processing (STMMCG02, STMBDD08)              │
│  ├── Electronic Payment (STMMCG03)                             │
│  ├── Local Clearing (STMMCG04)                                 │
│  ├── Real-Time Transfer (STMMCG06)                             │
│  └── Account Processing (STMBDD06, STMBDD07)                   │
├─────────────────────────────────────────────────────────────────┤
│                Domain Model Layer                               │
│  ├── MulticastReport (Aggregate Root)                          │
│  ├── ProcessingStep (Step entity)                              │
│  ├── ReportData (Report content)                               │
│  ├── PaymentTransaction (Payment entity)                       │
│  ├── AccountProfile (Account entity)                           │
│  └── ValidationResult (Validation value object)                │
├─────────────────────────────────────────────────────────────────┤
│             Infrastructure Interface Layer                      │
│  ├── ReportRepository (Data persistence interface)             │
│  ├── FileService (File handling interface)                     │
│  ├── EventPublisher (Event publishing interface)               │
│  └── ExternalService (External integration interface)          │
└─────────────────────────────────────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────────┐
│              Infrastructure Implementation                       │
│        (Provided by Consumer Applications)                      │
│  ├── MongoDB Repositories                                       │
│  ├── File System Integration                                    │
│  ├── Message Queue Integration                                  │
│  └── External Service Clients                                   │
└─────────────────────────────────────────────────────────────────┘
```

### Design Principles

#### 1. **Hexagonal Architecture (Clean Architecture)**
```java
// Core business logic (inside hexagon)
@Service
public class ReportOrchestrator {
    
    private final Map<Integer, StepProcessor> stepProcessors;
    private final ReportRepository reportRepository;        // Port (interface)
    private final EventPublisher eventPublisher;           // Port (interface)
    private final FileService fileService;                 // Port (interface)
    
    public ReportResult processReport(ReportRequest request) {
        // Pure business logic - no infrastructure dependencies
        return executeEightStepWorkflow(request);
    }
}

// Infrastructure adapter (outside hexagon)
@Component
public class MongoReportRepository implements ReportRepository {
    // MongoDB-specific implementation
}
```

#### 2. **Domain-Driven Design**
```java
// Domain aggregate root encapsulating business rules
@Entity
@Document(collection = "multicast_reports")
public class MulticastReport {
    
    private ReportId reportId;
    private ReportStatus status;
    private List<ProcessingStep> completedSteps;
    private ReportData inputData;
    private ReportData outputData;
    
    // Business logic methods
    public void processStep(int stepNumber) {
        validateStepTransition(stepNumber);
        executeStepBusinessLogic(stepNumber);
        updateStatusAfterStep(stepNumber);
    }
    
    private void validateStepTransition(int stepNumber) {
        // Business rules for step execution order
        if (stepNumber > 1 && !isStepCompleted(stepNumber - 1)) {
            throw new InvalidStepTransitionException(
                "Step " + (stepNumber - 1) + " must be completed before step " + stepNumber);
        }
    }
}
```

#### 3. **Sequential Processing with Virtual Threads**
```java
@Service
public class SequentialStepProcessor {
    
    @Async("virtualThreadTaskExecutor")
    public CompletableFuture<StepResult> processStepAsync(MulticastReport report, int stepNumber) {
        return CompletableFuture.supplyAsync(() -> {
            // Process step with virtual thread for scalability
            return processStep(report, stepNumber);
        });
    }
    
    public ReportResult processAllStepsSequentially(MulticastReport report) {
        for (int step = 1; step <= 8; step++) {
            StepResult result = processStep(report, step);
            if (!result.isSuccess()) {
                return ReportResult.failed("Step " + step + " failed: " + result.getErrorMessage());
            }
            report.completeStep(step, result);
        }
        return ReportResult.success(report);
    }
}
```

#### 4. **Event-Driven Step Coordination**
```java
// Events for step completion coordination
public record StepCompletedEvent(
    ReportId reportId,
    int stepNumber,
    LocalDateTime completedAt,
    StepResult result
) implements DomainEvent {}

@EventListener
public void handleStepCompletion(StepCompletedEvent event) {
    if (event.stepNumber() < 8) {
        // Trigger next step
        reportOrchestrator.processNextStep(event.reportId(), event.stepNumber() + 1);
    } else {
        // Complete report processing
        reportOrchestrator.finalizeReport(event.reportId());
    }
}
```

## UML Diagrams and System Flow

### High-Level System Sequence Diagram

```mermaid
sequenceDiagram
    participant Client
    participant ReportController
    participant ReportOrchestrator
    participant Step1Service
    participant Step2Service
    participant Step6Service
    participant Step8Service
    participant FileService
    participant Repository
    participant EventPublisher

    Client->>ReportController: POST /api/reports/process
    ReportController->>ReportOrchestrator: processReport(request)
    
    ReportOrchestrator->>FileService: readInputFile()
    FileService-->>ReportOrchestrator: inputData
    
    ReportOrchestrator->>Step1Service: processStep1(inputData)
    Step1Service->>Repository: saveIntermediateData()
    Step1Service->>EventPublisher: publishStepCompleted(STEP1)
    Step1Service-->>ReportOrchestrator: step1Result
    
    ReportOrchestrator->>Step2Service: processStep2(step1Result)
    Step2Service->>Repository: saveIntermediateData()
    Step2Service->>EventPublisher: publishStepCompleted(STEP2)
    Step2Service-->>ReportOrchestrator: step2Result
    
    Note over ReportOrchestrator: Continue steps 3-5...
    
    ReportOrchestrator->>Step6Service: processStep6(step5Result)
    Step6Service->>Repository: saveIntermediateData()
    Step6Service->>EventPublisher: publishStepCompleted(STEP6)
    Step6Service-->>ReportOrchestrator: step6Result
    
    Note over ReportOrchestrator: Continue step 7...
    
    ReportOrchestrator->>Step8Service: processStep8(step7Result)
    Step8Service->>FileService: writeOutputFile(finalData)
    Step8Service->>EventPublisher: publishReportCompleted()
    Step8Service-->>ReportOrchestrator: finalResult
    
    ReportOrchestrator-->>ReportController: ReportResult
    ReportController-->>Client: HTTP 200 OK
```

### Domain Model Class Diagram

```mermaid
classDiagram
    class MulticastReport {
        +ReportId reportId
        +ReportStatus status
        +LocalDateTime createdAt
        +LocalDateTime completedAt
        +List~ProcessingStep~ completedSteps
        +ReportData inputData
        +ReportData outputData
        +processStep(int stepNumber)
        +validateStepTransition(int stepNumber)
        +isComplete() boolean
        +canProcessStep(int stepNumber) boolean
    }
    
    class ProcessingStep {
        +int stepNumber
        +StepStatus status
        +LocalDateTime startTime
        +LocalDateTime endTime
        +StepResult result
        +Map~String,Object~ stepData
        +execute()
        +validate()
        +rollback()
    }
    
    class ReportData {
        +String dataContent
        +DataFormat format
        +long size
        +String checksum
        +Map~String,Object~ metadata
        +validateFormat()
        +transform(DataFormat targetFormat)
    }
    
    class StepResult {
        +boolean success
        +String message
        +Map~String,Object~ outputData
        +List~ValidationError~ errors
        +long processingTime
        +isSuccess() boolean
        +hasErrors() boolean
    }
    
    class PaymentTransaction {
        +TransactionId transactionId
        +AccountNumber accountNumber
        +BigDecimal amount
        +TransactionType type
        +TransactionStatus status
        +LocalDateTime processedDate
        +process()
        +validate()
        +cancel()
    }
    
    class AccountProfile {
        +AccountNumber accountNumber
        +AccountType accountType
        +AccountStatus status
        +Map~String,Object~ profileData
        +validateProfile()
        +isActive() boolean
    }
    
    MulticastReport ||--o{ ProcessingStep
    ProcessingStep ||--|| StepResult
    MulticastReport ||--o{ ReportData
    ProcessingStep ||--o{ PaymentTransaction
    ProcessingStep ||--o{ AccountProfile
```

### 8-Step Processing Flow Diagram

```mermaid
graph TD
    A[Input File] --> B[Step 1: Initial Processing]
    
    B --> C[EBCMMC01: Account Setup]
    C --> D[EBCMMC02 + STMBDD06: Statement Processing]
    D --> E[EBCMMC03 + STMBDD07: Interbank Processing]
    E --> F[EBCMMC04 + STMMCG01: Statement Merging]
    
    F --> G[Step 2: Bill Payment]
    G --> H[EBCMMC05 + STMBDD08 + STMMCG02: Bill Payment Processing]
    
    H --> I[Step 3: EPP Processing]
    I --> J[EBCMMC06 + STMMCG03: Electronic Payment Processing]
    
    J --> K[Step 4: LCC Processing]
    K --> L[EBCMMC07 + STMMCG04: Local Clearing Collection]
    
    L --> M[Step 5: RFT Processing]
    M --> N[EBCMMC71 + STMMCG06: Real-Time Fund Transfer]
    
    N --> O[Step 6: CN/IPS Processing]
    O --> P[EBCMMC08 + STMMCG05: Core Payment Processing]
    
    P --> Q[Step 7: Statement Generation]
    Q --> R[EBCMMC09 + STMMCREF + STMMCG07: Reference & Outward Processing]
    
    R --> S[Step 8: Final Output]
    S --> T[EBCMAFTB: Job Completion]
    T --> U[Output File]
    
    style P fill:#ff9999
    style R fill:#99ff99
    style T fill:#9999ff
    
    classDef stepBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    class C,D,E,F,H,J,L,N,P,R,T stepBox
```

### Service Interaction Architecture

```mermaid
graph LR
    subgraph "API Layer"
        RC[ReportController]
        RF[ReportFacade]
        VC[ValidationController]
    end
    
    subgraph "Application Layer"
        RO[ReportOrchestrator]
        FPS[FileProcessorService]
        VS[ValidationService]
        STS[StatusTrackingService]
    end
    
    subgraph "Domain Services"
        S1[Step1Service]
        S2[Step2Service]
        S3[Step3Service]
        S4[Step4Service]
        S5[Step5Service]
        S6[Step6Service]
        S7[Step7Service]
        S8[Step8Service]
    end
    
    subgraph "Business Logic"
        PP[PaymentProcessor]
        SP[StatementProcessor]
        AP[AccountProcessor]
        VP[ValidationProcessor]
    end
    
    subgraph "Infrastructure"
        RR[ReportRepository]
        FS[FileService]
        EP[EventPublisher]
        ES[ExternalService]
    end
    
    RC --> RF
    RF --> RO
    VC --> VS
    
    RO --> S1
    RO --> S2
    RO --> S3
    RO --> S4
    RO --> S5
    RO --> S6
    RO --> S7
    RO --> S8
    
    S1 --> PP
    S2 --> PP
    S6 --> PP
    S7 --> SP
    S1 --> AP
    
    PP --> RR
    SP --> RR
    AP --> RR
    
    RO --> FPS
    FPS --> FS
    RO --> EP
    VS --> VP
```

## Complete Classes, Interfaces, and Models Inventory

### API Layer Classes

#### 1. Report Controller
```java
package com.scb.bizo.report.api.controller;

/**
 * REST controller for multicast report processing operations.
 * Provides endpoints for report submission, status tracking, and result retrieval.
 */
@RestController
@RequestMapping("/api/v1/reports")
@Validated
public class ReportController {
    
    private final ReportFacade reportFacade;
    private final StatusTrackingService statusTrackingService;
    
    /**
     * Submit multicast report for processing through 8-step workflow.
     * Input: File upload at Step 1
     * Output: Report ID for tracking
     */
    @PostMapping("/process")
    public ResponseEntity<ReportSubmissionResponse> processReport(
            @RequestParam("file") MultipartFile inputFile,
            @Valid @RequestBody ReportProcessingRequest request) {
        
        ReportSubmissionResponse response = reportFacade.submitReport(inputFile, request);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Get report processing status and progress.
     */
    @GetMapping("/{reportId}/status")
    public ResponseEntity<ReportStatusResponse> getReportStatus(
            @PathVariable String reportId) {
        
        ReportStatusResponse status = statusTrackingService.getReportStatus(reportId);
        return ResponseEntity.ok(status);
    }
    
    /**
     * Download processed report output file from Step 8.
     */
    @GetMapping("/{reportId}/download")
    public ResponseEntity<Resource> downloadReportOutput(
            @PathVariable String reportId) {
        
        Resource outputFile = reportFacade.getReportOutput(reportId);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"report_output.txt\"")
                .body(outputFile);
    }
    
    /**
     * Get detailed step-by-step processing results.
     */
    @GetMapping("/{reportId}/steps")
    public ResponseEntity<List<StepStatusResponse>> getStepDetails(
            @PathVariable String reportId) {
        
        List<StepStatusResponse> stepDetails = statusTrackingService.getStepDetails(reportId);
        return ResponseEntity.ok(stepDetails);
    }
}
```

#### 2. Report Facade
```java
package com.scb.bizo.report.api.facade;

/**
 * Business facade orchestrating multicast report processing.
 * Coordinates file handling, validation, and 8-step workflow execution.
 */
@Component
@Transactional
public class ReportFacade {
    
    private final ReportOrchestrator reportOrchestrator;
    private final FileProcessorService fileProcessorService;
    private final ValidationService validationService;
    private final ReportRepository reportRepository;
    
    /**
     * Submit report for processing with comprehensive validation.
     */
    public ReportSubmissionResponse submitReport(MultipartFile inputFile, ReportProcessingRequest request) {
        try {
            // Step 1: Validate input file and request
            ValidationResult validation = validationService.validateReportSubmission(inputFile, request);
            if (!validation.isValid()) {
                return ReportSubmissionResponse.validationFailed(validation.getErrors());
            }
            
            // Step 2: Process input file
            ReportData inputData = fileProcessorService.processInputFile(inputFile);
            
            // Step 3: Create report entity
            MulticastReport report = MulticastReport.builder()
                .reportId(ReportId.generate())
                .status(ReportStatus.SUBMITTED)
                .inputData(inputData)
                .createdAt(LocalDateTime.now())
                .build();
            
            // Step 4: Save report
            report = reportRepository.save(report);
            
            // Step 5: Start 8-step processing asynchronously
            reportOrchestrator.processReportAsync(report);
            
            return ReportSubmissionResponse.success(report.getReportId().getValue());
            
        } catch (Exception e) {
            log.error("Failed to submit report", e);
            return ReportSubmissionResponse.error("Failed to submit report: " + e.getMessage());
        }
    }
    
    /**
     * Retrieve processed report output file.
     */
    public Resource getReportOutput(String reportId) {
        MulticastReport report = reportRepository.findByReportId(new ReportId(reportId))
            .orElseThrow(() -> new ReportNotFoundException(reportId));
        
        if (!report.isComplete()) {
            throw new ReportNotCompleteException("Report processing not yet complete");
        }
        
        return fileProcessorService.getOutputFileResource(report.getOutputData());
    }
}
```

### Application Service Layer

#### 1. Report Orchestrator
```java
package com.scb.bizo.report.application.orchestrator;

/**
 * Main orchestrator for 8-step multicast report processing workflow.
 * Coordinates sequential execution of all processing steps.
 */
@Service
@Slf4j
public class ReportOrchestrator {
    
    private final Map<Integer, StepProcessor> stepProcessors;
    private final ReportRepository reportRepository;
    private final EventPublisher eventPublisher;
    private final StatusTrackingService statusTrackingService;
    
    public ReportOrchestrator(List<StepProcessor> processors,
                             ReportRepository reportRepository,
                             EventPublisher eventPublisher,
                             StatusTrackingService statusTrackingService) {
        this.stepProcessors = processors.stream()
            .collect(Collectors.toMap(StepProcessor::getStepNumber, Function.identity()));
        this.reportRepository = reportRepository;
        this.eventPublisher = eventPublisher;
        this.statusTrackingService = statusTrackingService;
    }
    
    /**
     * Process report through all 8 steps sequentially.
     */
    @Async("virtualThreadTaskExecutor")
    public void processReportAsync(MulticastReport report) {
        try {
            log.info("Starting 8-step processing for report: {}", report.getReportId());
            
            report.updateStatus(ReportStatus.PROCESSING);
            reportRepository.save(report);
            
            ReportData currentData = report.getInputData();
            
            // Execute steps 1-8 sequentially
            for (int stepNumber = 1; stepNumber <= 8; stepNumber++) {
                log.info("Processing step {} for report: {}", stepNumber, report.getReportId());
                
                StepResult stepResult = processStep(report, stepNumber, currentData);
                
                if (!stepResult.isSuccess()) {
                    handleStepFailure(report, stepNumber, stepResult);
                    return;
                }
                
                // Update report with step completion
                report.completeStep(stepNumber, stepResult);
                currentData = new ReportData(stepResult.getOutputData());
                
                // Save intermediate progress
                reportRepository.save(report);
                
                // Publish step completion event
                eventPublisher.publish(new StepCompletedEvent(
                    report.getReportId(), stepNumber, LocalDateTime.now(), stepResult));
                
                log.info("Completed step {} for report: {}", stepNumber, report.getReportId());
            }
            
            // Finalize report
            report.setOutputData(currentData);
            report.updateStatus(ReportStatus.COMPLETED);
            reportRepository.save(report);
            
            eventPublisher.publish(new ReportCompletedEvent(report.getReportId(), LocalDateTime.now()));
            
            log.info("Successfully completed all 8 steps for report: {}", report.getReportId());
            
        } catch (Exception e) {
            log.error("Failed to process report: {}", report.getReportId(), e);
            handleProcessingFailure(report, e);
        }
    }
    
    /**
     * Process individual step with error handling and validation.
     */
    private StepResult processStep(MulticastReport report, int stepNumber, ReportData inputData) {
        StepProcessor processor = stepProcessors.get(stepNumber);
        if (processor == null) {
            throw new StepProcessorNotFoundException("No processor found for step: " + stepNumber);
        }
        
        try {
            // Update status tracking
            statusTrackingService.updateStepStatus(report.getReportId(), stepNumber, StepStatus.PROCESSING);
            
            // Process step
            StepResult result = processor.processStep(report, inputData);
            
            // Update status
            StepStatus finalStatus = result.isSuccess() ? StepStatus.COMPLETED : StepStatus.FAILED;
            statusTrackingService.updateStepStatus(report.getReportId(), stepNumber, finalStatus);
            
            return result;
            
        } catch (Exception e) {
            log.error("Step {} failed for report: {}", stepNumber, report.getReportId(), e);
            statusTrackingService.updateStepStatus(report.getReportId(), stepNumber, StepStatus.FAILED);
            return StepResult.failed("Step " + stepNumber + " failed: " + e.getMessage());
        }
    }
    
    private void handleStepFailure(MulticastReport report, int stepNumber, StepResult stepResult) {
        log.error("Step {} failed for report: {}, errors: {}", stepNumber, report.getReportId(), stepResult.getErrors());
        
        report.updateStatus(ReportStatus.FAILED);
        report.addError("Step " + stepNumber + " failed: " + stepResult.getMessage());
        reportRepository.save(report);
        
        eventPublisher.publish(new ReportFailedEvent(report.getReportId(), stepNumber, stepResult.getErrors()));
    }
    
    private void handleProcessingFailure(MulticastReport report, Exception e) {
        report.updateStatus(ReportStatus.FAILED);
        report.addError("Processing failed: " + e.getMessage());
        reportRepository.save(report);
        
        eventPublisher.publish(new ReportFailedEvent(report.getReportId(), 0, List.of(e.getMessage())));
    }
}
```

### Domain Service Layer (Step Processors)

#### 1. Step 1 Service (Initial Processing)
```java
package com.scb.bizo.step.step1;

/**
 * Step 1 processor handling initial report processing.
 * Processes: EBCMMC01, EBCMMC02+STMBDD06, EBCMMC03+STMBDD07, EBCMMC04+STMMCG01
 */
@Service
@Slf4j
public class Step1Service implements StepProcessor {
    
    private final AccountSetupProcessor accountSetupProcessor;           // EBCMMC01
    private final StatementProcessor statementProcessor;                  // EBCMMC02 + STMBDD06
    private final InterbankProcessor interbankProcessor;                  // EBCMMC03 + STMBDD07
    private final StatementMergeProcessor statementMergeProcessor;        // EBCMMC04 + STMMCG01
    
    @Override
    public int getStepNumber() {
        return 1;
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 1 processing for report: {}", report.getReportId());
        
        try {
            // Sub-step 1: Account Setup (EBCMMC01)
            AccountSetupResult accountResult = accountSetupProcessor.processAccountSetup(inputData);
            if (!accountResult.isSuccess()) {
                return StepResult.failed("Account setup failed: " + accountResult.getErrorMessage());
            }
            
            // Sub-step 2: Statement Processing (EBCMMC02 + STMBDD06)
            StatementProcessingResult statementResult = statementProcessor.processStatements(
                inputData, accountResult.getAccountData());
            if (!statementResult.isSuccess()) {
                return StepResult.failed("Statement processing failed: " + statementResult.getErrorMessage());
            }
            
            // Sub-step 3: Interbank Processing (EBCMMC03 + STMBDD07)
            InterbankProcessingResult interbankResult = interbankProcessor.processInterbank(
                statementResult.getStatementData());
            if (!interbankResult.isSuccess()) {
                return StepResult.failed("Interbank processing failed: " + interbankResult.getErrorMessage());
            }
            
            // Sub-step 4: Statement Merging (EBCMMC04 + STMMCG01)
            StatementMergeResult mergeResult = statementMergeProcessor.mergeStatements(
                statementResult.getStatementData(), interbankResult.getInterbankData());
            if (!mergeResult.isSuccess()) {
                return StepResult.failed("Statement merging failed: " + mergeResult.getErrorMessage());
            }
            
            // Combine all results for next step
            Map<String, Object> outputData = Map.of(
                "accountData", accountResult.getAccountData(),
                "statementData", mergeResult.getMergedData(),
                "processingMetadata", createStep1Metadata()
            );
            
            log.info("Completed Step 1 processing for report: {}", report.getReportId());
            return StepResult.success("Step 1 completed successfully", outputData);
            
        } catch (Exception e) {
            log.error("Step 1 processing failed for report: {}", report.getReportId(), e);
            return StepResult.failed("Step 1 processing failed: " + e.getMessage());
        }
    }
    
    private Map<String, Object> createStep1Metadata() {
        return Map.of(
            "stepNumber", 1,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "subStepsCompleted", List.of("EBCMMC01", "EBCMMC02+STMBDD06", "EBCMMC03+STMBDD07", "EBCMMC04+STMMCG01")
        );
    }
}
```

#### 2. Step 6 Service (Core Payment Processing)
```java
package com.scb.bizo.step.step6;

/**
 * Step 6 processor handling core CN/IPS payment processing.
 * This is the most critical step - processes: EBCMMC08 + STMMCG05
 */
@Service
@Slf4j
public class Step6Service implements StepProcessor {
    
    private final CnIpsPaymentProcessor cnIpsPaymentProcessor;           // STMMCG05 - Core Logic
    private final PaymentValidationService paymentValidationService;
    private final PaymentStatusManager paymentStatusManager;
    private final ProductCodeValidator productCodeValidator;
    
    @Override
    public int getStepNumber() {
        return 6;
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 6 (Core Payment Processing) for report: {}", report.getReportId());
        
        try {
            // Extract payment data from previous steps
            Map<String, Object> stepData = inputData.getDataAsMap();
            List<PaymentTransaction> paymentTransactions = extractPaymentTransactions(stepData);
            
            if (paymentTransactions.isEmpty()) {
                log.warn("No payment transactions found for processing in Step 6");
                return StepResult.success("Step 6 completed - no payments to process", Map.of());
            }
            
            List<PaymentProcessingResult> processedPayments = new ArrayList<>();
            
            // Process each payment transaction through core STMMCG05 logic
            for (PaymentTransaction transaction : paymentTransactions) {
                log.debug("Processing payment transaction: {}", transaction.getTransactionId());
                
                // Core payment processing with COBOL business logic
                PaymentProcessingResult result = cnIpsPaymentProcessor.processPayment(transaction);
                processedPayments.add(result);
                
                if (!result.isSuccess()) {
                    log.error("Payment processing failed for transaction: {}, error: {}", 
                        transaction.getTransactionId(), result.getErrorMessage());
                }
            }
            
            // Validate processing results
            long successCount = processedPayments.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
            long totalCount = processedPayments.size();
            
            log.info("Step 6 completed: {}/{} payments processed successfully", successCount, totalCount);
            
            // Prepare output data for next step
            Map<String, Object> outputData = Map.of(
                "processedPayments", processedPayments,
                "successCount", successCount,
                "totalCount", totalCount,
                "processingMetadata", createStep6Metadata(successCount, totalCount)
            );
            
            return StepResult.success(
                String.format("Step 6 completed: %d/%d payments processed", successCount, totalCount),
                outputData
            );
            
        } catch (Exception e) {
            log.error("Step 6 processing failed for report: {}", report.getReportId(), e);
            return StepResult.failed("Step 6 processing failed: " + e.getMessage());
        }
    }
    
    @SuppressWarnings("unchecked")
    private List<PaymentTransaction> extractPaymentTransactions(Map<String, Object> stepData) {
        // Extract payment transactions from previous step results
        Object paymentsObj = stepData.get("paymentTransactions");
        if (paymentsObj instanceof List<?> list) {
            return list.stream()
                .filter(PaymentTransaction.class::isInstance)
                .map(PaymentTransaction.class::cast)
                .collect(Collectors.toList());
        }
        return List.of();
    }
    
    private Map<String, Object> createStep6Metadata(long successCount, long totalCount) {
        return Map.of(
            "stepNumber", 6,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "coreProcessor", "STMMCG05",
            "successRate", totalCount > 0 ? (double) successCount / totalCount : 0.0,
            "businessLogicPreserved", true
        );
    }
}
```

### Domain Model Classes

#### 1. MulticastReport (Aggregate Root)
```java
package com.scb.bizo.report.domain.model;

/**
 * Aggregate root for multicast report processing.
 * Encapsulates 8-step workflow state and business rules.
 */
@Entity
@Document(collection = "multicast_reports")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MulticastReport {
    
    @Id
    private ReportId reportId;
    private ReportStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime completedAt;
    private ReportData inputData;
    private ReportData outputData;
    
    @Builder.Default
    private List<ProcessingStep> completedSteps = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Complete a processing step with validation.
     */
    public void completeStep(int stepNumber, StepResult result) {
        validateStepCompletion(stepNumber);
        
        ProcessingStep step = ProcessingStep.builder()
            .stepNumber(stepNumber)
            .status(result.isSuccess() ? StepStatus.COMPLETED : StepStatus.FAILED)
            .startTime(LocalDateTime.now().minusSeconds(result.getProcessingTime()))
            .endTime(LocalDateTime.now())
            .result(result)
            .build();
        
        // Remove any existing step with same number (for retries)
        completedSteps.removeIf(s -> s.getStepNumber() == stepNumber);
        completedSteps.add(step);
        
        // Update overall status
        updateStatusAfterStep(stepNumber, result.isSuccess());
    }
    
    /**
     * Check if a specific step can be processed.
     */
    public boolean canProcessStep(int stepNumber) {
        if (stepNumber < 1 || stepNumber > 8) {
            return false;
        }
        
        if (stepNumber == 1) {
            return status == ReportStatus.SUBMITTED;
        }
        
        // Check if previous step is completed
        return isStepCompleted(stepNumber - 1);
    }
    
    /**
     * Check if a specific step is completed successfully.
     */
    public boolean isStepCompleted(int stepNumber) {
        return completedSteps.stream()
            .anyMatch(step -> step.getStepNumber() == stepNumber && 
                            step.getStatus() == StepStatus.COMPLETED);
    }
    
    /**
     * Check if all 8 steps are completed.
     */
    public boolean isComplete() {
        return completedSteps.stream()
            .filter(step -> step.getStatus() == StepStatus.COMPLETED)
            .mapToInt(ProcessingStep::getStepNumber)
            .distinct()
            .count() == 8;
    }
    
    /**
     * Get current processing progress (0-100%).
     */
    public double getProgress() {
        long completedCount = completedSteps.stream()
            .filter(step -> step.getStatus() == StepStatus.COMPLETED)
            .mapToInt(ProcessingStep::getStepNumber)
            .distinct()
            .count();
        return (completedCount / 8.0) * 100.0;
    }
    
    /**
     * Update report status.
     */
    public void updateStatus(ReportStatus newStatus) {
        this.status = newStatus;
        if (newStatus == ReportStatus.COMPLETED) {
            this.completedAt = LocalDateTime.now();
        }
    }
    
    /**
     * Add processing error.
     */
    public void addError(String error) {
        this.errors.add(error);
    }
    
    private void validateStepCompletion(int stepNumber) {
        if (!canProcessStep(stepNumber)) {
            throw new InvalidStepTransitionException(
                "Cannot process step " + stepNumber + " in current state. Status: " + status);
        }
    }
    
    private void updateStatusAfterStep(int stepNumber, boolean success) {
        if (!success) {
            this.status = ReportStatus.FAILED;
        } else if (stepNumber == 8) {
            this.status = ReportStatus.COMPLETED;
            this.completedAt = LocalDateTime.now();
        } else {
            this.status = ReportStatus.PROCESSING;
        }
    }
}
```

#### 2. ProcessingStep Entity
```java
package com.scb.bizo.report.domain.model;

/**
 * Entity representing individual processing step within the 8-step workflow.
 */
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingStep {
    
    private int stepNumber;
    private StepStatus status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private StepResult result;
    
    @Builder.Default
    private Map<String, Object> stepData = new HashMap<>();
    
    /**
     * Get processing duration in seconds.
     */
    public long getProcessingDurationSeconds() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return Duration.between(startTime, endTime).getSeconds();
    }
    
    /**
     * Check if step processing is in progress.
     */
    public boolean isInProgress() {
        return status == StepStatus.PROCESSING;
    }
    
    /**
     * Check if step completed successfully.
     */
    public boolean isSuccessful() {
        return status == StepStatus.COMPLETED && result != null && result.isSuccess();
    }
    
    /**
     * Get step description based on step number.
     */
    public String getStepDescription() {
        return switch (stepNumber) {
            case 1 -> "Initial Processing (EBCMMC01-04)";
            case 2 -> "Bill Payment Processing (EBCMMC05)";
            case 3 -> "EPP Processing (EBCMMC06)";
            case 4 -> "LCC Processing (EBCMMC07)";
            case 5 -> "RFT Processing (EBCMMC71)";
            case 6 -> "CN/IPS Core Processing (EBCMMC08)";
            case 7 -> "Statement Generation (EBCMMC09)";
            case 8 -> "Final Output (EBCMAFTB)";
            default -> "Unknown Step";
        };
    }
    
    /**
     * Get COBOL programs processed in this step.
     */
    public List<String> getCobolPrograms() {
        return switch (stepNumber) {
            case 1 -> List.of("EBCMMC01", "EBCMMC02+STMBDD06", "EBCMMC03+STMBDD07", "EBCMMC04+STMMCG01");
            case 2 -> List.of("EBCMMC05+STMBDD08+STMMCG02");
            case 3 -> List.of("EBCMMC06+STMMCG03");
            case 4 -> List.of("EBCMMC07+STMMCG04");
            case 5 -> List.of("EBCMMC71+STMMCG06");
            case 6 -> List.of("EBCMMC08+STMMCG05");
            case 7 -> List.of("EBCMMC09+STMMCREF+STMMCG07");
            case 8 -> List.of("EBCMAFTB");
            default -> List.of();
        };
    }
}
```

## Business Logic Implementation by Class

### Core Business Logic Classes

#### 1. CN/IPS Payment Processor (STMMCG05 - Most Critical)
```java
package com.scb.bizo.payment.processor;

/**
 * Core CN/IPS payment processor implementing STMMCG05 business logic.
 * This is the most critical component preserving exact COBOL business rules.
 */
@Service
@Slf4j
public class CnIpsPaymentProcessor {
    
    private final ProductCodeValidator productCodeValidator;
    private final PaymentAmountProcessor amountProcessor;
    private final PaymentStatusManager statusManager;
    private final PaymentRepository paymentRepository;
    
    /**
     * Process CN/IPS payment with complete STMMCG05 business logic preservation.
     * 
     * COBOL Business Rules Preserved:
     * 1. Product code mapping: DCP → BNT
     * 2. EWT pattern validation: EWT## (2 digits)
     * 3. Status management: 'C' and 'J' handling based on debit date
     * 4. Amount processing: COBOL decimal format with implied decimals
     * 5. Exception handling: Business rule violations and data errors
     */
    public PaymentProcessingResult processPayment(PaymentTransaction transaction) {
        log.debug("Processing CN/IPS payment: {}", transaction.getTransactionId());
        
        try {
            // Step 1: Validate and map product code (critical COBOL logic)
            ProductCodeMapping productMapping = productCodeValidator.validateAndMap(
                transaction.getProductCode());
            
            if (!productMapping.isValid()) {
                return PaymentProcessingResult.failed(
                    "Invalid product code: " + transaction.getProductCode(),
                    "INVALID_PRODUCT_CODE");
            }
            
            // Step 2: Process amount with COBOL format conversion
            AmountProcessingResult amountResult = amountProcessor.processAmount(
                transaction.getAmount(), productMapping.getMappedCode());
            
            if (!amountResult.isValid()) {
                return PaymentProcessingResult.failed(
                    "Invalid amount: " + amountResult.getErrorMessage(),
                    "INVALID_AMOUNT");
            }
            
            // Step 3: Apply COBOL business validation rules
            PaymentValidationResult validation = validatePaymentBusinessRules(
                transaction, productMapping, amountResult);
            
            if (!validation.isValid()) {
                return PaymentProcessingResult.failed(
                    "Business validation failed: " + validation.getErrorMessage(),
                    "BUSINESS_VALIDATION_FAILED");
            }
            
            // Step 4: Create processed payment with COBOL status logic
            ProcessedPayment processedPayment = ProcessedPayment.builder()
                .originalTransaction(transaction)
                .mappedProductCode(productMapping.getMappedCode())
                .processedAmount(amountResult.getProcessedAmount())
                .status(PaymentStatus.PROCESSED)
                .processedAt(LocalDateTime.now())
                .build();
            
            // Step 5: Apply status management rules from COBOL
            statusManager.applyCobolStatusRules(processedPayment);
            
            // Step 6: Save processed payment
            paymentRepository.save(processedPayment);
            
            log.debug("Successfully processed CN/IPS payment: {}", transaction.getTransactionId());
            
            return PaymentProcessingResult.success(processedPayment);
            
        } catch (Exception e) {
            log.error("Failed to process CN/IPS payment: {}", transaction.getTransactionId(), e);
            return PaymentProcessingResult.failed(
                "Processing failed: " + e.getMessage(),
                "PROCESSING_EXCEPTION");
        }
    }
    
    /**
     * Handle payment status updates with exact COBOL logic from STMMCG05.
     */
    public void updatePaymentStatus(PaymentId paymentId, String statusCode) {
        ProcessedPayment payment = paymentRepository.findById(paymentId)
            .orElseThrow(() -> new PaymentNotFoundException(paymentId));
        
        // Apply COBOL status management rules
        switch (statusCode.toUpperCase()) {
            case "C" -> handleCancellationStatus(payment);
            case "J" -> handleCancellationAfterDebit(payment);
            case "P" -> payment.updateStatus(PaymentStatus.PENDING);
            case "S" -> payment.updateStatus(PaymentStatus.SUCCESSFUL);
            default -> throw new InvalidStatusCodeException("Invalid status code: " + statusCode);
        }
        
        paymentRepository.save(payment);
    }
    
    /**
     * Handle 'C' status with COBOL business logic.
     * 'C' = Cancel Before Debit if no debit date
     * 'C' = Cancel After Debit if debit date exists
     */
    private void handleCancellationStatus(ProcessedPayment payment) {
        if (payment.getDebitDate() == null) {
            payment.updateStatus(PaymentStatus.CANCELLED_BEFORE_DEBIT);
            log.debug("Payment {} cancelled before debit", payment.getPaymentId());
        } else {
            payment.updateStatus(PaymentStatus.CANCELLED_AFTER_DEBIT);
            log.debug("Payment {} cancelled after debit", payment.getPaymentId());
        }
    }
    
    /**
     * Handle 'J' status with COBOL business logic.
     * 'J' always means cancelled after debit
     */
    private void handleCancellationAfterDebit(ProcessedPayment payment) {
        payment.updateStatus(PaymentStatus.CANCELLED_AFTER_DEBIT);
        log.debug("Payment {} cancelled after debit (J status)", payment.getPaymentId());
    }
    
    private PaymentValidationResult validatePaymentBusinessRules(
            PaymentTransaction transaction, 
            ProductCodeMapping productMapping, 
            AmountProcessingResult amountResult) {
        
        List<String> errors = new ArrayList<>();
        
        // Validate account eligibility for product
        if (!isAccountEligibleForProduct(transaction.getAccountNumber(), productMapping.getMappedCode())) {
            errors.add("Account not eligible for product: " + productMapping.getMappedCode());
        }
        
        // Validate amount limits for product
        if (!isAmountWithinLimits(amountResult.getProcessedAmount(), productMapping.getMappedCode())) {
            errors.add("Amount exceeds limits for product: " + productMapping.getMappedCode());
        }
        
        // Validate transaction type compatibility
        if (!isTransactionTypeCompatible(transaction.getType(), productMapping.getMappedCode())) {
            errors.add("Transaction type incompatible with product: " + productMapping.getMappedCode());
        }
        
        return errors.isEmpty() ? 
            PaymentValidationResult.valid() : 
            PaymentValidationResult.invalid(String.join("; ", errors));
    }
    
    // Additional validation methods...
    private boolean isAccountEligibleForProduct(String accountNumber, String productCode) {
        // Implement account eligibility business logic
        return true; // Simplified for example
    }
    
    private boolean isAmountWithinLimits(BigDecimal amount, String productCode) {
        // Implement amount limit business logic
        return true; // Simplified for example
    }
    
    private boolean isTransactionTypeCompatible(TransactionType type, String productCode) {
        // Implement compatibility business logic
        return true; // Simplified for example
    }
}
```

#### 2. Product Code Validator (STMMCG05 Logic)
```java
package com.scb.bizo.payment.validation;

/**
 * Product code validator implementing exact COBOL logic from STMMCG05.
 * Handles critical product code mapping and validation rules.
 */
@Component
@Slf4j
public class ProductCodeValidator {
    
    // COBOL mapping rules from STMMCG05 - CRITICAL BUSINESS LOGIC
    private static final Map<String, String> COBOL_PRODUCT_MAPPING = Map.of(
        "DCP", "BNT",  // Critical mapping from COBOL
        "EWT", "EWT",  // Pass-through mapping
        "IPS", "IPS"   // Pass-through mapping
    );
    
    private static final Pattern EWT_PATTERN = Pattern.compile("^EWT\\d{2}$");
    private static final Set<String> VALID_PRODUCT_CODES = Set.of(
        "BNT", "EWT", "IPS", "LCC", "EPP", "RFT"
    );
    
    /**
     * Validate and map product code according to exact STMMCG05 rules.
     * This is critical business logic that must match COBOL behavior exactly.
     */
    public ProductCodeMapping validateAndMap(String inputCode) {
        if (inputCode == null || inputCode.trim().isEmpty()) {
            return ProductCodeMapping.invalid("Product code cannot be null or empty");
        }
        
        String trimmedCode = inputCode.trim().toUpperCase();
        log.debug("Validating product code: {}", trimmedCode);
        
        // Step 1: Apply COBOL mapping rules first
        String mappedCode = COBOL_PRODUCT_MAPPING.getOrDefault(trimmedCode, trimmedCode);
        log.debug("Mapped {} to {}", trimmedCode, mappedCode);
        
        // Step 2: Validate mapped code according to COBOL rules
        ValidationResult validation = validateMappedCode(mappedCode);
        
        if (!validation.isValid()) {
            return ProductCodeMapping.invalid(
                "Invalid product code '" + inputCode + "': " + validation.getErrorMessage());
        }
        
        ProductCodeMapping mapping = ProductCodeMapping.builder()
            .originalCode(trimmedCode)
            .mappedCode(mappedCode)
            .valid(true)
            .description(getProductDescription(mappedCode))
            .requiresSpecialProcessing(requiresSpecialProcessing(mappedCode))
            .build();
        
        log.debug("Product code validation successful: {} -> {}", inputCode, mappedCode);
        return mapping;
    }
    
    /**
     * Validate mapped product code according to COBOL business rules.
     */
    private ValidationResult validateMappedCode(String code) {
        // Check standard valid codes
        if (VALID_PRODUCT_CODES.contains(code)) {
            return ValidationResult.valid();
        }
        
        // Check EWT pattern (COBOL business rule: EWT + 2 digits)
        if (EWT_PATTERN.matcher(code).matches()) {
            return ValidationResult.valid();
        }
        
        return ValidationResult.invalid("Code does not match any valid pattern");
    }
    
    /**
     * Check if product code requires special processing according to COBOL logic.
     */
    public boolean requiresSpecialProcessing(String productCode) {
        Set<String> specialProcessingCodes = Set.of("EWT", "IPS", "RFT");
        return specialProcessingCodes.contains(productCode) || 
               EWT_PATTERN.matcher(productCode).matches();
    }
    
    /**
     * Get business description for product code.
     */
    private String getProductDescription(String code) {
        if (EWT_PATTERN.matcher(code).matches()) {
            return "Electronic Wire Transfer (" + code + ")";
        }
        
        Map<String, String> descriptions = Map.of(
            "BNT", "Bank Note Transfer",
            "EWT", "Electronic Wire Transfer",
            "IPS", "Interbank Payment System",
            "LCC", "Local Clearing Collection",
            "EPP", "Electronic Payment Processing",
            "RFT", "Real-time Fund Transfer"
        );
        
        return descriptions.getOrDefault(code, "Unknown Product Code");
    }
    
    /**
     * Validate product code pattern without mapping.
     */
    public boolean isValidPattern(String productCode) {
        if (productCode == null || productCode.trim().isEmpty()) {
            return false;
        }
        
        String code = productCode.trim().toUpperCase();
        
        // Check if it's a mappable code
        if (COBOL_PRODUCT_MAPPING.containsKey(code)) {
            return true;
        }
        
        // Check if it's already a valid target code
        if (VALID_PRODUCT_CODES.contains(code)) {
            return true;
        }
        
        // Check EWT pattern
        return EWT_PATTERN.matcher(code).matches();
    }
}
```

#### 3. Statement Merge Processor (STMMCG01)
```java
package com.scb.bizo.statement.processor;

/**
 * Statement merge processor implementing STMMCG01 business logic.
 * Handles merging of interbank and historical statements.
 */
@Service
@Slf4j
public class StatementMergeProcessor {
    
    private final StatementValidator statementValidator;
    private final StatementFormatter statementFormatter;
    
    /**
     * Merge statements following STMMCG01 business logic.
     * Creates 525-byte records as per COBOL requirements.
     */
    public StatementMergeResult mergeStatements(StatementData statementData, InterbankData interbankData) {
        log.debug("Starting statement merge process");
        
        try {
            // Step 1: Validate input data
            ValidationResult validation = validateInputData(statementData, interbankData);
            if (!validation.isValid()) {
                return StatementMergeResult.failed("Input validation failed: " + validation.getErrorMessage());
            }
            
            // Step 2: Apply business prioritization rules
            List<StatementLine> prioritizedLines = applyMergePrioritization(
                statementData.getStatementLines(), 
                interbankData.getInterbankLines());
            
            // Step 3: Merge data with business rules
            MergedStatementData mergedData = performMergeOperation(prioritizedLines);
            
            // Step 4: Format to 525-byte records (COBOL requirement)
            List<String> formattedRecords = statementFormatter.formatTo525ByteRecords(mergedData);
            
            // Step 5: Validate merged output
            ValidationResult outputValidation = validateMergedOutput(formattedRecords);
            if (!outputValidation.isValid()) {
                return StatementMergeResult.failed("Output validation failed: " + outputValidation.getErrorMessage());
            }
            
            StatementMergeResult result = StatementMergeResult.builder()
                .success(true)
                .mergedData(mergedData)
                .formattedRecords(formattedRecords)
                .totalRecords(formattedRecords.size())
                .build();
            
            log.debug("Statement merge completed successfully: {} records", formattedRecords.size());
            return result;
            
        } catch (Exception e) {
            log.error("Statement merge failed", e);
            return StatementMergeResult.failed("Merge operation failed: " + e.getMessage());
        }
    }
    
    /**
     * Apply business prioritization rules for statement merging.
     * Implements COBOL business logic for statement precedence.
     */
    private List<StatementLine> applyMergePrioritization(
            List<StatementLine> statementLines, 
            List<InterbankLine> interbankLines) {
        
        List<StatementLine> prioritizedLines = new ArrayList<>();
        
        // Priority 1: Interbank transactions (highest priority)
        prioritizedLines.addAll(interbankLines.stream()
            .map(this::convertToStatementLine)
            .collect(Collectors.toList()));
        
        // Priority 2: Regular statement lines (sorted by business criteria)
        prioritizedLines.addAll(statementLines.stream()
            .sorted(this::compareStatementLines)
            .collect(Collectors.toList()));
        
        // Apply deduplication rules
        return applyDeduplicationRules(prioritizedLines);
    }
    
    /**
     * Perform actual merge operation with business rules.
     */
    private MergedStatementData performMergeOperation(List<StatementLine> prioritizedLines) {
        MergedStatementData.Builder builder = MergedStatementData.builder();
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        int lineCount = 0;
        
        for (StatementLine line : prioritizedLines) {
            // Apply business transformation rules
            TransformedLine transformedLine = applyBusinessTransformation(line);
            builder.addLine(transformedLine);
            
            // Accumulate totals
            totalAmount = totalAmount.add(transformedLine.getAmount());
            lineCount++;
        }
        
        return builder
            .totalAmount(totalAmount)
            .lineCount(lineCount)
            .mergedAt(LocalDateTime.now())
            .build();
    }
    
    private ValidationResult validateInputData(StatementData statementData, InterbankData interbankData) {
        List<String> errors = new ArrayList<>();
        
        if (statementData == null || statementData.getStatementLines().isEmpty()) {
            errors.add("Statement data is null or empty");
        }
        
        if (interbankData == null || interbankData.getInterbankLines().isEmpty()) {
            errors.add("Interbank data is null or empty");
        }
        
        return errors.isEmpty() ? 
            ValidationResult.valid() : 
            ValidationResult.invalid(String.join("; ", errors));
    }
    
    private StatementLine convertToStatementLine(InterbankLine interbankLine) {
        // Convert interbank line to statement line format
        return StatementLine.builder()
            .lineType(LineType.INTERBANK)
            .amount(interbankLine.getAmount())
            .description(interbankLine.getDescription())
            .transactionDate(interbankLine.getTransactionDate())
            .referenceNumber(interbankLine.getReferenceNumber())
            .build();
    }
    
    private int compareStatementLines(StatementLine line1, StatementLine line2) {
        // Business sorting rules: date first, then amount, then reference
        int dateComparison = line1.getTransactionDate().compareTo(line2.getTransactionDate());
        if (dateComparison != 0) return dateComparison;
        
        int amountComparison = line1.getAmount().compareTo(line2.getAmount());
        if (amountComparison != 0) return amountComparison;
        
        return line1.getReferenceNumber().compareTo(line2.getReferenceNumber());
    }
    
    private List<StatementLine> applyDeduplicationRules(List<StatementLine> lines) {
        // Remove duplicates based on business key (reference number + amount)
        Map<String, StatementLine> uniqueLines = new LinkedHashMap<>();
        
        for (StatementLine line : lines) {
            String businessKey = line.getReferenceNumber() + "|" + line.getAmount();
            if (!uniqueLines.containsKey(businessKey)) {
                uniqueLines.put(businessKey, line);
            }
        }
        
        return new ArrayList<>(uniqueLines.values());
    }
    
    private TransformedLine applyBusinessTransformation(StatementLine line) {
        // Apply business transformation rules
        return TransformedLine.builder()
            .originalLine(line)
            .transformedAmount(line.getAmount())
            .transformedDescription(line.getDescription())
            .lineNumber(generateLineNumber())
            .build();
    }
    
    private ValidationResult validateMergedOutput(List<String> formattedRecords) {
        List<String> errors = new ArrayList<>();
        
        // Validate record format (525 bytes each)
        for (int i = 0; i < formattedRecords.size(); i++) {
            String record = formattedRecords.get(i);
            if (record.length() != 525) {
                errors.add("Record " + (i + 1) + " length is " + record.length() + ", expected 525");
            }
        }
        
        return errors.isEmpty() ? 
            ValidationResult.valid() : 
            ValidationResult.invalid(String.join("; ", errors));
    }
    
    private int generateLineNumber() {
        // Generate sequential line number
        return ThreadLocalRandom.current().nextInt(1, 9999);
    }
}
```

## 8-Step Service Orchestration

### Step Execution Flow

#### Sequential Step Processing
```java
/**
 * The 8-step processing must be executed sequentially with proper dependency management.
 * Each step depends on the successful completion of the previous step.
 */
public class StepExecutionFlow {
    
    /**
     * Step execution dependencies:
     * 
     * Input File → Step 1 → Step 2 → Step 3 → Step 4 → Step 5 → Step 6 → Step 7 → Step 8 → Output File
     *     ↓          ↓        ↓        ↓        ↓        ↓        ↓        ↓        ↓         ↓
     *   Read      Account   Bill    EPP      LCC      RFT    CN/IPS   Statement  Final   Write
     *             Setup   Payment           Processing        Core     Generation Output
     */
    
    public static final Map<Integer, StepInfo> STEP_DEFINITIONS = Map.of(
        1, new StepInfo(1, "Initial Processing", List.of("EBCMMC01", "EBCMMC02+STMBDD06", "EBCMMC03+STMBDD07", "EBCMMC04+STMMCG01")),
        2, new StepInfo(2, "Bill Payment Processing", List.of("EBCMMC05+STMBDD08+STMMCG02")),
        3, new StepInfo(3, "EPP Processing", List.of("EBCMMC06+STMMCG03")),
        4, new StepInfo(4, "LCC Processing", List.of("EBCMMC07+STMMCG04")),
        5, new StepInfo(5, "RFT Processing", List.of("EBCMMC71+STMMCG06")),
        6, new StepInfo(6, "CN/IPS Core Processing", List.of("EBCMMC08+STMMCG05")),
        7, new StepInfo(7, "Statement Generation", List.of("EBCMMC09+STMMCREF+STMMCG07")),
        8, new StepInfo(8, "Final Output", List.of("EBCMAFTB"))
    );
}
```

### Step Interface Definition
```java
package com.scb.bizo.step.api;

/**
 * Interface for all step processors in the 8-step workflow.
 * Each step must implement this interface to ensure consistent behavior.
 */
public interface StepProcessor {
    
    /**
     * Get the step number (1-8).
     */
    int getStepNumber();
    
    /**
     * Get step description for logging and monitoring.
     */
    default String getStepDescription() {
        return "Step " + getStepNumber();
    }
    
    /**
     * Get COBOL programs implemented by this step.
     */
    List<String> getCobolPrograms();
    
    /**
     * Process the step with input data and return result.
     */
    StepResult processStep(MulticastReport report, ReportData inputData);
    
    /**
     * Validate step prerequisites before processing.
     */
    default ValidationResult validatePrerequisites(MulticastReport report, ReportData inputData) {
        if (report == null) {
            return ValidationResult.invalid("Report cannot be null");
        }
        
        if (inputData == null) {
            return ValidationResult.invalid("Input data cannot be null");
        }
        
        if (!report.canProcessStep(getStepNumber())) {
            return ValidationResult.invalid("Step " + getStepNumber() + " cannot be processed in current report state");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Check if step can be retried in case of failure.
     */
    default boolean canRetry() {
        return true;
    }
    
    /**
     * Get maximum retry attempts for this step.
     */
    default int getMaxRetryAttempts() {
        return 3;
    }
}
```

### Complete Step Implementation Example - Step 8 (Final Output)
```java
package com.scb.bizo.step.step8;

/**
 * Step 8 processor handling final output generation.
 * Processes: EBCMAFTB - Job completion and output file generation
 */
@Service
@Slf4j
public class Step8Service implements StepProcessor {
    
    private final FileService fileService;
    private final OutputFormatter outputFormatter;
    private final ReportFinalizer reportFinalizer;
    private final EventPublisher eventPublisher;
    
    @Override
    public int getStepNumber() {
        return 8;
    }
    
    @Override
    public String getStepDescription() {
        return "Final Output Generation (EBCMAFTB)";
    }
    
    @Override
    public List<String> getCobolPrograms() {
        return List.of("EBCMAFTB");
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 8 (Final Output) for report: {}", report.getReportId());
        
        try {
            // Step 1: Validate all previous steps completed
            ValidationResult stepValidation = validateAllStepsCompleted(report);
            if (!stepValidation.isValid()) {
                return StepResult.failed("Step validation failed: " + stepValidation.getErrorMessage());
            }
            
            // Step 2: Prepare final output data
            FinalOutputData outputData = prepareFinalOutputData(report, inputData);
            
            // Step 3: Format output according to business requirements
            FormattedOutput formattedOutput = outputFormatter.formatFinalOutput(outputData);
            
            // Step 4: Generate output file (equivalent to COBOL file output)
            OutputFileResult fileResult = fileService.generateOutputFile(
                report.getReportId(), formattedOutput);
            
            if (!fileResult.isSuccess()) {
                return StepResult.failed("Output file generation failed: " + fileResult.getErrorMessage());
            }
            
            // Step 5: Finalize report processing
            ReportFinalizationResult finalizationResult = reportFinalizer.finalizeReport(
                report, fileResult.getOutputFile());
            
            if (!finalizationResult.isSuccess()) {
                return StepResult.failed("Report finalization failed: " + finalizationResult.getErrorMessage());
            }
            
            // Step 6: Publish completion event
            eventPublisher.publish(new ReportCompletedEvent(
                report.getReportId(), 
                LocalDateTime.now(),
                fileResult.getOutputFile()));
            
            // Prepare output data for final result
            Map<String, Object> resultData = Map.of(
                "outputFile", fileResult.getOutputFile(),
                "totalRecords", formattedOutput.getTotalRecords(),
                "fileSize", fileResult.getFileSize(),
                "processingMetadata", createStep8Metadata(report, formattedOutput)
            );
            
            log.info("Successfully completed Step 8 for report: {}", report.getReportId());
            return StepResult.success("Step 8 completed - Output file generated", resultData);
            
        } catch (Exception e) {
            log.error("Step 8 processing failed for report: {}", report.getReportId(), e);
            return StepResult.failed("Step 8 processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Validate that all previous steps (1-7) are completed successfully.
     */
    private ValidationResult validateAllStepsCompleted(MulticastReport report) {
        List<String> errors = new ArrayList<>();
        
        for (int stepNumber = 1; stepNumber <= 7; stepNumber++) {
            if (!report.isStepCompleted(stepNumber)) {
                errors.add("Step " + stepNumber + " is not completed");
            }
        }
        
        return errors.isEmpty() ? 
            ValidationResult.valid() : 
            ValidationResult.invalid(String.join("; ", errors));
    }
    
    /**
     * Prepare final output data by aggregating results from all previous steps.
     */
    private FinalOutputData prepareFinalOutputData(MulticastReport report, ReportData inputData) {
        FinalOutputData.Builder builder = FinalOutputData.builder()
            .reportId(report.getReportId())
            .originalInputData(report.getInputData())
            .finalInputData(inputData);
        
        // Aggregate data from all completed steps
        for (ProcessingStep step : report.getCompletedSteps()) {
            StepResult stepResult = step.getResult();
            if (stepResult.isSuccess() && stepResult.getOutputData() != null) {
                builder.addStepData(step.getStepNumber(), stepResult.getOutputData());
            }
        }
        
        return builder
            .processedAt(LocalDateTime.now())
            .totalProcessingSteps(8)
            .build();
    }
    
    private Map<String, Object> createStep8Metadata(MulticastReport report, FormattedOutput formattedOutput) {
        return Map.of(
            "stepNumber", 8,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "cobolProgram", "EBCMAFTB",
            "totalRecords", formattedOutput.getTotalRecords(),
            "processingDuration", calculateTotalProcessingDuration(report),
            "allStepsCompleted", true
        );
    }
    
    private Duration calculateTotalProcessingDuration(MulticastReport report) {
        if (report.getCreatedAt() == null) {
            return Duration.ZERO;
        }
        return Duration.between(report.getCreatedAt(), LocalDateTime.now());
    }
}
```

## COBOL-to-Java Mapping Reference

### Complete Program Implementation Mapping

| Step | COBOL Program | Java Implementation | Package | Business Logic Focus | Priority |
|------|---------------|-------------------|---------|---------------------|----------|
| **1** | **EBCMMC01** | `AccountSetupProcessor` | `com.scb.bizo.step.step1` | Account profile setup and validation | HIGH |
| **1** | **EBCMMC02 + STMBDD06** | `StatementProcessor` | `com.scb.bizo.step.step1` | Statement processing with account filtering | HIGH |
| **1** | **EBCMMC03 + STMBDD07** | `InterbankProcessor` | `com.scb.bizo.step.step1` | Interbank statement processing | HIGH |
| **1** | **EBCMMC04 + STMMCG01** | `StatementMergeProcessor` | `com.scb.bizo.step.step1` | Statement merging and consolidation | HIGH |
| **2** | **EBCMMC05 + STMBDD08 + STMMCG02** | `BillPaymentProcessor` | `com.scb.bizo.step.step2` | Bill payment processing with PromptPay | HIGH |
| **3** | **EBCMMC06 + STMMCG03** | `EppProcessor` | `com.scb.bizo.step.step3` | Electronic payment processing | MEDIUM |
| **4** | **EBCMMC07 + STMMCG04** | `LccProcessor` | `com.scb.bizo.step.step4` | Local clearing collection processing | MEDIUM |
| **5** | **EBCMMC71 + STMMCG06** | `RftProcessor` | `com.scb.bizo.step.step5` | Real-time fund transfer processing | MEDIUM |
| **6** | **EBCMMC08 + STMMCG05** | `CnIpsPaymentProcessor` | `com.scb.bizo.step.step6` | **Core CN/IPS payment processing** | **CRITICAL** |
| **7** | **EBCMMC09 + STMMCREF + STMMCG07** | `StatementGenerationProcessor` | `com.scb.bizo.step.step7` | Statement generation and reference processing | HIGH |
| **8** | **EBCMAFTB** | `FinalOutputProcessor` | `com.scb.bizo.step.step8` | Final output generation and file creation | HIGH |

### Business Logic Component Mapping

| COBOL Business Logic | Java Implementation | Method/Class | Package | Description |
|---------------------|-------------------|--------------|---------|-------------|
| **Product Code Mapping (DCP→BNT)** | `ProductCodeValidator.validateAndMap()` | `validateAndMap(String)` | `com.scb.bizo.payment.validation` | Critical STMMCG05 product mapping |
| **EWT Pattern Validation** | `ProductCodeValidator.validateEwtPattern()` | `validateEwtPattern(String)` | `com.scb.bizo.payment.validation` | EWT## pattern validation |
| **Status Management (C/J)** | `PaymentStatusManager.updateStatus()` | `updateStatus(Payment, String)` | `com.scb.bizo.payment.service` | COBOL status handling logic |
| **Amount Processing** | `PaymentAmountProcessor.processAmount()` | `processAmount(String)` | `com.scb.bizo.common.util` | COBOL decimal format conversion |
| **Bill Payment Validation** | `BillerValidator.validateBillerId()` | `validateBillerId(String)` | `com.scb.bizo.payment.validation` | PromptPay BILLER-ID validation |
| **Statement Merging** | `StatementMergeProcessor.mergeStatements()` | `mergeStatements(List)` | `com.scb.bizo.statement.processor` | Statement consolidation logic |
| **Account Filtering** | `AccountFilterService.filterAccounts()` | `filterAccounts(List)` | `com.scb.bizo.account.service` | Account profile filtering |
| **Date Conversion** | `DateConverter.convertCobolDate()` | `convertCobolDate(String)` | `com.scb.bizo.common.util` | COBOL date format handling |
| **Record Parsing** | `RecordParser.parseFixedLength()` | `parseFixedLength(String, int)` | `com.scb.bizo.common.util` | Fixed-length record processing |
| **File Processing** | `FileProcessor.processFile()` | `processFile(InputStream)` | `com.scb.bizo.file.service` | Input/output file handling |

### Data Structure and Record Format Mapping

| COBOL Data Structure | Java Entity/Model | Record Length | Key Fields | Package |
|---------------------|------------------|---------------|------------|---------|
| **Input File Record** | `InputFileRecord` | Variable | recordType, data, sequence | `com.scb.bizo.file.model` |
| **Account Profile** | `AccountProfile` | 350 chars | accountNumber, accountType, status | `com.scb.bizo.account.model` |
| **Statement Record** | `StatementRecord` | 175/525 chars | statementId, accountNumber, amount | `com.scb.bizo.statement.model` |
| **Bill Payment Detail** | `BillPaymentDetail` | 285 chars | paymentId, billerId, amount, date | `com.scb.bizo.payment.model` |
| **EPP Record** | `EppRecord` | 700 chars | eppId, transactionType, amount | `com.scb.bizo.payment.model` |
| **LCC Record** | `LccRecord` | 1300 chars | lccId, transactionType, amount | `com.scb.bizo.payment.model` |
| **CN/IPS Payment** | `CnIpsPayment` | Variable | paymentId, productCode, amount, status | `com.scb.bizo.payment.model` |
| **Reference Statement** | `ReferenceStatement` | 3200 chars | referenceId, sections, lookup | `com.scb.bizo.statement.model` |
| **Output File Record** | `OutputFileRecord` | Variable | recordType, data, checksum | `com.scb.bizo.file.model` |

### Step-to-Service Mapping

| Step | Service Call Chain | Input Source | Output Target | Dependencies |
|------|-------------------|--------------|---------------|-------------|
| **1** | `Step1Service` → `AccountSetupProcessor` → `StatementProcessor` → `InterbankProcessor` → `StatementMergeProcessor` | Input File | Step 2 Data | None |
| **2** | `Step2Service` → `BillPaymentProcessor` | Step 1 Output | Step 3 Data | Step 1 Complete |
| **3** | `Step3Service` → `EppProcessor` | Step 2 Output | Step 4 Data | Step 2 Complete |
| **4** | `Step4Service` → `LccProcessor` | Step 3 Output | Step 5 Data | Step 3 Complete |
| **5** | `Step5Service` → `RftProcessor` | Step 4 Output | Step 6 Data | Step 4 Complete |
| **6** | `Step6Service` → `CnIpsPaymentProcessor` | Step 5 Output | Step 7 Data | Step 5 Complete |
| **7** | `Step7Service` → `StatementGenerationProcessor` | Step 6 Output | Step 8 Data | Step 6 Complete |
| **8** | `Step8Service` → `FinalOutputProcessor` | Step 7 Output | Output File | Step 7 Complete |

## Migration Strategy and Implementation Order

### Phase-Based Implementation Strategy

#### Phase 1: Foundation and Infrastructure (Weeks 1-4)

**Week 1: Project Setup and Core Infrastructure**
```bash
# Create Spring Boot project with Java 21
mvn archetype:generate \
  -DgroupId=com.scb.bizo \
  -DartifactId=multicast-report-library \
  -DarchetypeArtifactId=maven-archetype-quickstart \
  -DinteractiveMode=false

# Set up package structure
mkdir -p src/main/java/com/scb/bizo/{report,step,common}
mkdir -p src/main/java/com/scb/bizo/report/{api,application,domain,infrastructure}
mkdir -p src/main/java/com/scb/bizo/step/{step1,step2,step3,step4,step5,step6,step7,step8}
mkdir -p src/main/java/com/scb/bizo/common/{util,exception,validation,constant}
```

**Implementation Tasks:**
- [x] Create Spring Boot 3.x project with Java 21 and virtual threads
- [x] Set up MongoDB NoSQL database integration
- [x] Configure Maven build with all required dependencies
- [x] Implement basic Spring configuration classes
- [x] Set up logging framework (SLF4J/Logback)
- [x] Create package structure following `com.scb.bizo` namespace

**Week 2: Domain Models and Value Objects**
**Implementation Order:**
1. `ReportId`, `StepResult`, `ValidationResult` (foundational value objects)
2. `ReportData`, `ProcessingStep` (core processing entities)
3. `MulticastReport` (main aggregate root)
4. `PaymentTransaction`, `AccountProfile` (business entities)
5. Enums: `ReportStatus`, `StepStatus`, `PaymentStatus`, `TransactionType`

**Week 3: Common Utilities and Infrastructure Interfaces**
- Date processing utilities for COBOL date conversion
- Amount processing utilities for COBOL decimal handling
- Record parsing utilities for fixed-length records
- File service interfaces for input/output handling
- Repository interfaces for data persistence
- Event publisher interfaces for step coordination

**Week 4: Core Business Rules and Validation Framework**
- Input validation framework
- Business rule validation engine
- Error handling and exception management
- Logging and monitoring setup

#### Phase 2: Step Interface and Base Services (Weeks 5-6)

**Week 5: Step Processor Framework**
```java
// Implementation checklist for Step Framework
✅ StepProcessor interface definition
✅ Base step service implementation
✅ Step validation framework
✅ Step result handling
✅ Error handling and retry logic
✅ Step orchestration framework
```

**Week 6: Report Orchestrator and File Services**
- Complete report orchestrator implementation
- File service implementations for input/output
- Status tracking service
- Event publishing framework

#### Phase 3: Core Step Implementations (Weeks 7-14) - Critical Phase

**Week 7-8: Step 1 Implementation (Foundation Step)**
- `AccountSetupProcessor` (EBCMMC01)
- `StatementProcessor` (EBCMMC02 + STMBDD06)
- `InterbankProcessor` (EBCMMC03 + STMBDD07)
- `StatementMergeProcessor` (EBCMMC04 + STMMCG01)

**Week 9: Step 2 Implementation**
- `BillPaymentProcessor` (EBCMMC05 + STMBDD08 + STMMCG02)
- PromptPay BILLER-ID validation
- 285-character record processing

**Week 10: Steps 3-5 Implementation**
- `EppProcessor` (EBCMMC06 + STMMCG03) - 700-character records
- `LccProcessor` (EBCMMC07 + STMMCG04) - 1300-character records
- `RftProcessor` (EBCMMC71 + STMMCG06) - Real-time processing

**Week 11-12: Step 6 Implementation (MOST CRITICAL)**
```java
// Critical implementation checklist for Step 6 (STMMCG05)
✅ Product code validation and mapping (DCP→BNT)
✅ EWT pattern validation (EWT##)
✅ Status management ('C', 'J' handling)
✅ Amount processing from COBOL format
✅ Payment validation business rules
✅ Exception handling and audit logging
✅ Comprehensive unit tests (>95% coverage)
```

**Week 13: Step 7 Implementation**
- `StatementGenerationProcessor` (EBCMMC09 + STMMCREF + STMMCG07)
- 3200-character record processing
- Reference data integration

**Week 14: Step 8 Implementation**
- `FinalOutputProcessor` (EBCMAFTB)
- Output file generation
- Report finalization

#### Phase 4: Integration and Testing (Weeks 15-18)

**Week 15: Service Integration**
- Complete 8-step workflow integration
- Service communication and data flow
- Error handling and recovery

**Week 16: API Layer Implementation**
- REST controllers for report processing
- Request/response handling
- Input validation and error formatting

**Week 17: Comprehensive Testing**
- End-to-end workflow testing
- Business logic validation
- Performance testing
- Integration testing

**Week 18: Documentation and Finalization**
- Complete API documentation
- Business logic documentation
- Deployment guides
- Performance optimization

### Implementation Dependencies and Critical Path

#### Critical Path Dependencies
```
Foundation → Step Framework → Step 1 → Step 6 (CRITICAL) → Step 7 → Step 8 → Integration
     ↓              ↓           ↓              ↓              ↓        ↓           ↓
Utilities → Interfaces → Account → Core Payment → Statement → Output → API Layer
```

#### Parallel Implementation Opportunities
```
Steps 2, 3, 4, 5 can be implemented in parallel after Step 1 is complete
Testing can be implemented in parallel with step development
Documentation can be written in parallel with implementation
```

### Risk Mitigation Strategy

#### High-Risk Components
1. **Step 6 (STMMCG05)** - Most critical COBOL logic preservation
2. **File I/O Operations** - Input/output format compatibility
3. **Sequential Processing** - Step dependency management
4. **Data Format Conversion** - COBOL to Java data mapping

#### Mitigation Approaches
1. **Intensive Testing**: >95% coverage for critical components
2. **COBOL Reference Implementation**: Side-by-side validation
3. **Incremental Delivery**: Step-by-step validation and testing
4. **Comprehensive Documentation**: Business logic preservation tracking

## Testing Strategy and Quality Assurance

### Testing Pyramid for 8-Step Workflow

#### 1. Unit Tests (70% - Individual Step Logic)

**Step 6 Critical Testing (STMMCG05)**
```java
@ExtendWith(MockitoExtension.class)
class CnIpsPaymentProcessorTest {
    
    @Mock
    private ProductCodeValidator productCodeValidator;
    
    @Mock
    private PaymentRepository paymentRepository;
    
    @InjectMocks
    private CnIpsPaymentProcessor processor;
    
    @Test
    void shouldProcessValidCnIpsPaymentWithDcpMapping() {
        // Given: Payment with DCP product code (critical COBOL mapping)
        PaymentTransaction transaction = PaymentTransaction.builder()
            .transactionId(new TransactionId("TXN001"))
            .accountNumber("**********")
            .productCode("DCP")  // Should map to BNT
            .amount(new BigDecimal("1000.00"))
            .type(TransactionType.CN_IPS)
            .build();
        
        ProductCodeMapping mapping = ProductCodeMapping.builder()
            .originalCode("DCP")
            .mappedCode("BNT")
            .valid(true)
            .build();
        
        when(productCodeValidator.validateAndMap("DCP")).thenReturn(mapping);
        
        ProcessedPayment savedPayment = createMockProcessedPayment();
        when(paymentRepository.save(any(ProcessedPayment.class))).thenReturn(savedPayment);
        
        // When: Process payment
        PaymentProcessingResult result = processor.processPayment(transaction);
        
        // Then: Verify COBOL business logic preserved
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getProcessedPayment().getMappedProductCode()).isEqualTo("BNT");
        
        verify(productCodeValidator).validateAndMap("DCP");
        verify(paymentRepository).save(any(ProcessedPayment.class));
    }
    
    @Test
    void shouldHandleCobolStatusManagementCorrectly() {
        // Test COBOL status logic: 'C' and 'J' handling
        PaymentId paymentId = new PaymentId("PAY001");
        ProcessedPayment payment = createProcessedPayment();
        
        when(paymentRepository.findById(paymentId)).thenReturn(Optional.of(payment));
        
        // Test 'C' status without debit date
        payment.setDebitDate(null);
        processor.updatePaymentStatus(paymentId, "C");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_BEFORE_DEBIT);
        
        // Test 'C' status with debit date
        payment.setDebitDate(LocalDateTime.now());
        processor.updatePaymentStatus(paymentId, "C");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_AFTER_DEBIT);
        
        // Test 'J' status
        processor.updatePaymentStatus(paymentId, "J");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_AFTER_DEBIT);
    }
    
    @ParameterizedTest
    @CsvSource({
        "EWT01, true",
        "EWT99, true",
        "EWT1, false",
        "EWTABC, false"
    })
    void shouldValidateEwtPatternFromCobol(String productCode, boolean expectedValid) {
        // Test EWT pattern validation from COBOL
        boolean result = productCodeValidator.isValidPattern(productCode);
        assertThat(result).isEqualTo(expectedValid);
    }
}
```

**Step Processing Integration Tests**
```java
@Test
void shouldProcessStepsSequentiallyWithValidation() {
    // Given: Valid multicast report
    MulticastReport report = createValidReport();
    ReportData inputData = createValidInputData();
    
    // When: Process through all 8 steps
    for (int step = 1; step <= 8; step++) {
        StepProcessor processor = stepProcessors.get(step);
        
        // Validate prerequisites
        ValidationResult validation = processor.validatePrerequisites(report, inputData);
        assertThat(validation.isValid()).isTrue();
        
        // Process step
        StepResult result = processor.processStep(report, inputData);
        assertThat(result.isSuccess()).isTrue();
        
        // Update report and prepare for next step
        report.completeStep(step, result);
        inputData = new ReportData(result.getOutputData());
    }
    
    // Then: Verify all steps completed
    assertThat(report.isComplete()).isTrue();
    assertThat(report.getProgress()).isEqualTo(100.0);
}
```

#### 2. Integration Tests (20% - Step Coordination)

**8-Step Workflow Integration Test**
```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.data.mongodb.uri=mongodb://localhost:27017/test-db"
})
class MulticastReportWorkflowIntegrationTest {
    
    @Autowired
    private ReportOrchestrator reportOrchestrator;
    
    @Autowired
    private ReportRepository reportRepository;
    
    @Test
    @Transactional
    void shouldProcessCompleteEightStepWorkflow() {
        // Given: Valid multicast report with input data
        MulticastReport report = MulticastReport.builder()
            .reportId(ReportId.generate())
            .status(ReportStatus.SUBMITTED)
            .inputData(createValidInputData())
            .createdAt(LocalDateTime.now())
            .build();
        
        report = reportRepository.save(report);
        
        // When: Process through complete 8-step workflow
        reportOrchestrator.processReportAsync(report);
        
        // Wait for async processing to complete
        await().atMost(30, TimeUnit.SECONDS)
            .until(() -> {
                Optional<MulticastReport> updated = reportRepository.findById(report.getReportId());
                return updated.isPresent() && updated.get().isComplete();
            });
        
        // Then: Verify complete workflow execution
        MulticastReport completedReport = reportRepository.findById(report.getReportId()).orElseThrow();
        
        assertThat(completedReport.getStatus()).isEqualTo(ReportStatus.COMPLETED);
        assertThat(completedReport.getCompletedSteps()).hasSize(8);
        assertThat(completedReport.getProgress()).isEqualTo(100.0);
        assertThat(completedReport.getOutputData()).isNotNull();
        
        // Verify step completion order
        List<Integer> stepOrder = completedReport.getCompletedSteps().stream()
            .filter(step -> step.getStatus() == StepStatus.COMPLETED)
            .map(ProcessingStep::getStepNumber)
            .sorted()
            .collect(Collectors.toList());
        
        assertThat(stepOrder).containsExactly(1, 2, 3, 4, 5, 6, 7, 8);
    }
}
```

#### 3. End-to-End Tests (10% - Complete Workflow)

**Complete Report Processing E2E**
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
class MulticastReportE2ETest {
    
    @Container
    static MongoDBContainer mongodb = new MongoDBContainer("mongo:5.0");
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldProcessCompleteMulticastReportFromFileInputToFileOutput() throws Exception {
        // Given: Input file for processing
        MockMultipartFile inputFile = new MockMultipartFile(
            "file", 
            "input.txt", 
            "text/plain", 
            createValidInputFileContent().getBytes());
        
        ReportProcessingRequest request = ReportProcessingRequest.builder()
            .reportType("MULTICAST")
            .priority("NORMAL")
            .build();
        
        // When: Submit report through API
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", inputFile.getResource());
        body.add("request", request);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        ResponseEntity<ReportSubmissionResponse> response = restTemplate.postForEntity(
            "/api/v1/reports/process", requestEntity, ReportSubmissionResponse.class);
        
        // Then: Verify successful submission
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        
        String reportId = response.getBody().getReportId();
        
        // Wait for processing to complete
        await().atMost(60, TimeUnit.SECONDS)
            .until(() -> {
                ResponseEntity<ReportStatusResponse> status = restTemplate.getForEntity(
                    "/api/v1/reports/" + reportId + "/status", ReportStatusResponse.class);
                return status.getBody().getStatus().equals("COMPLETED");
            });
        
        // Verify final status
        ResponseEntity<ReportStatusResponse> finalStatus = restTemplate.getForEntity(
            "/api/v1/reports/" + reportId + "/status", ReportStatusResponse.class);
        
        assertThat(finalStatus.getBody().getStatus()).isEqualTo("COMPLETED");
        assertThat(finalStatus.getBody().getProgress()).isEqualTo(100.0);
        assertThat(finalStatus.getBody().getCompletedSteps()).hasSize(8);
        
        // Download and verify output file
        ResponseEntity<Resource> outputResponse = restTemplate.getForEntity(
            "/api/v1/reports/" + reportId + "/download", Resource.class);
        
        assertThat(outputResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(outputResponse.getBody()).isNotNull();
        
        // Verify output file content
        String outputContent = StreamUtils.copyToString(
            outputResponse.getBody().getInputStream(), StandardCharsets.UTF_8);
        assertThat(outputContent).isNotEmpty();
        assertThat(outputContent).contains("REPORT_COMPLETED");
    }
}
```

### COBOL Business Logic Validation Tests

#### Side-by-Side Validation
```java
/**
 * Tests specifically validating exact COBOL business logic preservation.
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CobolBusinessLogicValidationTest {
    
    @Test
    void shouldPreserveAllStmmcg05BusinessLogic() {
        // Test exact COBOL business logic from STMMCG05
        
        // 1. Product code mapping validation
        ProductCodeMapping dcpMapping = productCodeValidator.validateAndMap("DCP");
        assertThat(dcpMapping.getMappedCode()).isEqualTo("BNT");
        
        ProductCodeMapping ewtMapping = productCodeValidator.validateAndMap("EWT01");
        assertThat(ewtMapping.getMappedCode()).isEqualTo("EWT01");
        assertThat(ewtMapping.isValid()).isTrue();
        
        // 2. Status management validation
        ProcessedPayment payment = new ProcessedPayment();
        
        // Test 'C' status without debit date
        payment.setDebitDate(null);
        paymentStatusManager.updateStatus(payment, "C");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_BEFORE_DEBIT);
        
        // Test 'C' status with debit date
        payment.setDebitDate(LocalDateTime.now());
        paymentStatusManager.updateStatus(payment, "C");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_AFTER_DEBIT);
        
        // Test 'J' status
        paymentStatusManager.updateStatus(payment, "J");
        assertThat(payment.getStatus()).isEqualTo(PaymentStatus.CANCELLED_AFTER_DEBIT);
        
        // 3. Amount processing validation
        AmountProcessingResult result = amountProcessor.processAmount(new BigDecimal("1000.50"), "BNT");
        assertThat(result.getProcessedAmount()).isEqualTo(new BigDecimal("1000.50"));
        assertThat(result.isValid()).isTrue();
    }
    
    @Test
    void shouldPreserveRecordFormatProcessing() {
        // Test 285-character record processing (STMBDD08)
        String record285 = create285CharacterRecord();
        BillPaymentDetail detail = billPaymentProcessor.parsePaymentDetail(record285);
        assertThat(detail).isNotNull();
        assertThat(detail.getBillerId()).matches("CB\\d{8}");
        
        // Test 525-byte statement records (STMMCG01)
        StatementMergeResult mergeResult = statementMergeProcessor.mergeStatements(
            createStatementData(), createInterbankData());
        assertThat(mergeResult.getFormattedRecords())
            .allMatch(record -> record.length() == 525);
        
        // Test 3200-character reference records (STMMCREF)
        String record3200 = create3200CharacterRecord();
        ReferenceStatement refStatement = referenceProcessor.processInputRecord(record3200);
        assertThat(refStatement.getSections()).hasSize(4);
    }
}
```

### Performance Testing

#### Load and Stress Testing
```java
@Test
void shouldMeetPerformanceRequirements() {
    // Performance test: Process multiple reports concurrently
    int numberOfReports = 10;
    List<CompletableFuture<ReportResult>> futures = new ArrayList<>();
    
    long startTime = System.currentTimeMillis();
    
    for (int i = 0; i < numberOfReports; i++) {
        MulticastReport report = createValidReport();
        CompletableFuture<ReportResult> future = CompletableFuture.supplyAsync(() -> {
            try {
                reportOrchestrator.processReportAsync(report);
                // Wait for completion
                while (!report.isComplete()) {
                    Thread.sleep(100);
                }
                return ReportResult.success(report);
            } catch (Exception e) {
                return ReportResult.failed(e.getMessage());
            }
        });
        futures.add(future);
    }
    
    // Wait for all to complete
    List<ReportResult> results = futures.stream()
        .map(CompletableFuture::join)
        .collect(Collectors.toList());
    
    long endTime = System.currentTimeMillis();
    long totalTime = endTime - startTime;
    
    // Verify performance requirements
    assertThat(results).hasSize(numberOfReports);
    assertThat(results).allMatch(ReportResult::isSuccess);
    assertThat(totalTime).isLessThan(60000); // Should complete within 60 seconds
    
    // Verify average processing time per report
    double averageTime = (double) totalTime / numberOfReports;
    assertThat(averageTime).isLessThan(10000); // Less than 10 seconds per report
}
```

## Implementation Checklist

### Pre-Migration Setup
- [ ] **Environment Configuration**
  - [ ] Java 21 development environment setup
  - [ ] Spring Boot 3.x project created with virtual thread support
  - [ ] MongoDB NoSQL database configured
  - [ ] Maven build configuration with all dependencies
  - [ ] IDE configured with Spring Boot and Java 21 support
  - [ ] Git repository initialized with proper structure

- [ ] **COBOL Analysis Completion**
  - [ ] All 22 COBOL files analyzed and documented
  - [ ] 8-step workflow dependencies mapped
  - [ ] Business logic vs infrastructure separation completed
  - [ ] Critical business rules identified and prioritized
  - [ ] Input/output file formats documented

### Phase 1: Foundation (Weeks 1-4) ✅
- [ ] **Week 1: Infrastructure Setup**
  - [ ] Spring Boot project structure created (`com.scb.bizo`)
  - [ ] Virtual thread configuration implemented
  - [ ] MongoDB integration configured
  - [ ] Logging framework setup (SLF4J/Logback)
  - [ ] Basic configuration classes created
  - [ ] Package structure following requirements

- [ ] **Week 2: Domain Models**
  - [ ] `MulticastReport` aggregate root with 8-step workflow logic
  - [ ] `ProcessingStep` entity with step validation
  - [ ] `ReportData` value object for data handling
  - [ ] `StepResult` value object for step outcomes
  - [ ] `PaymentTransaction` entity for payment processing
  - [ ] `AccountProfile` entity for account management
  - [ ] All domain enums: `ReportStatus`, `StepStatus`, `PaymentStatus`

- [ ] **Week 3: Common Utilities**
  - [ ] Date processing utilities with COBOL format support
  - [ ] Amount processing utilities for decimal conversion
  - [ ] Record parsing utilities for fixed-length records
  - [ ] File service interfaces for input/output handling
  - [ ] Repository interfaces for data persistence
  - [ ] Event publisher interfaces for coordination

- [ ] **Week 4: Validation Framework**
  - [ ] Input validation framework
  - [ ] Business rule validation engine
  - [ ] Error handling and exception management
  - [ ] Validation result handling

### Phase 2: Step Framework (Weeks 5-6) ✅
- [ ] **Week 5: Step Processor Framework**
  - [ ] `StepProcessor` interface definition
  - [ ] Base step service implementation
  - [ ] Step validation framework
  - [ ] Step result handling and aggregation
  - [ ] Error handling and retry logic
  - [ ] Step dependency validation

- [ ] **Week 6: Orchestration Layer**
  - [ ] `ReportOrchestrator` for 8-step coordination
  - [ ] File service implementations
  - [ ] Status tracking service
  - [ ] Event publishing framework
  - [ ] Async processing with virtual threads

### Phase 3: Step Implementations (Weeks 7-14) 🔥 CRITICAL
- [ ] **Week 7-8: Step 1 (Foundation Step)**
  - [ ] `AccountSetupProcessor` (EBCMMC01)
  - [ ] `StatementProcessor` (EBCMMC02 + STMBDD06)
  - [ ] `InterbankProcessor` (EBCMMC03 + STMBDD07)
  - [ ] `StatementMergeProcessor` (EBCMMC04 + STMMCG01)
  - [ ] 525-byte record generation
  - [ ] Comprehensive unit tests

- [ ] **Week 9: Step 2 (Bill Payment)**
  - [ ] `BillPaymentProcessor` (EBCMMC05 + STMBDD08 + STMMCG02)
  - [ ] PromptPay BILLER-ID validation (********** pattern)
  - [ ] 285-character record processing
  - [ ] Statement-detail matching logic
  - [ ] Integration tests

- [ ] **Week 10: Steps 3-5 (Specialized Processing)**
  - [ ] `EppProcessor` (EBCMMC06 + STMMCG03) - 700-character records
  - [ ] `LccProcessor` (EBCMMC07 + STMMCG04) - 1300-character records
  - [ ] `RftProcessor` (EBCMMC71 + STMMCG06) - Real-time processing
  - [ ] Format validation and business rules
  - [ ] Performance optimization

- [ ] **Week 11-12: Step 6 (MOST CRITICAL - STMMCG05)**
  - [ ] `CnIpsPaymentProcessor` core implementation
  - [ ] Product code validation and mapping (DCP→BNT) ⚠️ CRITICAL
  - [ ] EWT pattern validation (EWT##) ⚠️ CRITICAL
  - [ ] Status management ('C', 'J' handling) ⚠️ CRITICAL
  - [ ] Amount processing from COBOL format ⚠️ CRITICAL
  - [ ] Payment validation business rules
  - [ ] Exception handling and audit logging
  - [ ] Comprehensive unit tests (>95% coverage) ⚠️ CRITICAL
  - [ ] COBOL behavior validation tests ⚠️ CRITICAL

- [ ] **Week 13: Step 7 (Statement Generation)**
  - [ ] `StatementGenerationProcessor` (EBCMMC09 + STMMCREF + STMMCG07)
  - [ ] 3200-character record processing
  - [ ] IM/ST name lookup integration
  - [ ] Multi-section record parsing
  - [ ] Reference data validation

- [ ] **Week 14: Step 8 (Final Output)**
  - [ ] `FinalOutputProcessor` (EBCMAFTB)
  - [ ] Output file generation
  - [ ] Report finalization logic
  - [ ] File format validation
  - [ ] Completion event publishing

### Phase 4: Integration and API (Weeks 15-18)
- [ ] **Week 15: Service Integration**
  - [ ] Complete 8-step workflow integration
  - [ ] Sequential processing validation
  - [ ] Error handling and recovery
  - [ ] Performance optimization

- [ ] **Week 16: API Layer**
  - [ ] `ReportController` REST endpoints
  - [ ] `ReportFacade` business orchestration
  - [ ] Request/response DTO handling
  - [ ] Input validation and error formatting
  - [ ] File upload/download functionality

- [ ] **Week 17: Testing and Validation**
  - [ ] End-to-end workflow testing
  - [ ] Business logic validation against COBOL
  - [ ] Performance testing under load
  - [ ] Integration testing all components
  - [ ] Error scenario testing

- [ ] **Week 18: Documentation and Finalization**
  - [ ] Complete API documentation
  - [ ] Business logic preservation documentation
  - [ ] Deployment guides and procedures
  - [ ] Performance benchmarks
  - [ ] Final quality assurance

### Critical Validation Checkpoints

#### After Each Step Implementation
- [ ] **Business Logic Validation**
  - [ ] COBOL behavior exactly preserved
  - [ ] All business scenarios tested
  - [ ] Error handling validated
  - [ ] Record formats comply with COBOL

#### After Phase 3 (Step Implementations)
- [ ] **Complete Workflow Validation**
  - [ ] All 8 steps process sequentially
  - [ ] Data flows correctly between steps
  - [ ] Error handling works at each step
  - [ ] Performance meets requirements

#### Final Quality Gate
- [ ] **System Validation**
  - [ ] Input file → Output file processing works
  - [ ] All 22 COBOL programs migrated
  - [ ] 8-step sequential workflow functional
  - [ ] Platform independence verified
  - [ ] NoSQL database integration working
  - [ ] Virtual thread performance optimized

### Success Metrics

#### Functional Metrics
- ✅ **100% COBOL Logic Preservation**: All business rules migrated
- ✅ **8-Step Workflow**: Sequential processing functional
- ✅ **File Processing**: Input → Output file handling
- ✅ **Record Format Compliance**: All COBOL record formats supported

#### Performance Metrics
- ✅ **Processing Speed**: Complete 8-step workflow in <10 seconds for standard report
- ✅ **Concurrent Processing**: Support for multiple reports simultaneously
- ✅ **Memory Efficiency**: Optimized memory usage with virtual threads
- ✅ **Scalability**: Linear performance scaling with load

#### Quality Metrics
- ✅ **Test Coverage**: >95% coverage for business logic components
- ✅ **Code Quality**: All code reviews passed, comprehensive documentation
- ✅ **Platform Independence**: Zero infrastructure dependencies in business logic
- ✅ **Maintainability**: Clean, readable code following Spring Boot conventions

This comprehensive migration handbook provides complete guidance for successfully migrating the 22-file COBOL multicast report system to a modern Java Spring Boot library while preserving 100% of the original business logic and ensuring the sequential 8-step workflow operates correctly from input file to output file.