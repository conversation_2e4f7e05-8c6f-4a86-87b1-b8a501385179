package com.scb.bizo.report.service.step.step3;

import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.ReportId;
import com.scb.bizo.report.service.domain.model.ReportStatus;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step3.model.EppRecord;
import com.scb.bizo.report.service.step.step3.model.EppStatement;
import com.scb.bizo.report.service.step.step3.model.ProcessedEppStatement;
import com.scb.bizo.report.service.step.step3.processor.EppDataMergeProcessor;
import com.scb.bizo.report.service.step.step3.processor.EppStatementProcessor;
import com.scb.bizo.report.service.step.step3.result.EppDataMergeResult;
import com.scb.bizo.report.service.step.step3.result.EppStatementResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for Step3Service.
 * 
 * Tests the complete Step 3 processing workflow including:
 * - EPP data merging (EBCMMC06)
 * - EPP statement processing (STMMCG03)
 * - EBPP channel filtering and validation
 * - Account and amount matching logic
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Step 3 Service Tests")
class Step3ServiceTest {
    
    @Mock
    private EppDataMergeProcessor eppDataMergeProcessor;
    
    @Mock
    private EppStatementProcessor eppStatementProcessor;
    
    @InjectMocks
    private Step3Service step3Service;
    
    private MulticastReport testReport;
    private ReportData testInputData;
    
    @BeforeEach
    void setUp() {
        testReport = MulticastReport.builder()
            .reportId(ReportId.generate())
            .status(ReportStatus.PROCESSING)
            .createdAt(LocalDateTime.now())
            .build();
        
        testInputData = ReportData.builder()
            .dataContent("test EPP data")
            .build();
    }
    
    @Test
    @DisplayName("Should return step number 3")
    void shouldReturnStepNumber3() {
        assertThat(step3Service.getStepNumber()).isEqualTo(3);
    }
    
    @Test
    @DisplayName("Should return correct step name")
    void shouldReturnCorrectStepName() {
        assertThat(step3Service.getStepName()).isEqualTo("EPP Processing");
    }
    
    @Test
    @DisplayName("Should return correct COBOL programs")
    void shouldReturnCorrectCobolPrograms() {
        List<String> expectedPrograms = List.of("EBCMMC06+STMMCG03");
        assertThat(step3Service.getCobolPrograms()).isEqualTo(expectedPrograms);
    }
    
    @Test
    @DisplayName("Should successfully process Step 3 with all sub-steps")
    void shouldSuccessfullyProcessStep3() {
        // Given
        List<EppRecord> mergedEppRecords = List.of(
            EppRecord.builder()
                .recordType("P")
                .accountNumber("**********")
                .dateField("240101")
                .paymentReference("REF123456789ABCD")
                .recordKey("P|**********|240101|REF123456789ABCD")
                .recordLength(185)
                .build()
        );
        
        List<ProcessedEppStatement> processedStatements = List.of(
            ProcessedEppStatement.builder()
                .originalStatement("EBPP statement data".repeat(20)) // 550 chars approx
                .hasEppDetails(true)
                .processed(true)
                .build()
        );
        
        List<EppStatement> eppDetails = List.of(
            EppStatement.builder()
                .accountNumber("**********")
                .processed(true)
                .eppProcessFlag(true)
                .build()
        );
        
        // Mock processor responses
        when(eppDataMergeProcessor.mergeEppData(any(ReportData.class)))
            .thenReturn(EppDataMergeResult.success(mergedEppRecords, 1));
        
        when(eppStatementProcessor.processEppStatements(eq(mergedEppRecords), any(ReportData.class)))
            .thenReturn(EppStatementResult.success(processedStatements, eppDetails, 1, 1, 0));
        
        // When
        StepResult result = step3Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 3 completed successfully");
        
        // Verify output data structure
        Map<String, Object> outputData = result.getOutputData();
        assertThat(outputData).containsKeys(
            "mergedEppData", 
            "processedStatements", 
            "eppDetails", 
            "processingMetadata",
            "recordCounts"
        );
        
        // Verify record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) outputData.get("recordCounts");
        assertThat(recordCounts).containsEntry("mergedEppRecords", 1);
        assertThat(recordCounts).containsEntry("processedStatements", 1);
        assertThat(recordCounts).containsEntry("eppDetailRecords", 1);
        assertThat(recordCounts).containsEntry("errorRecords", 0);
        
        // Verify metadata
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) outputData.get("processingMetadata");
        assertThat(metadata).containsEntry("stepNumber", 3);
        assertThat(metadata).containsEntry("eppProcessingEnabled", true);
        assertThat(metadata).containsEntry("businessLogicPreserved", true);
    }
    
    @Test
    @DisplayName("Should fail when EPP data merge fails")
    void shouldFailWhenEppDataMergeFails() {
        // Given
        when(eppDataMergeProcessor.mergeEppData(any(ReportData.class)))
            .thenReturn(EppDataMergeResult.failed("EPP data merge failed"));
        
        // When
        StepResult result = step3Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("EPP data merge failed");
    }
    
    @Test
    @DisplayName("Should fail when EPP statement processing fails")
    void shouldFailWhenEppStatementProcessingFails() {
        // Given
        List<EppRecord> mergedEppRecords = List.of(
            EppRecord.builder().accountNumber("**********").build()
        );
        
        when(eppDataMergeProcessor.mergeEppData(any(ReportData.class)))
            .thenReturn(EppDataMergeResult.success(mergedEppRecords, 1));
        
        when(eppStatementProcessor.processEppStatements(eq(mergedEppRecords), any(ReportData.class)))
            .thenReturn(EppStatementResult.failed("EPP statement processing failed"));
        
        // When
        StepResult result = step3Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("EPP statement processing failed");
    }
    
    @Test
    @DisplayName("Should handle exceptions gracefully")
    void shouldHandleExceptionsGracefully() {
        // Given
        when(eppDataMergeProcessor.mergeEppData(any(ReportData.class)))
            .thenThrow(new RuntimeException("Unexpected error"));
        
        // When
        StepResult result = step3Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Step 3 processing failed");
    }
    
    @Test
    @DisplayName("Should handle empty EPP data gracefully")
    void shouldHandleEmptyEppDataGracefully() {
        // Given
        List<EppRecord> emptyEppRecords = List.of();
        List<ProcessedEppStatement> emptyStatements = List.of();
        List<EppStatement> emptyDetails = List.of();
        
        when(eppDataMergeProcessor.mergeEppData(any(ReportData.class)))
            .thenReturn(EppDataMergeResult.success(emptyEppRecords, 0));
        
        when(eppStatementProcessor.processEppStatements(eq(emptyEppRecords), any(ReportData.class)))
            .thenReturn(EppStatementResult.success(emptyStatements, emptyDetails, 0, 0, 0));
        
        // When
        StepResult result = step3Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 3 completed successfully");
        
        // Verify empty record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("mergedEppRecords", 0);
        assertThat(recordCounts).containsEntry("processedStatements", 0);
        assertThat(recordCounts).containsEntry("eppDetailRecords", 0);
        assertThat(recordCounts).containsEntry("errorRecords", 0);
    }
}
