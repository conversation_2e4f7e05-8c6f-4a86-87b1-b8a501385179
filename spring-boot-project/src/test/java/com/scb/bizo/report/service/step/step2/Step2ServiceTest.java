package com.scb.bizo.report.service.step.step2;

import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.ReportId;
import com.scb.bizo.report.service.domain.model.ReportStatus;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step2.model.BillPaymentRecord;
import com.scb.bizo.report.service.step.step2.model.FilteredStatement;
import com.scb.bizo.report.service.step.step2.model.GeneratedStatement;
import com.scb.bizo.report.service.step.step2.processor.BillPaymentDataProcessor;
import com.scb.bizo.report.service.step.step2.processor.BillPaymentFilterProcessor;
import com.scb.bizo.report.service.step.step2.processor.BillPaymentStatementGenerator;
import com.scb.bizo.report.service.step.step2.result.BillPaymentDataResult;
import com.scb.bizo.report.service.step.step2.result.BillPaymentFilterResult;
import com.scb.bizo.report.service.step.step2.result.BillPaymentStatementResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for Step2Service.
 * 
 * Tests the complete Step 2 processing workflow including:
 * - Bill payment data processing (EBCMMC05)
 * - Statement filtering (STMBDD08)
 * - Statement generation (STMMCG02)
 * - PromptPay BILLER-ID support
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Step 2 Service Tests")
class Step2ServiceTest {
    
    @Mock
    private BillPaymentDataProcessor billPaymentDataProcessor;
    
    @Mock
    private BillPaymentFilterProcessor billPaymentFilterProcessor;
    
    @Mock
    private BillPaymentStatementGenerator billPaymentStatementGenerator;
    
    @InjectMocks
    private Step2Service step2Service;
    
    private MulticastReport testReport;
    private ReportData testInputData;
    
    @BeforeEach
    void setUp() {
        testReport = MulticastReport.builder()
            .reportId(ReportId.generate())
            .status(ReportStatus.PROCESSING)
            .createdAt(LocalDateTime.now())
            .build();
        
        testInputData = ReportData.builder()
            .dataContent("test bill payment data")
            .build();
    }
    
    @Test
    @DisplayName("Should return step number 2")
    void shouldReturnStepNumber2() {
        assertThat(step2Service.getStepNumber()).isEqualTo(2);
    }
    
    @Test
    @DisplayName("Should return correct step name")
    void shouldReturnCorrectStepName() {
        assertThat(step2Service.getStepName()).isEqualTo("Bill Payment Processing");
    }
    
    @Test
    @DisplayName("Should return correct COBOL programs")
    void shouldReturnCorrectCobolPrograms() {
        List<String> expectedPrograms = List.of("EBCMMC05+STMBDD08+STMMCG02");
        assertThat(step2Service.getCobolPrograms()).isEqualTo(expectedPrograms);
    }
    
    @Test
    @DisplayName("Should successfully process Step 2 with all sub-steps")
    void shouldSuccessfullyProcessStep2() {
        // Given
        List<BillPaymentRecord> billPaymentRecords = List.of(
            BillPaymentRecord.builder()
                .accountNumber("**********")
                .statementCode("BPAY01")
                .statementDate("240101")
                .build()
        );
        
        List<FilteredStatement> filteredStatements = List.of(
            FilteredStatement.builder()
                .accountNumber("**********")
                .sequenceNumber(1)
                .recordType("D")
                .build()
        );
        
        List<GeneratedStatement> generatedStatements = List.of(
            GeneratedStatement.builder()
                .accountNumber("**********")
                .sequenceNumber(1)
                .statementType("PROMPTPAY")
                .channel("BPAY")
                .balance(BigDecimal.valueOf(1000.00))
                .promptPayEnabled(true)
                .build()
        );
        
        // Mock processor responses
        when(billPaymentDataProcessor.processBillPaymentData(any(ReportData.class)))
            .thenReturn(BillPaymentDataResult.success(billPaymentRecords, 1));
        
        when(billPaymentFilterProcessor.filterStatements(eq(billPaymentRecords), any(ReportData.class)))
            .thenReturn(BillPaymentFilterResult.success(filteredStatements, 1));
        
        when(billPaymentStatementGenerator.generateStatements(eq(filteredStatements), eq(billPaymentRecords)))
            .thenReturn(BillPaymentStatementResult.success(generatedStatements, 1, 1));
        
        // When
        StepResult result = step2Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 2 completed successfully");
        
        // Verify output data structure
        Map<String, Object> outputData = result.getOutputData();
        assertThat(outputData).containsKeys(
            "billPaymentData", 
            "filteredStatements", 
            "generatedStatements", 
            "processingMetadata",
            "recordCounts"
        );
        
        // Verify record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) outputData.get("recordCounts");
        assertThat(recordCounts).containsEntry("billPaymentRecords", 1);
        assertThat(recordCounts).containsEntry("filteredStatements", 1);
        assertThat(recordCounts).containsEntry("generatedStatements", 1);
        assertThat(recordCounts).containsEntry("promptPayTransactions", 1);
        
        // Verify metadata
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) outputData.get("processingMetadata");
        assertThat(metadata).containsEntry("stepNumber", 2);
        assertThat(metadata).containsEntry("promptPaySupport", true);
        assertThat(metadata).containsEntry("businessLogicPreserved", true);
    }
    
    @Test
    @DisplayName("Should fail when bill payment data processing fails")
    void shouldFailWhenBillPaymentDataProcessingFails() {
        // Given
        when(billPaymentDataProcessor.processBillPaymentData(any(ReportData.class)))
            .thenReturn(BillPaymentDataResult.failed("Bill payment data processing failed"));
        
        // When
        StepResult result = step2Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Bill payment data processing failed");
    }
    
    @Test
    @DisplayName("Should fail when statement filtering fails")
    void shouldFailWhenStatementFilteringFails() {
        // Given
        List<BillPaymentRecord> billPaymentRecords = List.of(
            BillPaymentRecord.builder().accountNumber("**********").build()
        );
        
        when(billPaymentDataProcessor.processBillPaymentData(any(ReportData.class)))
            .thenReturn(BillPaymentDataResult.success(billPaymentRecords, 1));
        
        when(billPaymentFilterProcessor.filterStatements(eq(billPaymentRecords), any(ReportData.class)))
            .thenReturn(BillPaymentFilterResult.failed("Statement filtering failed"));
        
        // When
        StepResult result = step2Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Statement filtering failed");
    }
    
    @Test
    @DisplayName("Should fail when statement generation fails")
    void shouldFailWhenStatementGenerationFails() {
        // Given
        List<BillPaymentRecord> billPaymentRecords = List.of(
            BillPaymentRecord.builder().accountNumber("**********").build()
        );
        
        List<FilteredStatement> filteredStatements = List.of(
            FilteredStatement.builder().accountNumber("**********").build()
        );
        
        when(billPaymentDataProcessor.processBillPaymentData(any(ReportData.class)))
            .thenReturn(BillPaymentDataResult.success(billPaymentRecords, 1));
        
        when(billPaymentFilterProcessor.filterStatements(eq(billPaymentRecords), any(ReportData.class)))
            .thenReturn(BillPaymentFilterResult.success(filteredStatements, 1));
        
        when(billPaymentStatementGenerator.generateStatements(eq(filteredStatements), eq(billPaymentRecords)))
            .thenReturn(BillPaymentStatementResult.failed("Statement generation failed"));
        
        // When
        StepResult result = step2Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Statement generation failed");
    }
    
    @Test
    @DisplayName("Should handle exceptions gracefully")
    void shouldHandleExceptionsGracefully() {
        // Given
        when(billPaymentDataProcessor.processBillPaymentData(any(ReportData.class)))
            .thenThrow(new RuntimeException("Unexpected error"));
        
        // When
        StepResult result = step2Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Step 2 processing failed");
    }
}
