package com.scb.bizo.report.service.step.step1;

import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.ReportId;
import com.scb.bizo.report.service.domain.model.ReportStatus;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step1.model.AccountProfile;
import com.scb.bizo.report.service.step.step1.model.InterbankData;
import com.scb.bizo.report.service.step.step1.model.MergedStatementData;
import com.scb.bizo.report.service.step.step1.model.StatementRecord;
import com.scb.bizo.report.service.step.step1.processor.AccountSetupProcessor;
import com.scb.bizo.report.service.step.step1.processor.InterbankProcessor;
import com.scb.bizo.report.service.step.step1.processor.StatementMergeProcessor;
import com.scb.bizo.report.service.step.step1.processor.StatementProcessor;
import com.scb.bizo.report.service.step.step1.result.AccountSetupResult;
import com.scb.bizo.report.service.step.step1.result.InterbankProcessingResult;
import com.scb.bizo.report.service.step.step1.result.StatementMergeResult;
import com.scb.bizo.report.service.step.step1.result.StatementProcessingResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for Step1Service.
 * 
 * Tests the complete Step 1 processing workflow including:
 * - Account setup (EBCMMC01)
 * - Statement processing (EBCMMC02 + STMBDD06)
 * - Interbank processing (EBCMMC03 + STMBDD07)
 * - Statement merging (EBCMMC04 + STMMCG01)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Step 1 Service Tests")
class Step1ServiceTest {
    
    @Mock
    private AccountSetupProcessor accountSetupProcessor;
    
    @Mock
    private StatementProcessor statementProcessor;
    
    @Mock
    private InterbankProcessor interbankProcessor;
    
    @Mock
    private StatementMergeProcessor statementMergeProcessor;
    
    @InjectMocks
    private Step1Service step1Service;
    
    private MulticastReport testReport;
    private ReportData testInputData;
    
    @BeforeEach
    void setUp() {
        testReport = MulticastReport.builder()
            .reportId(ReportId.generate())
            .status(ReportStatus.SUBMITTED)
            .createdAt(LocalDateTime.now())
            .build();
        
        testInputData = ReportData.builder()
            .dataContent("test data content")
            .build();
    }
    
    @Test
    @DisplayName("Should return step number 1")
    void shouldReturnStepNumber1() {
        assertThat(step1Service.getStepNumber()).isEqualTo(1);
    }
    
    @Test
    @DisplayName("Should return correct step name")
    void shouldReturnCorrectStepName() {
        assertThat(step1Service.getStepName()).isEqualTo("Initial Processing");
    }
    
    @Test
    @DisplayName("Should return correct COBOL programs")
    void shouldReturnCorrectCobolPrograms() {
        List<String> expectedPrograms = List.of(
            "EBCMMC01", 
            "EBCMMC02+STMBDD06", 
            "EBCMMC03+STMBDD07", 
            "EBCMMC04+STMMCG01"
        );
        
        assertThat(step1Service.getCobolPrograms()).isEqualTo(expectedPrograms);
    }
    
    @Test
    @DisplayName("Should successfully process Step 1 with all sub-steps")
    void shouldSuccessfullyProcessStep1() {
        // Given
        List<AccountProfile> accountProfiles = List.of(
            AccountProfile.builder()
                .accountNumber("**********")
                .multicashEnabled(true)
                .build()
        );
        
        List<StatementRecord> statementRecords = List.of(
            StatementRecord.builder()
                .accountNumber("**********")
                .bankCode("14")
                .currencyCode("764")
                .build()
        );
        
        List<InterbankData> interbankData = List.of(
            InterbankData.builder()
                .accountNumber("**********")
                .interbankType("TRANSFER")
                .build()
        );
        
        List<MergedStatementData> mergedData = List.of(
            MergedStatementData.builder()
                .accountNumber("**********")
                .totalStatements(1)
                .totalInterbankRecords(1)
                .build()
        );
        
        // Mock processor responses
        when(accountSetupProcessor.processAccountSetup(any(ReportData.class)))
            .thenReturn(AccountSetupResult.success(accountProfiles, 1));
        
        when(statementProcessor.processStatements(any(ReportData.class), eq(accountProfiles)))
            .thenReturn(StatementProcessingResult.success(statementRecords, 1));
        
        when(interbankProcessor.processInterbank(eq(statementRecords)))
            .thenReturn(InterbankProcessingResult.success(interbankData, 1));
        
        when(statementMergeProcessor.mergeStatements(eq(statementRecords), eq(interbankData)))
            .thenReturn(StatementMergeResult.success(mergedData, 1));
        
        // When
        StepResult result = step1Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 1 completed successfully");
        
        // Verify output data structure
        Map<String, Object> outputData = result.getOutputData();
        assertThat(outputData).containsKeys(
            "accountData", 
            "statementData", 
            "interbankData", 
            "processingMetadata",
            "recordCounts"
        );
        
        // Verify record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) outputData.get("recordCounts");
        assertThat(recordCounts).containsEntry("accountRecords", 1);
        assertThat(recordCounts).containsEntry("statementRecords", 1);
        assertThat(recordCounts).containsEntry("interbankRecords", 1);
        assertThat(recordCounts).containsEntry("mergedRecords", 1);
    }
    
    @Test
    @DisplayName("Should fail when account setup fails")
    void shouldFailWhenAccountSetupFails() {
        // Given
        when(accountSetupProcessor.processAccountSetup(any(ReportData.class)))
            .thenReturn(AccountSetupResult.failed("Account setup failed"));
        
        // When
        StepResult result = step1Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Account setup failed");
    }
    
    @Test
    @DisplayName("Should fail when statement processing fails")
    void shouldFailWhenStatementProcessingFails() {
        // Given
        List<AccountProfile> accountProfiles = List.of(
            AccountProfile.builder().accountNumber("**********").build()
        );
        
        when(accountSetupProcessor.processAccountSetup(any(ReportData.class)))
            .thenReturn(AccountSetupResult.success(accountProfiles, 1));
        
        when(statementProcessor.processStatements(any(ReportData.class), eq(accountProfiles)))
            .thenReturn(StatementProcessingResult.failed("Statement processing failed"));
        
        // When
        StepResult result = step1Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Statement processing failed");
    }
    
    @Test
    @DisplayName("Should handle exceptions gracefully")
    void shouldHandleExceptionsGracefully() {
        // Given
        when(accountSetupProcessor.processAccountSetup(any(ReportData.class)))
            .thenThrow(new RuntimeException("Unexpected error"));
        
        // When
        StepResult result = step1Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Step 1 processing failed");
    }
}
