package com.scb.bizo.report.service.step.step6;

import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.ReportId;
import com.scb.bizo.report.service.domain.model.ReportStatus;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step6.model.CnIpsRecord;
import com.scb.bizo.report.service.step.step6.model.CnIpsPayment;
import com.scb.bizo.report.service.step.step6.model.ProcessedCnIpsPayment;
import com.scb.bizo.report.service.step.step6.processor.CnIpsDataMergeProcessor;
import com.scb.bizo.report.service.step.step6.processor.CnIpsPaymentProcessor;
import com.scb.bizo.report.service.step.step6.result.CnIpsDataMergeResult;
import com.scb.bizo.report.service.step.step6.result.CnIpsPaymentResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for Step6Service.
 * 
 * Tests the complete Step 6 processing workflow including:
 * - CN/IPS data merging (EBCMMC08)
 * - CN/IPS payment processing (STMMCG05) - CRITICAL
 * - Product code mapping and validation
 * - EWT pattern validation and processing
 * - Exception report generation
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Step 6 Service Tests - CRITICAL CN/IPS Core Processing")
class Step6ServiceTest {
    
    @Mock
    private CnIpsDataMergeProcessor cnIpsDataMergeProcessor;
    
    @Mock
    private CnIpsPaymentProcessor cnIpsPaymentProcessor;
    
    @InjectMocks
    private Step6Service step6Service;
    
    private MulticastReport testReport;
    private ReportData testInputData;
    
    @BeforeEach
    void setUp() {
        testReport = MulticastReport.builder()
            .reportId(ReportId.generate())
            .status(ReportStatus.PROCESSING)
            .createdAt(LocalDateTime.now())
            .build();
        
        testInputData = ReportData.builder()
            .dataContent("test CN/IPS data")
            .build();
    }
    
    @Test
    @DisplayName("Should return step number 6")
    void shouldReturnStepNumber6() {
        assertThat(step6Service.getStepNumber()).isEqualTo(6);
    }
    
    @Test
    @DisplayName("Should return correct step name")
    void shouldReturnCorrectStepName() {
        assertThat(step6Service.getStepName()).isEqualTo("CN/IPS Core Processing");
    }
    
    @Test
    @DisplayName("Should return correct COBOL programs")
    void shouldReturnCorrectCobolPrograms() {
        List<String> expectedPrograms = List.of("EBCMMC08+STMMCG05");
        assertThat(step6Service.getCobolPrograms()).isEqualTo(expectedPrograms);
    }
    
    @Test
    @DisplayName("Should successfully process Step 6 with all sub-steps - CRITICAL")
    void shouldSuccessfullyProcessStep6() {
        // Given
        List<CnIpsRecord> mergedCnIpsRecords = List.of(
            CnIpsRecord.builder()
                .recordType("STATEMENT")
                .statementKey("**********123456")
                .accountNumber("**********")
                .recordLength(2100)
                .build(),
            CnIpsRecord.builder()
                .recordType("DEBIT")
                .accountNumber("**********")
                .customerReference("CUST12345678")
                .productCode("DCP")
                .ewtFlag("   ")
                .recordLength(950)
                .build()
        );
        
        List<ProcessedCnIpsPayment> processedPayments = List.of(
            ProcessedCnIpsPayment.builder()
                .originalStatement("CN/IPS statement data".repeat(84)) // 2100 chars approx
                .hasCnIpsDetails(true)
                .processed(true)
                .productCode("BNT") // After DCP → BNT mapping
                .ewtTransaction(false)
                .build()
        );
        
        List<CnIpsPayment> cnIpsDetails = List.of(
            CnIpsPayment.builder()
                .accountNumber("**********")
                .productCode("BNT")
                .processed(true)
                .ewtEnabled(false)
                .build()
        );
        
        List<String> exceptionReports = List.of();
        
        // Mock processor responses
        when(cnIpsDataMergeProcessor.mergeCnIpsData(any(ReportData.class)))
            .thenReturn(CnIpsDataMergeResult.success(mergedCnIpsRecords, 2));
        
        when(cnIpsPaymentProcessor.processCnIpsPayments(eq(mergedCnIpsRecords), any(ReportData.class)))
            .thenReturn(CnIpsPaymentResult.success(processedPayments, cnIpsDetails, exceptionReports, 1, 1, 0, 0, 1, 0));
        
        // When
        StepResult result = step6Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 6 completed successfully");
        
        // Verify output data structure
        Map<String, Object> outputData = result.getOutputData();
        assertThat(outputData).containsKeys(
            "mergedCnIpsData", 
            "processedPayments", 
            "cnIpsDetails", 
            "exceptionReports",
            "processingMetadata",
            "recordCounts"
        );
        
        // Verify record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) outputData.get("recordCounts");
        assertThat(recordCounts).containsEntry("mergedCnIpsRecords", 2);
        assertThat(recordCounts).containsEntry("processedPayments", 1);
        assertThat(recordCounts).containsEntry("cnIpsDetailRecords", 1);
        assertThat(recordCounts).containsEntry("exceptionRecords", 0);
        assertThat(recordCounts).containsEntry("ewtTransactions", 0);
        assertThat(recordCounts).containsEntry("productCodeMappings", 1);
        assertThat(recordCounts).containsEntry("errorRecords", 0);
        
        // Verify metadata
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) outputData.get("processingMetadata");
        assertThat(metadata).containsEntry("stepNumber", 6);
        assertThat(metadata).containsEntry("cnIpsProcessingEnabled", true);
        assertThat(metadata).containsEntry("criticalStep", true);
        assertThat(metadata).containsEntry("businessLogicPreserved", true);
    }
    
    @Test
    @DisplayName("Should fail when CN/IPS data merge fails")
    void shouldFailWhenCnIpsDataMergeFails() {
        // Given
        when(cnIpsDataMergeProcessor.mergeCnIpsData(any(ReportData.class)))
            .thenReturn(CnIpsDataMergeResult.failed("CN/IPS data merge failed"));
        
        // When
        StepResult result = step6Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("CN/IPS data merge failed");
    }
    
    @Test
    @DisplayName("Should fail when CN/IPS payment processing fails - CRITICAL")
    void shouldFailWhenCnIpsPaymentProcessingFails() {
        // Given
        List<CnIpsRecord> mergedCnIpsRecords = List.of(
            CnIpsRecord.builder().accountNumber("**********").build()
        );
        
        when(cnIpsDataMergeProcessor.mergeCnIpsData(any(ReportData.class)))
            .thenReturn(CnIpsDataMergeResult.success(mergedCnIpsRecords, 1));
        
        when(cnIpsPaymentProcessor.processCnIpsPayments(eq(mergedCnIpsRecords), any(ReportData.class)))
            .thenReturn(CnIpsPaymentResult.failed("CN/IPS payment processing failed"));
        
        // When
        StepResult result = step6Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("CN/IPS payment processing failed");
    }
    
    @Test
    @DisplayName("Should handle exceptions gracefully")
    void shouldHandleExceptionsGracefully() {
        // Given
        when(cnIpsDataMergeProcessor.mergeCnIpsData(any(ReportData.class)))
            .thenThrow(new RuntimeException("Unexpected error"));
        
        // When
        StepResult result = step6Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Step 6 processing failed");
    }
    
    @Test
    @DisplayName("Should handle empty CN/IPS data gracefully")
    void shouldHandleEmptyCnIpsDataGracefully() {
        // Given
        List<CnIpsRecord> emptyCnIpsRecords = List.of();
        List<ProcessedCnIpsPayment> emptyPayments = List.of();
        List<CnIpsPayment> emptyDetails = List.of();
        List<String> emptyExceptions = List.of();
        
        when(cnIpsDataMergeProcessor.mergeCnIpsData(any(ReportData.class)))
            .thenReturn(CnIpsDataMergeResult.success(emptyCnIpsRecords, 0));
        
        when(cnIpsPaymentProcessor.processCnIpsPayments(eq(emptyCnIpsRecords), any(ReportData.class)))
            .thenReturn(CnIpsPaymentResult.success(emptyPayments, emptyDetails, emptyExceptions, 0, 0, 0, 0, 0, 0));
        
        // When
        StepResult result = step6Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 6 completed successfully");
        
        // Verify empty record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("mergedCnIpsRecords", 0);
        assertThat(recordCounts).containsEntry("processedPayments", 0);
        assertThat(recordCounts).containsEntry("cnIpsDetailRecords", 0);
        assertThat(recordCounts).containsEntry("exceptionRecords", 0);
        assertThat(recordCounts).containsEntry("ewtTransactions", 0);
        assertThat(recordCounts).containsEntry("productCodeMappings", 0);
        assertThat(recordCounts).containsEntry("errorRecords", 0);
    }
    
    @Test
    @DisplayName("Should handle CN/IPS processing with EWT transactions")
    void shouldHandleCnIpsProcessingWithEwtTransactions() {
        // Given
        List<CnIpsRecord> mergedCnIpsRecords = List.of(
            CnIpsRecord.builder()
                .recordType("DEBIT")
                .accountNumber("**********")
                .productCode("EWT")
                .ewtFlag("EWT")
                .build()
        );
        
        List<ProcessedCnIpsPayment> processedPayments = List.of(
            ProcessedCnIpsPayment.builder()
                .ewtTransaction(true)
                .processed(true)
                .build()
        );
        
        List<CnIpsPayment> cnIpsDetails = List.of(
            CnIpsPayment.builder()
                .ewtEnabled(true)
                .processed(true)
                .build()
        );
        
        when(cnIpsDataMergeProcessor.mergeCnIpsData(any(ReportData.class)))
            .thenReturn(CnIpsDataMergeResult.success(mergedCnIpsRecords, 1));
        
        when(cnIpsPaymentProcessor.processCnIpsPayments(eq(mergedCnIpsRecords), any(ReportData.class)))
            .thenReturn(CnIpsPaymentResult.success(processedPayments, cnIpsDetails, List.of(), 1, 1, 0, 1, 0, 0));
        
        // When
        StepResult result = step6Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("ewtTransactions", 1);
    }
    
    @Test
    @DisplayName("Should handle CN/IPS processing with exception reports")
    void shouldHandleCnIpsProcessingWithExceptionReports() {
        // Given
        List<CnIpsRecord> mergedCnIpsRecords = List.of(
            CnIpsRecord.builder()
                .recordType("STATEMENT")
                .accountNumber("**********")
                .build()
        );
        
        List<String> exceptionReports = List.of(
            "Exception Report: Unmatched CN/IPS transaction"
        );
        
        when(cnIpsDataMergeProcessor.mergeCnIpsData(any(ReportData.class)))
            .thenReturn(CnIpsDataMergeResult.success(mergedCnIpsRecords, 1));
        
        when(cnIpsPaymentProcessor.processCnIpsPayments(eq(mergedCnIpsRecords), any(ReportData.class)))
            .thenReturn(CnIpsPaymentResult.success(List.of(), List.of(), exceptionReports, 1, 0, 1, 0, 0, 0));
        
        // When
        StepResult result = step6Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("exceptionRecords", 1);
        
        @SuppressWarnings("unchecked")
        List<String> exceptions = (List<String>) result.getOutputData().get("exceptionReports");
        assertThat(exceptions).hasSize(1);
        assertThat(exceptions.get(0)).contains("Unmatched CN/IPS transaction");
    }
}
