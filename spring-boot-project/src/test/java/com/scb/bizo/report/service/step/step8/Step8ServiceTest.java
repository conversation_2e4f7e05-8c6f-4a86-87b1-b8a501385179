package com.scb.bizo.report.service.step.step8;

import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.ReportId;
import com.scb.bizo.report.service.domain.model.ReportStatus;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step8.model.FinalOutputData;
import com.scb.bizo.report.service.step.step8.model.GeneratedFile;
import com.scb.bizo.report.service.step.step8.model.FinalizationData;
import com.scb.bizo.report.service.step.step8.processor.FinalOutputProcessor;
import com.scb.bizo.report.service.step.step8.processor.OutputFileGenerator;
import com.scb.bizo.report.service.step.step8.processor.ReportFinalizer;
import com.scb.bizo.report.service.step.step8.result.FinalOutputResult;
import com.scb.bizo.report.service.step.step8.result.OutputFileResult;
import com.scb.bizo.report.service.step.step8.result.ReportFinalizationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for Step8Service.
 * 
 * Tests the complete Step 8 processing workflow including:
 * - Final output processing (EBCMAFTB)
 * - Output file generation
 * - Report finalization
 * - File transfer operations
 * - Completion event publishing
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Step 8 Service Tests - Final Output Generation")
class Step8ServiceTest {
    
    @Mock
    private FinalOutputProcessor finalOutputProcessor;
    
    @Mock
    private OutputFileGenerator outputFileGenerator;
    
    @Mock
    private ReportFinalizer reportFinalizer;
    
    @InjectMocks
    private Step8Service step8Service;
    
    private MulticastReport testReport;
    private ReportData testInputData;
    
    @BeforeEach
    void setUp() {
        testReport = MulticastReport.builder()
            .reportId(ReportId.generate())
            .status(ReportStatus.PROCESSING)
            .createdAt(LocalDateTime.now())
            .build();
        
        testInputData = ReportData.builder()
            .dataContent("test final output data")
            .build();
    }
    
    @Test
    @DisplayName("Should return step number 8")
    void shouldReturnStepNumber8() {
        assertThat(step8Service.getStepNumber()).isEqualTo(8);
    }
    
    @Test
    @DisplayName("Should return correct step name")
    void shouldReturnCorrectStepName() {
        assertThat(step8Service.getStepName()).isEqualTo("Final Output Generation");
    }
    
    @Test
    @DisplayName("Should return correct COBOL programs")
    void shouldReturnCorrectCobolPrograms() {
        List<String> expectedPrograms = List.of("EBCMAFTB");
        assertThat(step8Service.getCobolPrograms()).isEqualTo(expectedPrograms);
    }
    
    @Test
    @DisplayName("Should successfully process Step 8 with all sub-steps")
    void shouldSuccessfullyProcessStep8() {
        // Given
        FinalOutputData finalOutputData = FinalOutputData.builder()
            .reportId(testReport.getReportId().toString())
            .finalStatements(List.of("final statement data".repeat(100))) // 2500 chars approx
            .totalRecords(1)
            .build();
        
        List<GeneratedFile> generatedFiles = List.of(
            GeneratedFile.builder()
                .fileName("ERP_INTERBANK_EPPLCCBP_20240115_DAILY.txt")
                .filePath("/SERVERDATA/BCM/ERP_INTERBANK_EPPLCCBP_20240115_DAILY.txt")
                .fileSize(2500)
                .recordCount(1)
                .fileType("DAILY")
                .build(),
            GeneratedFile.builder()
                .fileName("ERP_INTERBANK_EPPLCCBP.txt")
                .filePath("/xi_spool/PRD/GENERAL/STM/SCB_FTP_FILE/ERP_INTERBANK_EPPLCCBP.txt")
                .fileSize(2500)
                .recordCount(1)
                .fileType("FTP")
                .build()
        );
        
        FinalizationData finalizationData = FinalizationData.builder()
            .reportId(testReport.getReportId().toString())
            .generatedFiles(generatedFiles)
            .status("COMPLETED")
            .build();
        
        // Mock processor responses
        when(finalOutputProcessor.processFinalOutput(any(MulticastReport.class), any(ReportData.class)))
            .thenReturn(FinalOutputResult.success(finalOutputData, 1));
        
        when(outputFileGenerator.generateOutputFiles(any(MulticastReport.class), eq(finalOutputData)))
            .thenReturn(OutputFileResult.success(generatedFiles, 
                "ERP_INTERBANK_EPPLCCBP_20240115_DAILY.txt",
                "ERP_INTERBANK_EPPLCCBP.txt",
                "/SERVERDATA/BCM/ERP_INTERBANK_EPPLCCBP_20240115_DAILY.txt",
                "/xi_spool/PRD/GENERAL/STM/SCB_FTP_FILE/ERP_INTERBANK_EPPLCCBP.txt",
                2500, 2500, 2));
        
        when(reportFinalizer.finalizeReport(any(MulticastReport.class), eq(generatedFiles)))
            .thenReturn(ReportFinalizationResult.success(finalizationData));
        
        // When
        StepResult result = step8Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 8 completed successfully - Final output generated");
        
        // Verify output data structure
        Map<String, Object> outputData = result.getOutputData();
        assertThat(outputData).containsKeys(
            "finalOutputData", 
            "generatedFiles", 
            "finalizationData", 
            "processingMetadata",
            "recordCounts",
            "outputFileInfo"
        );
        
        // Verify record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) outputData.get("recordCounts");
        assertThat(recordCounts).containsEntry("totalRecords", 1);
        assertThat(recordCounts).containsEntry("outputFiles", 2);
        assertThat(recordCounts).containsEntry("dailyFileSize", 2500L);
        assertThat(recordCounts).containsEntry("ftpFileSize", 2500L);
        
        // Verify output file info
        @SuppressWarnings("unchecked")
        Map<String, Object> outputFileInfo = (Map<String, Object>) outputData.get("outputFileInfo");
        assertThat(outputFileInfo).containsEntry("dailyFileName", "ERP_INTERBANK_EPPLCCBP_20240115_DAILY.txt");
        assertThat(outputFileInfo).containsEntry("ftpFileName", "ERP_INTERBANK_EPPLCCBP.txt");
        
        // Verify metadata
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) outputData.get("processingMetadata");
        assertThat(metadata).containsEntry("stepNumber", 8);
        assertThat(metadata).containsEntry("finalOutputEnabled", true);
        assertThat(metadata).containsEntry("businessLogicPreserved", true);
    }
    
    @Test
    @DisplayName("Should fail when final output processing fails")
    void shouldFailWhenFinalOutputProcessingFails() {
        // Given
        when(finalOutputProcessor.processFinalOutput(any(MulticastReport.class), any(ReportData.class)))
            .thenReturn(FinalOutputResult.failed("Final output processing failed"));
        
        // When
        StepResult result = step8Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Final output processing failed");
    }
    
    @Test
    @DisplayName("Should fail when output file generation fails")
    void shouldFailWhenOutputFileGenerationFails() {
        // Given
        FinalOutputData finalOutputData = FinalOutputData.builder()
            .reportId(testReport.getReportId().toString())
            .totalRecords(1)
            .build();
        
        when(finalOutputProcessor.processFinalOutput(any(MulticastReport.class), any(ReportData.class)))
            .thenReturn(FinalOutputResult.success(finalOutputData, 1));
        
        when(outputFileGenerator.generateOutputFiles(any(MulticastReport.class), eq(finalOutputData)))
            .thenReturn(OutputFileResult.failed("Output file generation failed"));
        
        // When
        StepResult result = step8Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Output file generation failed");
    }
    
    @Test
    @DisplayName("Should fail when report finalization fails")
    void shouldFailWhenReportFinalizationFails() {
        // Given
        FinalOutputData finalOutputData = FinalOutputData.builder()
            .reportId(testReport.getReportId().toString())
            .totalRecords(1)
            .build();
        
        List<GeneratedFile> generatedFiles = List.of(
            GeneratedFile.builder().fileName("test.txt").build()
        );
        
        when(finalOutputProcessor.processFinalOutput(any(MulticastReport.class), any(ReportData.class)))
            .thenReturn(FinalOutputResult.success(finalOutputData, 1));
        
        when(outputFileGenerator.generateOutputFiles(any(MulticastReport.class), eq(finalOutputData)))
            .thenReturn(OutputFileResult.success(generatedFiles, "daily.txt", "ftp.txt", 
                "/path/daily.txt", "/path/ftp.txt", 1000, 1000, 1));
        
        when(reportFinalizer.finalizeReport(any(MulticastReport.class), eq(generatedFiles)))
            .thenReturn(ReportFinalizationResult.failed("Report finalization failed"));
        
        // When
        StepResult result = step8Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Report finalization failed");
    }
    
    @Test
    @DisplayName("Should handle exceptions gracefully")
    void shouldHandleExceptionsGracefully() {
        // Given
        when(finalOutputProcessor.processFinalOutput(any(MulticastReport.class), any(ReportData.class)))
            .thenThrow(new RuntimeException("Unexpected error"));
        
        // When
        StepResult result = step8Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Step 8 processing failed");
    }
    
    @Test
    @DisplayName("Should handle empty final output data gracefully")
    void shouldHandleEmptyFinalOutputDataGracefully() {
        // Given
        FinalOutputData emptyOutputData = FinalOutputData.empty();
        List<GeneratedFile> emptyFiles = List.of();
        FinalizationData emptyFinalization = FinalizationData.builder()
            .reportId(testReport.getReportId().toString())
            .status("COMPLETED")
            .build();
        
        when(finalOutputProcessor.processFinalOutput(any(MulticastReport.class), any(ReportData.class)))
            .thenReturn(FinalOutputResult.success(emptyOutputData, 0));
        
        when(outputFileGenerator.generateOutputFiles(any(MulticastReport.class), eq(emptyOutputData)))
            .thenReturn(OutputFileResult.success(emptyFiles, "", "", "", "", 0, 0, 0));
        
        when(reportFinalizer.finalizeReport(any(MulticastReport.class), eq(emptyFiles)))
            .thenReturn(ReportFinalizationResult.success(emptyFinalization));
        
        // When
        StepResult result = step8Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 8 completed successfully - Final output generated");
        
        // Verify empty record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("totalRecords", 0);
        assertThat(recordCounts).containsEntry("outputFiles", 0);
    }
}
