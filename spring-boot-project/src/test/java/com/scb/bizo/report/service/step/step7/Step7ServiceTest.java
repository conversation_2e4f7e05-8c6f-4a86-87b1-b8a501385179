package com.scb.bizo.report.service.step.step7;

import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.ReportId;
import com.scb.bizo.report.service.domain.model.ReportStatus;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step7.model.StatementRecord;
import com.scb.bizo.report.service.step.step7.model.OutwardDetailRecord;
import com.scb.bizo.report.service.step.step7.model.ProcessedStatement;
import com.scb.bizo.report.service.step.step7.model.FinalStatement;
import com.scb.bizo.report.service.step.step7.processor.StatementDataMergeProcessor;
import com.scb.bizo.report.service.step.step7.processor.StatementReferenceProcessor;
import com.scb.bizo.report.service.step.step7.processor.OutwardDetailProcessor;
import com.scb.bizo.report.service.step.step7.result.StatementDataMergeResult;
import com.scb.bizo.report.service.step.step7.result.StatementReferenceResult;
import com.scb.bizo.report.service.step.step7.result.OutwardDetailResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for Step7Service.
 * 
 * Tests the complete Step 7 processing workflow including:
 * - Statement data merging (EBCMMC09)
 * - Statement reference processing (STMMCREF)
 * - Outward detail processing (STMMCG07)
 * - IM/ST branch name lookup
 * - EWT flag processing
 * - Status mapping and date conversion
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Step 7 Service Tests - Statement Generation")
class Step7ServiceTest {
    
    @Mock
    private StatementDataMergeProcessor statementDataMergeProcessor;
    
    @Mock
    private StatementReferenceProcessor statementReferenceProcessor;
    
    @Mock
    private OutwardDetailProcessor outwardDetailProcessor;
    
    @InjectMocks
    private Step7Service step7Service;
    
    private MulticastReport testReport;
    private ReportData testInputData;
    
    @BeforeEach
    void setUp() {
        testReport = MulticastReport.builder()
            .reportId(ReportId.generate())
            .status(ReportStatus.PROCESSING)
            .createdAt(LocalDateTime.now())
            .build();
        
        testInputData = ReportData.builder()
            .dataContent("test statement generation data")
            .build();
    }
    
    @Test
    @DisplayName("Should return step number 7")
    void shouldReturnStepNumber7() {
        assertThat(step7Service.getStepNumber()).isEqualTo(7);
    }
    
    @Test
    @DisplayName("Should return correct step name")
    void shouldReturnCorrectStepName() {
        assertThat(step7Service.getStepName()).isEqualTo("Statement Generation");
    }
    
    @Test
    @DisplayName("Should return correct COBOL programs")
    void shouldReturnCorrectCobolPrograms() {
        List<String> expectedPrograms = List.of("EBCMMC09+STMMCREF+STMMCG07");
        assertThat(step7Service.getCobolPrograms()).isEqualTo(expectedPrograms);
    }
    
    @Test
    @DisplayName("Should successfully process Step 7 with all sub-steps")
    void shouldSuccessfullyProcessStep7() {
        // Given
        List<StatementRecord> mergedStatements = List.of(
            StatementRecord.builder()
                .rawRecord("statement data".repeat(160)) // 3200 chars approx
                .outputRecord("statement output".repeat(157)) // 2516 chars approx
                .statementKey("****************")
                .accountNumber("**********")
                .sequenceNumber("000001")
                .recordLength(2516)
                .build()
        );
        
        List<OutwardDetailRecord> outwardDetails = List.of(
            OutwardDetailRecord.builder()
                .rawRecord("outward data".repeat(86)) // 1038 chars approx
                .outputRecord("outward output".repeat(78)) // 1100 chars approx
                .referenceKey("REF**********123")
                .recordLength(1100)
                .build()
        );
        
        List<ProcessedStatement> processedStatements = List.of(
            ProcessedStatement.builder()
                .originalRecord("statement output".repeat(157)) // 2516 chars approx
                .outputRecord("processed output".repeat(156)) // 2500 chars approx
                .statementKey("****************")
                .accountNumber("**********")
                .bankId("014")
                .branchNumber("0001")
                .branchName("Main Branch")
                .accountType("IM")
                .balance(BigDecimal.valueOf(10000.00))
                .amount(BigDecimal.valueOf(500.00))
                .dcCode("C ")
                .ewtTransaction(false)
                .adjustmentRecord(false)
                .processed(true)
                .build()
        );
        
        List<FinalStatement> finalStatements = List.of(
            FinalStatement.builder()
                .originalStatement("processed output".repeat(156)) // 2500 chars approx
                .outputRecord("final output".repeat(156)) // 2500 chars approx
                .statementKey("****************")
                .accountNumber("**********")
                .hasOutwardDetails(true)
                .processed(true)
                .status("I")
                .currency("THB")
                .amount("500.00")
                .date("2024-01-15")
                .build()
        );
        
        List<String> reportFiles = List.of(
            "****************REF**********123THB500.00     2024-01-15I "
        );
        
        // Mock processor responses
        when(statementDataMergeProcessor.mergeStatementData(any(ReportData.class)))
            .thenReturn(StatementDataMergeResult.success(mergedStatements, outwardDetails, 1, 1));
        
        when(statementReferenceProcessor.processStatementReference(eq(mergedStatements), any(ReportData.class)))
            .thenReturn(StatementReferenceResult.success(processedStatements, 1, 0, 0));
        
        when(outwardDetailProcessor.processOutwardDetail(eq(processedStatements), eq(outwardDetails), any(ReportData.class)))
            .thenReturn(OutwardDetailResult.success(finalStatements, reportFiles, 1, 1, 0, 0));
        
        // When
        StepResult result = step7Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 7 completed successfully");
        
        // Verify output data structure
        Map<String, Object> outputData = result.getOutputData();
        assertThat(outputData).containsKeys(
            "mergedStatementData", 
            "outwardDetailData", 
            "processedStatements", 
            "finalStatements",
            "reportFiles",
            "processingMetadata",
            "recordCounts"
        );
        
        // Verify record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) outputData.get("recordCounts");
        assertThat(recordCounts).containsEntry("mergedStatements", 1);
        assertThat(recordCounts).containsEntry("outwardDetails", 1);
        assertThat(recordCounts).containsEntry("processedStatements", 1);
        assertThat(recordCounts).containsEntry("finalStatements", 1);
        assertThat(recordCounts).containsEntry("reportRecords", 1);
        assertThat(recordCounts).containsEntry("adjustmentRecords", 0);
        assertThat(recordCounts).containsEntry("errorRecords", 0);
        
        // Verify metadata
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) outputData.get("processingMetadata");
        assertThat(metadata).containsEntry("stepNumber", 7);
        assertThat(metadata).containsEntry("statementGenerationEnabled", true);
        assertThat(metadata).containsEntry("businessLogicPreserved", true);
    }
    
    @Test
    @DisplayName("Should fail when statement data merge fails")
    void shouldFailWhenStatementDataMergeFails() {
        // Given
        when(statementDataMergeProcessor.mergeStatementData(any(ReportData.class)))
            .thenReturn(StatementDataMergeResult.failed("Statement data merge failed"));
        
        // When
        StepResult result = step7Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Statement data merge failed");
    }
    
    @Test
    @DisplayName("Should fail when statement reference processing fails")
    void shouldFailWhenStatementReferenceProcessingFails() {
        // Given
        List<StatementRecord> mergedStatements = List.of(
            StatementRecord.builder().accountNumber("**********").build()
        );
        
        when(statementDataMergeProcessor.mergeStatementData(any(ReportData.class)))
            .thenReturn(StatementDataMergeResult.success(mergedStatements, List.of(), 1, 0));
        
        when(statementReferenceProcessor.processStatementReference(eq(mergedStatements), any(ReportData.class)))
            .thenReturn(StatementReferenceResult.failed("Statement reference processing failed"));
        
        // When
        StepResult result = step7Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Statement reference processing failed");
    }
    
    @Test
    @DisplayName("Should fail when outward detail processing fails")
    void shouldFailWhenOutwardDetailProcessingFails() {
        // Given
        List<StatementRecord> mergedStatements = List.of(
            StatementRecord.builder().accountNumber("**********").build()
        );
        
        List<ProcessedStatement> processedStatements = List.of(
            ProcessedStatement.builder().accountNumber("**********").build()
        );
        
        when(statementDataMergeProcessor.mergeStatementData(any(ReportData.class)))
            .thenReturn(StatementDataMergeResult.success(mergedStatements, List.of(), 1, 0));
        
        when(statementReferenceProcessor.processStatementReference(eq(mergedStatements), any(ReportData.class)))
            .thenReturn(StatementReferenceResult.success(processedStatements, 1, 0, 0));
        
        when(outwardDetailProcessor.processOutwardDetail(eq(processedStatements), eq(List.of()), any(ReportData.class)))
            .thenReturn(OutwardDetailResult.failed("Outward detail processing failed"));
        
        // When
        StepResult result = step7Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Outward detail processing failed");
    }
    
    @Test
    @DisplayName("Should handle exceptions gracefully")
    void shouldHandleExceptionsGracefully() {
        // Given
        when(statementDataMergeProcessor.mergeStatementData(any(ReportData.class)))
            .thenThrow(new RuntimeException("Unexpected error"));
        
        // When
        StepResult result = step7Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Step 7 processing failed");
    }
    
    @Test
    @DisplayName("Should handle EWT transactions in statement reference processing")
    void shouldHandleEwtTransactionsInStatementReferenceProcessing() {
        // Given
        List<StatementRecord> mergedStatements = List.of(
            StatementRecord.builder()
                .accountNumber("**********")
                .build()
        );
        
        List<ProcessedStatement> processedStatements = List.of(
            ProcessedStatement.builder()
                .accountNumber("**********")
                .ewtTransaction(true)
                .build()
        );
        
        when(statementDataMergeProcessor.mergeStatementData(any(ReportData.class)))
            .thenReturn(StatementDataMergeResult.success(mergedStatements, List.of(), 1, 0));
        
        when(statementReferenceProcessor.processStatementReference(eq(mergedStatements), any(ReportData.class)))
            .thenReturn(StatementReferenceResult.success(processedStatements, 1, 1, 0));
        
        when(outwardDetailProcessor.processOutwardDetail(eq(processedStatements), eq(List.of()), any(ReportData.class)))
            .thenReturn(OutwardDetailResult.success(List.of(), List.of(), 0, 0, 0, 0));
        
        // When
        StepResult result = step7Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("processedStatements", 1);
    }
    
    @Test
    @DisplayName("Should handle adjustment records in outward detail processing")
    void shouldHandleAdjustmentRecordsInOutwardDetailProcessing() {
        // Given
        List<StatementRecord> mergedStatements = List.of(
            StatementRecord.builder().accountNumber("**********").build()
        );
        
        List<ProcessedStatement> processedStatements = List.of(
            ProcessedStatement.builder().accountNumber("**********").build()
        );
        
        List<FinalStatement> finalStatements = List.of(
            FinalStatement.builder()
                .accountNumber("**********")
                .adjustmentRecord(true)
                .build()
        );
        
        when(statementDataMergeProcessor.mergeStatementData(any(ReportData.class)))
            .thenReturn(StatementDataMergeResult.success(mergedStatements, List.of(), 1, 0));
        
        when(statementReferenceProcessor.processStatementReference(eq(mergedStatements), any(ReportData.class)))
            .thenReturn(StatementReferenceResult.success(processedStatements, 1, 0, 0));
        
        when(outwardDetailProcessor.processOutwardDetail(eq(processedStatements), eq(List.of()), any(ReportData.class)))
            .thenReturn(OutwardDetailResult.success(finalStatements, List.of(), 1, 0, 1, 0));
        
        // When
        StepResult result = step7Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("adjustmentRecords", 1);
    }
}
