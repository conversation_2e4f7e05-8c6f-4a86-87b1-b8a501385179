package com.scb.bizo.report.service.step.step4;

import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.ReportId;
import com.scb.bizo.report.service.domain.model.ReportStatus;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step4.model.LccRecord;
import com.scb.bizo.report.service.step.step4.model.LccStatement;
import com.scb.bizo.report.service.step.step4.model.ProcessedLccStatement;
import com.scb.bizo.report.service.step.step4.processor.LccDataMergeProcessor;
import com.scb.bizo.report.service.step.step4.processor.LccStatementProcessor;
import com.scb.bizo.report.service.step.step4.result.LccDataMergeResult;
import com.scb.bizo.report.service.step.step4.result.LccStatementResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for Step4Service.
 * 
 * Tests the complete Step 4 processing workflow including:
 * - LCC data merging (EBCMMC07)
 * - LCC statement processing (STMMCG04)
 * - LCC channel filtering and validation
 * - Account and reference key matching logic
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Step 4 Service Tests")
class Step4ServiceTest {
    
    @Mock
    private LccDataMergeProcessor lccDataMergeProcessor;
    
    @Mock
    private LccStatementProcessor lccStatementProcessor;
    
    @InjectMocks
    private Step4Service step4Service;
    
    private MulticastReport testReport;
    private ReportData testInputData;
    
    @BeforeEach
    void setUp() {
        testReport = MulticastReport.builder()
            .reportId(ReportId.generate())
            .status(ReportStatus.PROCESSING)
            .createdAt(LocalDateTime.now())
            .build();
        
        testInputData = ReportData.builder()
            .dataContent("test LCC data")
            .build();
    }
    
    @Test
    @DisplayName("Should return step number 4")
    void shouldReturnStepNumber4() {
        assertThat(step4Service.getStepNumber()).isEqualTo(4);
    }
    
    @Test
    @DisplayName("Should return correct step name")
    void shouldReturnCorrectStepName() {
        assertThat(step4Service.getStepName()).isEqualTo("LCC Processing");
    }
    
    @Test
    @DisplayName("Should return correct COBOL programs")
    void shouldReturnCorrectCobolPrograms() {
        List<String> expectedPrograms = List.of("EBCMMC07+STMMCG04");
        assertThat(step4Service.getCobolPrograms()).isEqualTo(expectedPrograms);
    }
    
    @Test
    @DisplayName("Should successfully process Step 4 with all sub-steps")
    void shouldSuccessfullyProcessStep4() {
        // Given
        List<LccRecord> mergedLccRecords = List.of(
            LccRecord.builder()
                .recordType("STATEMENT")
                .sortKey("********************12345678")
                .accountNumber("**********")
                .recordLength(700)
                .build(),
            LccRecord.builder()
                .recordType("LCC_DETAIL")
                .sortKey("********************12345678")
                .accountNumber("**********")
                .projectCode("BC1")
                .reportNumber("001")
                .recordLength(600)
                .build()
        );
        
        List<ProcessedLccStatement> processedStatements = List.of(
            ProcessedLccStatement.builder()
                .originalStatement("BC statement data".repeat(35)) // 700 chars approx
                .hasLccDetails(true)
                .processed(true)
                .build()
        );
        
        List<LccStatement> lccDetails = List.of(
            LccStatement.builder()
                .accountNumber("**********")
                .processed(true)
                .build()
        );
        
        // Mock processor responses
        when(lccDataMergeProcessor.mergeLccData(any(ReportData.class)))
            .thenReturn(LccDataMergeResult.success(mergedLccRecords, 2));
        
        when(lccStatementProcessor.processLccStatements(eq(mergedLccRecords), any(ReportData.class)))
            .thenReturn(LccStatementResult.success(processedStatements, lccDetails, 1, 1, 0, 0));
        
        // When
        StepResult result = step4Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 4 completed successfully");
        
        // Verify output data structure
        Map<String, Object> outputData = result.getOutputData();
        assertThat(outputData).containsKeys(
            "mergedLccData", 
            "processedStatements", 
            "lccDetails", 
            "processingMetadata",
            "recordCounts"
        );
        
        // Verify record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) outputData.get("recordCounts");
        assertThat(recordCounts).containsEntry("mergedLccRecords", 2);
        assertThat(recordCounts).containsEntry("processedStatements", 1);
        assertThat(recordCounts).containsEntry("lccDetailRecords", 1);
        assertThat(recordCounts).containsEntry("adjustmentRecords", 0);
        assertThat(recordCounts).containsEntry("errorRecords", 0);
        
        // Verify metadata
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) outputData.get("processingMetadata");
        assertThat(metadata).containsEntry("stepNumber", 4);
        assertThat(metadata).containsEntry("lccProcessingEnabled", true);
        assertThat(metadata).containsEntry("businessLogicPreserved", true);
    }
    
    @Test
    @DisplayName("Should fail when LCC data merge fails")
    void shouldFailWhenLccDataMergeFails() {
        // Given
        when(lccDataMergeProcessor.mergeLccData(any(ReportData.class)))
            .thenReturn(LccDataMergeResult.failed("LCC data merge failed"));
        
        // When
        StepResult result = step4Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("LCC data merge failed");
    }
    
    @Test
    @DisplayName("Should fail when LCC statement processing fails")
    void shouldFailWhenLccStatementProcessingFails() {
        // Given
        List<LccRecord> mergedLccRecords = List.of(
            LccRecord.builder().accountNumber("**********").build()
        );
        
        when(lccDataMergeProcessor.mergeLccData(any(ReportData.class)))
            .thenReturn(LccDataMergeResult.success(mergedLccRecords, 1));
        
        when(lccStatementProcessor.processLccStatements(eq(mergedLccRecords), any(ReportData.class)))
            .thenReturn(LccStatementResult.failed("LCC statement processing failed"));
        
        // When
        StepResult result = step4Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("LCC statement processing failed");
    }
    
    @Test
    @DisplayName("Should handle exceptions gracefully")
    void shouldHandleExceptionsGracefully() {
        // Given
        when(lccDataMergeProcessor.mergeLccData(any(ReportData.class)))
            .thenThrow(new RuntimeException("Unexpected error"));
        
        // When
        StepResult result = step4Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("Step 4 processing failed");
    }
    
    @Test
    @DisplayName("Should handle empty LCC data gracefully")
    void shouldHandleEmptyLccDataGracefully() {
        // Given
        List<LccRecord> emptyLccRecords = List.of();
        List<ProcessedLccStatement> emptyStatements = List.of();
        List<LccStatement> emptyDetails = List.of();
        
        when(lccDataMergeProcessor.mergeLccData(any(ReportData.class)))
            .thenReturn(LccDataMergeResult.success(emptyLccRecords, 0));
        
        when(lccStatementProcessor.processLccStatements(eq(emptyLccRecords), any(ReportData.class)))
            .thenReturn(LccStatementResult.success(emptyStatements, emptyDetails, 0, 0, 0, 0));
        
        // When
        StepResult result = step4Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("Step 4 completed successfully");
        
        // Verify empty record counts
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("mergedLccRecords", 0);
        assertThat(recordCounts).containsEntry("processedStatements", 0);
        assertThat(recordCounts).containsEntry("lccDetailRecords", 0);
        assertThat(recordCounts).containsEntry("adjustmentRecords", 0);
        assertThat(recordCounts).containsEntry("errorRecords", 0);
    }
    
    @Test
    @DisplayName("Should handle LCC processing with adjustments")
    void shouldHandleLccProcessingWithAdjustments() {
        // Given
        List<LccRecord> mergedLccRecords = List.of(
            LccRecord.builder()
                .recordType("STATEMENT")
                .accountNumber("**********")
                .build()
        );
        
        List<ProcessedLccStatement> processedStatements = List.of(
            ProcessedLccStatement.builder()
                .adjustmentRecord(true)
                .processed(true)
                .build()
        );
        
        when(lccDataMergeProcessor.mergeLccData(any(ReportData.class)))
            .thenReturn(LccDataMergeResult.success(mergedLccRecords, 1));
        
        when(lccStatementProcessor.processLccStatements(eq(mergedLccRecords), any(ReportData.class)))
            .thenReturn(LccStatementResult.success(processedStatements, List.of(), 1, 0, 1, 0));
        
        // When
        StepResult result = step4Service.processStep(testReport, testInputData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        Map<String, Object> recordCounts = (Map<String, Object>) result.getOutputData().get("recordCounts");
        assertThat(recordCounts).containsEntry("adjustmentRecords", 1);
    }
}
