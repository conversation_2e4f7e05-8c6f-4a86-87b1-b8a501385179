spring:
  application:
    name: multicast-report-service
  
  profiles:
    active: dev
  
  # MongoDB Configuration
  data:
    mongodb:
      host: localhost
      port: 27017
      database: multicast_reports
      auto-index-creation: true
  
  # Virtual Threads Configuration
  threads:
    virtual:
      enabled: true
  
  # Actuator Configuration
  management:
    endpoints:
      web:
        exposure:
          include: health,info,metrics,prometheus
    endpoint:
      health:
        show-details: always

# Logging Configuration
logging:
  level:
    com.scb.bizo: DEBUG
    org.springframework.data.mongodb: INFO
    org.springframework.boot: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Application Configuration
multicast:
  report:
    processing:
      # Maximum number of concurrent reports
      max-concurrent-reports: 10
      # Timeout for step processing (in seconds)
      step-timeout: 300
      # Enable detailed logging
      detailed-logging: true
    
    # File processing configuration
    file:
      # Maximum file size (in MB)
      max-size: 100
      # Supported file formats
      supported-formats: txt,csv,dat
      # Temporary directory for file processing
      temp-directory: /tmp/multicast-reports
    
    # Step configuration
    steps:
      step1:
        enabled: true
        timeout: 120
        retry-attempts: 3
      step2:
        enabled: true
        timeout: 180
        retry-attempts: 3
      step3:
        enabled: true
        timeout: 150
        retry-attempts: 3
      step4:
        enabled: true
        timeout: 200
        retry-attempts: 3
      step5:
        enabled: true
        timeout: 150
        retry-attempts: 3
      step6:
        enabled: true
        timeout: 300
        retry-attempts: 3
      step7:
        enabled: true
        timeout: 180
        retry-attempts: 3
      step8:
        enabled: true
        timeout: 120
        retry-attempts: 3

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  data:
    mongodb:
      host: localhost
      port: 27017
      database: multicast_reports_dev

logging:
  level:
    com.scb.bizo: DEBUG

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  
  data:
    mongodb:
      host: localhost
      port: 27017
      database: multicast_reports_test

logging:
  level:
    com.scb.bizo: INFO

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/multicast_reports_prod}

logging:
  level:
    com.scb.bizo: WARN
    org.springframework: WARN
