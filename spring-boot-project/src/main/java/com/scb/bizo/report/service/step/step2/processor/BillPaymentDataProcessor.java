package com.scb.bizo.report.service.step.step2.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step2.model.BillPaymentRecord;
import com.scb.bizo.report.service.step.step2.result.BillPaymentDataResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Bill Payment Data Processor implementing EBCMMC05 business logic.
 * 
 * This processor migrates the COBOL EBCMMC05 JCL job functionality:
 * 
 * Original COBOL Logic (EBCMMC05):
 * 1. Read bill payment data file (PSPBCM.INF.BCM.T951.DRDBFILE.&DAYDATE.V1.TM1)
 * 2. Sort by account number (positions 7-30, 24 characters)
 * 3. Process 285-character fixed-length records
 * 4. Create VSAM indexed file with 16-character key (positions 1-16)
 * 5. Handle file space allocation and management
 * 
 * Java Implementation:
 * - Preserves exact sorting and record structure logic
 * - Maintains 285-character record length validation
 * - Implements account number key indexing
 * - Provides structured output for statement filtering
 * 
 * Record Structure (285 characters):
 * - Positions 1-6: Statement code
 * - Positions 7-16: Account number (10 digits)
 * - Positions 17-22: Statement date
 * - Positions 23-285: Additional bill payment data
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class BillPaymentDataProcessor {
    
    // COBOL field positions (0-based in Java)
    private static final int STMT_CODE_START = 0;      // Position 1-6 in COBOL
    private static final int STMT_CODE_LENGTH = 6;
    private static final int ACCOUNT_NUMBER_START = 6;  // Position 7-16 in COBOL
    private static final int ACCOUNT_NUMBER_LENGTH = 10;
    private static final int STMT_DATE_START = 16;     // Position 17-22 in COBOL
    private static final int STMT_DATE_LENGTH = 6;
    private static final int RECORD_LENGTH = 285;
    
    // COBOL sort key positions for account number (positions 7-30)
    private static final int SORT_KEY_START = 6;       // Position 7 in COBOL
    private static final int SORT_KEY_LENGTH = 24;
    
    /**
     * Process bill payment data with EBCMMC05 business logic.
     * 
     * @param inputData Input data containing bill payment records
     * @return Bill payment data processing result
     */
    public BillPaymentDataResult processBillPaymentData(ReportData inputData) {
        log.info("Starting bill payment data processing (EBCMMC05)");
        
        try {
            // Extract bill payment records from input data
            List<String> billPaymentRecords = extractBillPaymentRecords(inputData);
            if (billPaymentRecords.isEmpty()) {
                log.warn("No bill payment records found in input data");
                return BillPaymentDataResult.success(new ArrayList<>(), 0);
            }
            
            log.debug("Processing {} bill payment records", billPaymentRecords.size());
            
            // Process each record with COBOL business logic
            List<BillPaymentRecord> processedRecords = new ArrayList<>();
            int processedCount = 0;
            int invalidCount = 0;
            
            for (String record : billPaymentRecords) {
                try {
                    // Validate record length (COBOL requirement)
                    if (record.length() != RECORD_LENGTH) {
                        log.warn("Invalid record length: {} (expected {}), skipping record", 
                            record.length(), RECORD_LENGTH);
                        invalidCount++;
                        continue;
                    }
                    
                    // Extract key fields according to COBOL layout
                    String statementCode = extractStatementCode(record);
                    String accountNumber = extractAccountNumber(record);
                    String statementDate = extractStatementDate(record);
                    
                    // Validate required fields
                    if (accountNumber == null || accountNumber.trim().isEmpty()) {
                        log.warn("Invalid account number in record, skipping");
                        invalidCount++;
                        continue;
                    }
                    
                    // Create bill payment record
                    BillPaymentRecord billPaymentRecord = createBillPaymentRecord(
                        record, statementCode, accountNumber, statementDate);
                    processedRecords.add(billPaymentRecord);
                    processedCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing bill payment record: {}", e.getMessage());
                    invalidCount++;
                }
            }
            
            // Sort by account number (COBOL SORT FIELDS=(7,30,A))
            sortBillPaymentRecords(processedRecords);
            
            log.info("Bill payment data processing completed: {} records processed, {} invalid", 
                processedCount, invalidCount);
            
            return BillPaymentDataResult.success(processedRecords, processedCount);
            
        } catch (Exception e) {
            log.error("Bill payment data processing failed", e);
            return BillPaymentDataResult.failed("Bill payment data processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract bill payment records from input data.
     * 
     * @param inputData Input data
     * @return List of bill payment record strings
     */
    @SuppressWarnings("unchecked")
    private List<String> extractBillPaymentRecords(ReportData inputData) {
        // Handle different input data formats
        if (inputData.getDataContent() != null) {
            // Split by lines if single string
            return List.of(inputData.getDataContent().split("\n"));
        }
        
        // Check metadata for structured data
        Object recordsObj = inputData.getMetadata("billPaymentRecords");
        if (recordsObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        // Check for T951 DRDBFILE data
        Object t951Data = inputData.getMetadata("t951DrdbFile");
        if (t951Data instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Extract statement code from record (COBOL positions 1-6).
     * 
     * @param record The 285-character record
     * @return Statement code or null if invalid
     */
    private String extractStatementCode(String record) {
        try {
            if (record.length() < STMT_CODE_START + STMT_CODE_LENGTH) {
                return null;
            }
            
            return record.substring(STMT_CODE_START, STMT_CODE_START + STMT_CODE_LENGTH)
                .trim();
        } catch (Exception e) {
            log.warn("Failed to extract statement code from record: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Extract account number from record (COBOL positions 7-16).
     * 
     * @param record The 285-character record
     * @return Account number or null if invalid
     */
    private String extractAccountNumber(String record) {
        try {
            if (record.length() < ACCOUNT_NUMBER_START + ACCOUNT_NUMBER_LENGTH) {
                return null;
            }
            
            return record.substring(ACCOUNT_NUMBER_START, ACCOUNT_NUMBER_START + ACCOUNT_NUMBER_LENGTH)
                .trim();
        } catch (Exception e) {
            log.warn("Failed to extract account number from record: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Extract statement date from record (COBOL positions 17-22).
     * 
     * @param record The 285-character record
     * @return Statement date or null if invalid
     */
    private String extractStatementDate(String record) {
        try {
            if (record.length() < STMT_DATE_START + STMT_DATE_LENGTH) {
                return null;
            }
            
            return record.substring(STMT_DATE_START, STMT_DATE_START + STMT_DATE_LENGTH)
                .trim();
        } catch (Exception e) {
            log.warn("Failed to extract statement date from record: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Create bill payment record from COBOL record.
     * 
     * @param record The 285-character record
     * @param statementCode Extracted statement code
     * @param accountNumber Extracted account number
     * @param statementDate Extracted statement date
     * @return BillPaymentRecord instance
     */
    private BillPaymentRecord createBillPaymentRecord(String record, String statementCode, 
                                                     String accountNumber, String statementDate) {
        return BillPaymentRecord.builder()
            .rawRecord(record)
            .statementCode(statementCode)
            .accountNumber(accountNumber)
            .statementDate(statementDate)
            .recordLength(record.length())
            .sortKey(extractSortKey(record))
            .metadata(Map.of(
                "source", "EBCMMC05",
                "recordType", "BILL_PAYMENT",
                "processingTimestamp", System.currentTimeMillis()
            ))
            .build();
    }
    
    /**
     * Extract sort key from record (COBOL positions 7-30).
     * 
     * @param record The 285-character record
     * @return Sort key for ordering
     */
    private String extractSortKey(String record) {
        try {
            if (record.length() < SORT_KEY_START + SORT_KEY_LENGTH) {
                return "";
            }
            
            return record.substring(SORT_KEY_START, SORT_KEY_START + SORT_KEY_LENGTH);
        } catch (Exception e) {
            log.warn("Failed to extract sort key from record: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Sort bill payment records by account number (COBOL SORT FIELDS=(7,30,A)).
     * 
     * @param records List of bill payment records to sort
     */
    private void sortBillPaymentRecords(List<BillPaymentRecord> records) {
        records.sort((a, b) -> {
            // Primary sort by account number
            int result = a.getAccountNumber().compareTo(b.getAccountNumber());
            if (result != 0) return result;
            
            // Secondary sort by statement date
            result = a.getStatementDate().compareTo(b.getStatementDate());
            if (result != 0) return result;
            
            // Tertiary sort by statement code
            return a.getStatementCode().compareTo(b.getStatementCode());
        });
    }
}
