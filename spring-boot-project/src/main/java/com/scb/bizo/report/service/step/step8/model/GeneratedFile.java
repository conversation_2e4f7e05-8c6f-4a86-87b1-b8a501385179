package com.scb.bizo.report.service.step.step8.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a generated output file from EBCMAFTB processing.
 * 
 * This model represents the output files generated by EBCMAFTB,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * Key Features:
 * - File naming conventions (daily, FTP, backup)
 * - File size and record count tracking
 * - File path and location management
 * - Creation timestamp tracking
 * - File type classification
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeneratedFile {
    
    /**
     * File name (e.g., ERP_INTERBANK_EPPLCCBP_20240115_DAILY.txt).
     */
    private String fileName;
    
    /**
     * Full file path including directory.
     */
    private String filePath;
    
    /**
     * File size in bytes.
     */
    private long fileSize;
    
    /**
     * Number of records in the file.
     */
    private int recordCount;
    
    /**
     * File type (DAILY, FTP, BACKUP).
     */
    private String fileType;
    
    /**
     * File creation timestamp.
     */
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    
    /**
     * Additional metadata for the file.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate generated file data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return fileName != null &&
               !fileName.trim().isEmpty() &&
               filePath != null &&
               !filePath.trim().isEmpty() &&
               fileSize >= 0 &&
               recordCount >= 0 &&
               fileType != null &&
               !fileType.trim().isEmpty();
    }
    
    /**
     * Check if file is daily type.
     * 
     * @return true if daily file
     */
    public boolean isDailyFile() {
        return "DAILY".equals(fileType);
    }
    
    /**
     * Check if file is FTP type.
     * 
     * @return true if FTP file
     */
    public boolean isFtpFile() {
        return "FTP".equals(fileType);
    }
    
    /**
     * Check if file is backup type.
     * 
     * @return true if backup file
     */
    public boolean isBackupFile() {
        return "BACKUP".equals(fileType);
    }
    
    /**
     * Get file name without extension.
     * 
     * @return File name without extension
     */
    public String getFileNameWithoutExtension() {
        if (fileName == null) {
            return null;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        
        return fileName;
    }
    
    /**
     * Get file extension.
     * 
     * @return File extension or empty string
     */
    public String getFileExtension() {
        if (fileName == null) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        
        return "";
    }
    
    /**
     * Get file size in KB.
     * 
     * @return File size in KB
     */
    public double getFileSizeKB() {
        return fileSize / 1024.0;
    }
    
    /**
     * Get file size in MB.
     * 
     * @return File size in MB
     */
    public double getFileSizeMB() {
        return fileSize / (1024.0 * 1024.0);
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
