package com.scb.bizo.report.service.domain.model;

/**
 * Enumeration representing the status of a multicast report.
 * 
 * The status reflects the current state of the report in the 8-step processing workflow:
 * SUBMITTED → PROCESSING → COMPLETED (or FAILED)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public enum ReportStatus {
    
    /**
     * Report has been submitted and is waiting to start processing.
     * This is the initial state when a report is first created.
     */
    SUBMITTED("Report submitted and queued for processing"),
    
    /**
     * Report is currently being processed through the 8-step workflow.
     * At least one step has started but the report is not yet complete.
     */
    PROCESSING("Report is being processed through workflow steps"),
    
    /**
     * All 8 steps have completed successfully.
     * The report output file is available for download.
     */
    COMPLETED("All processing steps completed successfully"),
    
    /**
     * Processing failed at one of the steps.
     * The report cannot proceed further without intervention.
     */
    FAILED("Processing failed - manual intervention required"),
    
    /**
     * Report processing was cancelled by user or system.
     * No further processing will occur.
     */
    CANCELLED("Report processing was cancelled");
    
    private final String description;
    
    ReportStatus(String description) {
        this.description = description;
    }
    
    /**
     * Get human-readable description of the status.
     * 
     * @return Status description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Check if the report is in a terminal state (completed, failed, or cancelled).
     * 
     * @return true if terminal state
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == CANCELLED;
    }
    
    /**
     * Check if the report is actively being processed.
     * 
     * @return true if processing
     */
    public boolean isProcessing() {
        return this == PROCESSING;
    }
    
    /**
     * Check if the report can be processed (not in terminal state).
     * 
     * @return true if can be processed
     */
    public boolean canProcess() {
        return this == SUBMITTED || this == PROCESSING;
    }
}
