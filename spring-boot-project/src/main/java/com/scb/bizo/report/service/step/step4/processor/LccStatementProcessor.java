package com.scb.bizo.report.service.step.step4.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step4.model.LccRecord;
import com.scb.bizo.report.service.step.step4.model.LccStatement;
import com.scb.bizo.report.service.step.step4.model.ProcessedLccStatement;
import com.scb.bizo.report.service.step.step4.result.LccStatementResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * LCC Statement Processor implementing STMMCG04 business logic.
 * 
 * This processor migrates the COBOL STMMCG04 program functionality:
 * 
 * Original COBOL Logic (STMMCG04):
 * 1. Read account profiles with LCC flag validation (ACCT-LCC-FLG = 'Y')
 * 2. Process statement files (700 characters) and detail files (600 characters)
 * 3. Match LCC transactions by account and reference:
 *    - STMT-IN-ACCT-KEY = BC-ACC-NO
 *    - BC-REF-KEY = STMT-IN-DESC40(1:16)
 * 4. Filter by channel types: 'BC  ', 'OTCL', 'AIR ', 'ATS '
 * 5. Generate 1300-character output records with embedded LCC details
 * 6. Handle LCC transaction types: BC, CSH, ABC, CBD, BR, LCC
 * 7. Create difference adjustment records for amount reconciliation
 * 8. Manage balance calculations with credit/debit processing
 * 
 * Key Business Rules:
 * - Account must have ACCT-LCC-FLG = 'Y' for LCC processing
 * - Channel filtering: BC, OTCL, AIR, ATS channels only
 * - Transaction type validation: BC, CSH, ABC, CBD, BR, LCC
 * - Status validation: S, C, P, A, R, O status codes
 * - Amount matching and reconciliation with difference adjustments
 * - Reference key matching: BC-REF-KEY = STMT-IN-DESC40(1:16)
 * - Balance calculation: WK-NEW-STMT-IN-BAL with C/D handling
 * - Record structure: 1300-character output with 600-character LCC detail section
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class LccStatementProcessor {
    
    // COBOL field positions for statement records (0-based in Java)
    private static final int STMT_ACCT_KEY_START = 0;   // STMT-IN-ACCT-KEY
    private static final int STMT_ACCT_KEY_LENGTH = 10;
    private static final int STMT_DESC40_START = 104;   // STMT-IN-DESC40 (position 105 in COBOL)
    private static final int STMT_DESC40_LENGTH = 40;
    private static final int STMT_AMT_START = 92;       // STMT-IN-AMT (position 93 in COBOL)
    private static final int STMT_AMT_LENGTH = 15;
    private static final int STMT_DC_CODE_START = 94;   // STMT-IN-DC-CODE (position 95 in COBOL)
    private static final int STMT_DC_CODE_LENGTH = 2;
    private static final int STMT_CHANNEL_START = 106;  // STMT-IN-CHANNEL (position 107 in COBOL)
    private static final int STMT_CHANNEL_LENGTH = 4;
    
    // COBOL field positions for LCC detail records
    private static final int BC_ACC_NO_START = 6;       // BC-ACC-NO (position 7 in COBOL)
    private static final int BC_ACC_NO_LENGTH = 10;
    private static final int BC_REF_KEY_START = 235;    // BC-REF-KEY (position 236 in COBOL)
    private static final int BC_REF_KEY_LENGTH = 16;
    private static final int BC_BCHQAMT_START = 191;    // BC-BCHQAMT (position 192 in COBOL)
    private static final int BC_BCHQAMT_LENGTH = 16;
    private static final int BC_BCHQTYPE_START = 205;   // BC-BCHQTYPE (position 206 in COBOL)
    private static final int BC_BCHQTYPE_LENGTH = 3;
    private static final int BC_BSTATUS_START = 207;    // BC-BSTATUS (position 208 in COBOL)
    
    // Business rule constants
    private static final List<String> LCC_CHANNELS = List.of("BC  ", "OTCL", "AIR ", "ATS ");
    private static final List<String> LCC_TRANSACTION_TYPES = List.of("BC ", "CSH", "ABC", "CBD", "BR ", "LCC");
    private static final List<String> VALID_STATUS_CODES = List.of("S", "C", "P", "A", "R", "O");
    private static final String CREDIT_CODE = "C ";
    private static final String DEBIT_CODE = "D ";
    
    // Record length constants
    private static final int STMT_RECORD_LENGTH = 700;
    private static final int LCC_RECORD_LENGTH = 600;
    private static final int OUTPUT_RECORD_LENGTH = 1300;
    
    /**
     * Process LCC statements with STMMCG04 business logic.
     * 
     * @param mergedLccData Merged LCC data from EBCMMC07
     * @param inputData Additional input data containing account profiles
     * @return LCC statement processing result
     */
    public LccStatementResult processLccStatements(List<LccRecord> mergedLccData, ReportData inputData) {
        log.info("Starting LCC statement processing (STMMCG04)");
        
        try {
            // Extract account profiles
            List<String> accountProfiles = extractAccountProfiles(inputData);
            
            if (accountProfiles.isEmpty()) {
                log.warn("No account profiles found for LCC processing");
                return LccStatementResult.success(new ArrayList<>(), new ArrayList<>(), 0, 0, 0, 0);
            }
            
            log.debug("Processing {} account profiles with {} LCC records", 
                accountProfiles.size(), mergedLccData.size());
            
            // Filter accounts with LCC flag enabled
            List<String> lccEnabledAccounts = filterLccEnabledAccounts(accountProfiles);
            
            // Separate statement and detail records
            List<LccRecord> statementRecords = mergedLccData.stream()
                .filter(record -> "STATEMENT".equals(record.getRecordType()))
                .toList();
            
            List<LccRecord> detailRecords = mergedLccData.stream()
                .filter(record -> "LCC_DETAIL".equals(record.getRecordType()))
                .toList();
            
            List<ProcessedLccStatement> processedStatements = new ArrayList<>();
            List<LccStatement> lccDetails = new ArrayList<>();
            int processedCount = 0;
            int lccDetailCount = 0;
            int adjustmentCount = 0;
            int errorCount = 0;
            
            // Process each LCC-enabled account
            for (String accountProfile : lccEnabledAccounts) {
                String accountNumber = extractAccountNumber(accountProfile);
                
                // Get statements and details for this account
                List<LccRecord> accountStatements = getRecordsForAccount(statementRecords, accountNumber);
                List<LccRecord> accountDetails = getRecordsForAccount(detailRecords, accountNumber);
                
                for (LccRecord statement : accountStatements) {
                    try {
                        // Check if statement is LCC channel
                        if (isLccChannelStatement(statement.getRawRecord())) {
                            // Process LCC matching
                            LccMatchResult matchResult = processLccMatching(statement, accountDetails);
                            
                            if (matchResult.isMatched()) {
                                // Create processed statement with LCC details
                                ProcessedLccStatement processed = createProcessedStatement(
                                    statement, matchResult.getMatchedLccRecord());
                                processedStatements.add(processed);
                                
                                // Create LCC detail record
                                LccStatement lccDetail = createLccDetail(matchResult.getMatchedLccRecord());
                                lccDetails.add(lccDetail);
                                
                                lccDetailCount++;
                            } else {
                                // Create adjustment record for unmatched LCC transaction
                                ProcessedLccStatement adjustment = createAdjustmentRecord(statement);
                                processedStatements.add(adjustment);
                                adjustmentCount++;
                            }
                            
                            processedCount++;
                        } else {
                            // Create regular statement record (no LCC processing)
                            ProcessedLccStatement regular = createRegularStatement(statement);
                            processedStatements.add(regular);
                            processedCount++;
                        }
                        
                    } catch (Exception e) {
                        log.error("Error processing statement for account {}: {}", accountNumber, e.getMessage());
                        errorCount++;
                    }
                }
            }
            
            log.info("LCC statement processing completed: {} processed, {} LCC details, {} adjustments, {} errors", 
                processedCount, lccDetailCount, adjustmentCount, errorCount);
            
            return LccStatementResult.success(processedStatements, lccDetails, 
                processedCount, lccDetailCount, adjustmentCount, errorCount);
            
        } catch (Exception e) {
            log.error("LCC statement processing failed", e);
            return LccStatementResult.failed("LCC statement processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract account profiles from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractAccountProfiles(ReportData inputData) {
        Object accountObj = inputData.getMetadata("accountProfiles");
        if (accountObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        return new ArrayList<>();
    }
    
    /**
     * Filter accounts with ACCT-LCC-FLG = 'Y'.
     */
    private List<String> filterLccEnabledAccounts(List<String> accountProfiles) {
        return accountProfiles.stream()
            .filter(this::isLccEnabled)
            .toList();
    }
    
    /**
     * Check if account has LCC flag enabled (position 153 in COBOL).
     */
    private boolean isLccEnabled(String accountProfile) {
        if (accountProfile.length() < 153) {
            return false;
        }
        return accountProfile.charAt(152) == 'Y'; // Position 153 in COBOL
    }
    
    /**
     * Extract account number from account profile.
     */
    private String extractAccountNumber(String accountProfile) {
        if (accountProfile.length() < 26) {
            return "";
        }
        return accountProfile.substring(16, 26).trim(); // Positions 17-26 in COBOL
    }
    
    /**
     * Get LCC records for specific account.
     */
    private List<LccRecord> getRecordsForAccount(List<LccRecord> records, String accountNumber) {
        return records.stream()
            .filter(record -> accountNumber.equals(record.getAccountNumber()))
            .toList();
    }
    
    /**
     * Check if statement is LCC channel.
     */
    private boolean isLccChannelStatement(String statement) {
        String channel = extractChannel(statement);
        return LCC_CHANNELS.contains(channel);
    }
    
    /**
     * Extract channel from statement.
     */
    private String extractChannel(String statement) {
        if (statement.length() < STMT_CHANNEL_START + STMT_CHANNEL_LENGTH) {
            return "";
        }
        return statement.substring(STMT_CHANNEL_START, STMT_CHANNEL_START + STMT_CHANNEL_LENGTH);
    }
    
    /**
     * Process LCC matching logic.
     */
    private LccMatchResult processLccMatching(LccRecord statement, List<LccRecord> lccDetails) {
        String stmtAccountKey = statement.getAccountNumber();
        String stmtDesc40 = extractStatementDesc40(statement.getRawRecord());
        String stmtRefKey = stmtDesc40.length() >= 16 ? stmtDesc40.substring(0, 16) : stmtDesc40;
        
        for (LccRecord lccRecord : lccDetails) {
            if (isLccMatch(stmtAccountKey, stmtRefKey, statement, lccRecord)) {
                return new LccMatchResult(true, lccRecord);
            }
        }
        
        return new LccMatchResult(false, null);
    }
    
    /**
     * Extract STMT-IN-DESC40 from statement.
     */
    private String extractStatementDesc40(String statement) {
        if (statement.length() < STMT_DESC40_START + STMT_DESC40_LENGTH) {
            return "";
        }
        return statement.substring(STMT_DESC40_START, STMT_DESC40_START + STMT_DESC40_LENGTH).trim();
    }
    
    /**
     * Check if LCC record matches statement.
     */
    private boolean isLccMatch(String stmtAccountKey, String stmtRefKey, LccRecord statement, LccRecord lccRecord) {
        // Match by account number
        if (!stmtAccountKey.equals(lccRecord.getAccountNumber())) {
            return false;
        }
        
        // Match by reference key (BC-REF-KEY = STMT-IN-DESC40(1:16))
        String bcRefKey = extractBcRefKey(lccRecord.getRawRecord());
        if (!stmtRefKey.equals(bcRefKey)) {
            return false;
        }
        
        // Additional validation based on channel and transaction type
        return validateLccTransaction(statement, lccRecord);
    }
    
    /**
     * Extract BC-REF-KEY from LCC detail record.
     */
    private String extractBcRefKey(String lccRecord) {
        if (lccRecord.length() < BC_REF_KEY_START + BC_REF_KEY_LENGTH) {
            return "";
        }
        return lccRecord.substring(BC_REF_KEY_START, BC_REF_KEY_START + BC_REF_KEY_LENGTH).trim();
    }
    
    /**
     * Validate LCC transaction based on STMMCG04 business rules.
     */
    private boolean validateLccTransaction(LccRecord statement, LccRecord lccRecord) {
        String channel = extractChannel(statement.getRawRecord());
        String transactionType = extractTransactionType(lccRecord.getRawRecord());
        String status = extractStatus(lccRecord.getRawRecord());
        
        // Channel-specific validation logic from STMMCG04
        return switch (channel) {
            case "BC  " -> validateBcTransaction(transactionType, status);
            case "OTCL" -> validateOtclTransaction(status);
            case "AIR " -> validateAirTransaction(status);
            case "ATS " -> validateAtsTransaction(transactionType, status);
            default -> false;
        };
    }
    
    /**
     * Extract transaction type from LCC record.
     */
    private String extractTransactionType(String lccRecord) {
        if (lccRecord.length() < BC_BCHQTYPE_START + BC_BCHQTYPE_LENGTH) {
            return "";
        }
        return lccRecord.substring(BC_BCHQTYPE_START, BC_BCHQTYPE_START + BC_BCHQTYPE_LENGTH);
    }
    
    /**
     * Extract status from LCC record.
     */
    private String extractStatus(String lccRecord) {
        if (lccRecord.length() <= BC_BSTATUS_START) {
            return "";
        }
        return String.valueOf(lccRecord.charAt(BC_BSTATUS_START));
    }
    
    /**
     * Validate BC channel transaction (STMMCG04 logic).
     */
    private boolean validateBcTransaction(String transactionType, String status) {
        return ("BC ".equals(transactionType) && "P".equals(status)) ||
               ("CSH".equals(transactionType) && "P".equals(status)) ||
               ("ABC".equals(transactionType) && List.of("O", "P", "R").contains(status));
    }
    
    /**
     * Validate OTCL channel transaction.
     */
    private boolean validateOtclTransaction(String status) {
        return List.of("O", "P").contains(status);
    }
    
    /**
     * Validate AIR channel transaction.
     */
    private boolean validateAirTransaction(String status) {
        return "R".equals(status);
    }
    
    /**
     * Validate ATS channel transaction.
     */
    private boolean validateAtsTransaction(String transactionType, String status) {
        return ("BC ".equals(transactionType) && "R".equals(status)) ||
               ("ABC".equals(transactionType) && "R".equals(status));
    }
    
    /**
     * Create processed statement with LCC details.
     */
    private ProcessedLccStatement createProcessedStatement(LccRecord statement, LccRecord lccRecord) {
        return ProcessedLccStatement.builder()
            .originalStatement(statement.getRawRecord())
            .lccRecord(lccRecord)
            .outputRecord(createOutputRecord(statement.getRawRecord(), lccRecord.getRawRecord()))
            .hasLccDetails(true)
            .processed(true)
            .build();
    }
    
    /**
     * Create LCC detail record.
     */
    private LccStatement createLccDetail(LccRecord lccRecord) {
        return LccStatement.builder()
            .lccRecord(lccRecord)
            .accountNumber(lccRecord.getAccountNumber())
            .processed(true)
            .build();
    }
    
    /**
     * Create adjustment record for unmatched LCC transaction.
     */
    private ProcessedLccStatement createAdjustmentRecord(LccRecord statement) {
        return ProcessedLccStatement.builder()
            .originalStatement(statement.getRawRecord())
            .outputRecord(createAdjustmentOutputRecord(statement.getRawRecord()))
            .hasLccDetails(false)
            .processed(true)
            .adjustmentRecord(true)
            .build();
    }
    
    /**
     * Create regular statement record.
     */
    private ProcessedLccStatement createRegularStatement(LccRecord statement) {
        return ProcessedLccStatement.builder()
            .originalStatement(statement.getRawRecord())
            .outputRecord(createRegularOutputRecord(statement.getRawRecord()))
            .hasLccDetails(false)
            .processed(true)
            .build();
    }
    
    /**
     * Create 1300-character output record with LCC details.
     */
    private String createOutputRecord(String statement, String lccDetail) {
        // Create 1300-character record: 700 (statement) + 600 (LCC detail)
        StringBuilder output = new StringBuilder(statement);
        output.append(lccDetail);
        
        // Pad to 1300 characters if needed
        while (output.length() < OUTPUT_RECORD_LENGTH) {
            output.append(" ");
        }
        
        return output.toString();
    }
    
    /**
     * Create adjustment output record.
     */
    private String createAdjustmentOutputRecord(String statement) {
        StringBuilder output = new StringBuilder(statement);
        
        // Add adjustment message
        String adjustmentMsg = "ERP021:Lcc Debit Adjust Txn  "; // From STMMCG04
        output.append(adjustmentMsg);
        
        // Pad to 1300 characters
        while (output.length() < OUTPUT_RECORD_LENGTH) {
            output.append(" ");
        }
        
        return output.toString();
    }
    
    /**
     * Create regular output record.
     */
    private String createRegularOutputRecord(String statement) {
        StringBuilder output = new StringBuilder(statement);
        
        // Pad to 1300 characters
        while (output.length() < OUTPUT_RECORD_LENGTH) {
            output.append(" ");
        }
        
        return output.toString();
    }
    
    /**
     * Inner class for LCC match results.
     */
    private static class LccMatchResult {
        private final boolean matched;
        private final LccRecord matchedLccRecord;
        
        public LccMatchResult(boolean matched, LccRecord matchedLccRecord) {
            this.matched = matched;
            this.matchedLccRecord = matchedLccRecord;
        }
        
        public boolean isMatched() { return matched; }
        public LccRecord getMatchedLccRecord() { return matchedLccRecord; }
    }
}
