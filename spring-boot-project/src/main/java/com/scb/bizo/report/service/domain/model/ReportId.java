package com.scb.bizo.report.service.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Value object representing a unique report identifier.
 * 
 * This class encapsulates the report ID generation and validation logic.
 * Report IDs are used to track reports throughout the 8-step processing workflow.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportId {
    
    private String value;
    
    /**
     * Generate a new unique report ID.
     * 
     * @return New ReportId instance
     */
    public static ReportId generate() {
        return new ReportId(UUID.randomUUID().toString());
    }
    
    /**
     * Create ReportId from string value.
     * 
     * @param value The string value
     * @return ReportId instance
     * @throws IllegalArgumentException if value is null or empty
     */
    public static ReportId of(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Report ID cannot be null or empty");
        }
        return new ReportId(value.trim());
    }
    
    /**
     * Check if this report ID is valid.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return value != null && !value.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return value;
    }
}
