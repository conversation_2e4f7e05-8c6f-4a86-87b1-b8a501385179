package com.scb.bizo.report.service.domain.model;

import com.scb.bizo.report.service.domain.exception.InvalidStepTransitionException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Aggregate root for multicast report processing.
 * Encapsulates 8-step workflow state and business rules.
 * 
 * This entity represents the complete lifecycle of a multicast report
 * from initial submission through all 8 processing steps to final output.
 * 
 * Business Rules:
 * - Steps must be processed sequentially (1 → 2 → 3 → ... → 8)
 * - Each step must complete successfully before the next can begin
 * - Report status reflects current processing state
 * - All step results are preserved for audit and debugging
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Document(collection = "multicast_reports")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MulticastReport {
    
    @Id
    private ReportId reportId;
    private ReportStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime completedAt;
    private ReportData inputData;
    private ReportData outputData;
    
    @Builder.Default
    private List<ProcessingStep> completedSteps = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Complete a processing step with validation.
     * 
     * @param stepNumber The step number (1-8)
     * @param result The step processing result
     * @throws InvalidStepTransitionException if step cannot be processed
     */
    public void completeStep(int stepNumber, StepResult result) {
        validateStepCompletion(stepNumber);
        
        ProcessingStep step = ProcessingStep.builder()
            .stepNumber(stepNumber)
            .status(result.isSuccess() ? StepStatus.COMPLETED : StepStatus.FAILED)
            .startTime(LocalDateTime.now().minusSeconds(result.getProcessingTime()))
            .endTime(LocalDateTime.now())
            .result(result)
            .build();
        
        // Remove any existing step with same number (for retries)
        completedSteps.removeIf(s -> s.getStepNumber() == stepNumber);
        completedSteps.add(step);
        
        // Update overall status
        updateStatusAfterStep(stepNumber, result.isSuccess());
    }
    
    /**
     * Check if a specific step can be processed.
     * 
     * @param stepNumber The step number to check (1-8)
     * @return true if step can be processed
     */
    public boolean canProcessStep(int stepNumber) {
        if (stepNumber < 1 || stepNumber > 8) {
            return false;
        }
        
        if (stepNumber == 1) {
            return status == ReportStatus.SUBMITTED;
        }
        
        // Check if previous step is completed
        return isStepCompleted(stepNumber - 1);
    }
    
    /**
     * Check if a specific step is completed successfully.
     * 
     * @param stepNumber The step number to check
     * @return true if step is completed successfully
     */
    public boolean isStepCompleted(int stepNumber) {
        return completedSteps.stream()
            .anyMatch(step -> step.getStepNumber() == stepNumber && 
                            step.getStatus() == StepStatus.COMPLETED);
    }
    
    /**
     * Check if all 8 steps are completed.
     * 
     * @return true if all steps are completed successfully
     */
    public boolean isComplete() {
        return completedSteps.stream()
            .filter(step -> step.getStatus() == StepStatus.COMPLETED)
            .mapToInt(ProcessingStep::getStepNumber)
            .distinct()
            .count() == 8;
    }
    
    /**
     * Get current processing progress (0-100%).
     * 
     * @return Progress percentage
     */
    public double getProgress() {
        long completedCount = completedSteps.stream()
            .filter(step -> step.getStatus() == StepStatus.COMPLETED)
            .mapToInt(ProcessingStep::getStepNumber)
            .distinct()
            .count();
        return (completedCount / 8.0) * 100.0;
    }
    
    /**
     * Update report status.
     * 
     * @param newStatus The new status
     */
    public void updateStatus(ReportStatus newStatus) {
        this.status = newStatus;
        if (newStatus == ReportStatus.COMPLETED) {
            this.completedAt = LocalDateTime.now();
        }
    }
    
    /**
     * Add processing error.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        this.errors.add(error);
    }
    
    /**
     * Get the next step number to be processed.
     * 
     * @return Next step number, or -1 if all steps are complete
     */
    public int getNextStepNumber() {
        for (int step = 1; step <= 8; step++) {
            if (!isStepCompleted(step)) {
                return step;
            }
        }
        return -1; // All steps completed
    }
    
    /**
     * Get step result by step number.
     * 
     * @param stepNumber The step number
     * @return Step result or null if not found
     */
    public StepResult getStepResult(int stepNumber) {
        return completedSteps.stream()
            .filter(step -> step.getStepNumber() == stepNumber)
            .map(ProcessingStep::getResult)
            .findFirst()
            .orElse(null);
    }
    
    private void validateStepCompletion(int stepNumber) {
        if (!canProcessStep(stepNumber)) {
            throw new InvalidStepTransitionException(
                "Cannot process step " + stepNumber + " in current state. Status: " + status);
        }
    }
    
    private void updateStatusAfterStep(int stepNumber, boolean success) {
        if (!success) {
            this.status = ReportStatus.FAILED;
        } else if (stepNumber == 8) {
            this.status = ReportStatus.COMPLETED;
            this.completedAt = LocalDateTime.now();
        } else {
            this.status = ReportStatus.PROCESSING;
        }
    }
}
