package com.scb.bizo.report.service.step.step2.result;

import com.scb.bizo.report.service.step.step2.model.BillPaymentRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for bill payment data processing (EBCMMC05).
 * 
 * Contains the results of processing bill payment data including:
 * - List of processed bill payment records
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillPaymentDataResult {
    
    private boolean success;
    private String errorMessage;
    private int processedCount;
    private int invalidCount;
    
    @Builder.Default
    private List<BillPaymentRecord> billPaymentData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param billPaymentData List of processed bill payment records
     * @param processedCount Number of records processed
     * @return Successful BillPaymentDataResult
     */
    public static BillPaymentDataResult success(List<BillPaymentRecord> billPaymentData, int processedCount) {
        return BillPaymentDataResult.builder()
            .success(true)
            .billPaymentData(billPaymentData != null ? new ArrayList<>(billPaymentData) : new ArrayList<>())
            .processedCount(processedCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed BillPaymentDataResult
     */
    public static BillPaymentDataResult failed(String errorMessage) {
        return BillPaymentDataResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .billPaymentData(new ArrayList<>())
            .processedCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of bill payment records.
     * 
     * @return Bill payment count
     */
    public int getBillPaymentCount() {
        return billPaymentData != null ? billPaymentData.size() : 0;
    }
    
    /**
     * Check if any bill payment records were processed.
     * 
     * @return true if records exist
     */
    public boolean hasBillPaymentData() {
        return getBillPaymentCount() > 0;
    }
}
