package com.scb.bizo.report.service.step.step1.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step1.model.AccountProfile;
import com.scb.bizo.report.service.step.step1.result.AccountSetupResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Account Setup Processor implementing EBCMMC01 business logic.
 * 
 * This processor migrates the COBOL EBCMMC01 job functionality:
 * 
 * Original COBOL Logic (EBCMMC01):
 * 1. Read ERP account data file (PSPBCM.ERP.BCM.P140.ERPACCT)
 * 2. Sort by account number (positions 17-26, 10 characters)
 * 3. Filter by multicash flag = 'Y' (position 151)
 * 4. Create VSAM indexed file with account number as key
 * 5. Record structure: 200-character fixed-length records
 * 
 * Java Implementation:
 * - Preserves exact sorting and filtering logic
 * - Maintains 200-character record structure validation
 * - Implements account number key indexing
 * - Provides structured output for next processing step
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class AccountSetupProcessor {
    
    // COBOL field positions (1-based in COBOL, 0-based in Java)
    private static final int ACCOUNT_NUMBER_START = 16; // Position 17 in COBOL
    private static final int ACCOUNT_NUMBER_LENGTH = 10;
    private static final int MULTICASH_FLAG_POSITION = 150; // Position 151 in COBOL
    private static final int RECORD_LENGTH = 200;
    
    /**
     * Process account setup with EBCMMC01 business logic.
     * 
     * @param inputData Input data containing ERP account records
     * @return Account setup processing result
     */
    public AccountSetupResult processAccountSetup(ReportData inputData) {
        log.info("Starting account setup processing (EBCMMC01)");
        
        try {
            // Extract account records from input data
            List<String> erpRecords = extractErpRecords(inputData);
            if (erpRecords.isEmpty()) {
                log.warn("No ERP account records found in input data");
                return AccountSetupResult.success(new ArrayList<>(), 0);
            }
            
            log.debug("Processing {} ERP account records", erpRecords.size());
            
            // Process each record with COBOL business logic
            List<AccountProfile> accountProfiles = new ArrayList<>();
            int processedCount = 0;
            int filteredCount = 0;
            
            for (String record : erpRecords) {
                try {
                    // Validate record length (COBOL requirement)
                    if (record.length() != RECORD_LENGTH) {
                        log.warn("Invalid record length: {} (expected {}), skipping record", 
                            record.length(), RECORD_LENGTH);
                        continue;
                    }
                    
                    // Extract multicash flag (position 151 in COBOL)
                    char multicashFlag = record.charAt(MULTICASH_FLAG_POSITION);
                    
                    // Filter by multicash flag = 'Y' (COBOL logic)
                    if (multicashFlag != 'Y') {
                        filteredCount++;
                        continue;
                    }
                    
                    // Extract account number (positions 17-26 in COBOL)
                    String accountNumber = extractAccountNumber(record);
                    if (accountNumber == null || accountNumber.trim().isEmpty()) {
                        log.warn("Invalid account number in record, skipping");
                        continue;
                    }
                    
                    // Create account profile
                    AccountProfile profile = createAccountProfile(record, accountNumber);
                    accountProfiles.add(profile);
                    processedCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing account record: {}", e.getMessage());
                    // Continue processing other records
                }
            }
            
            // Sort by account number (COBOL SORT FIELDS=(17,10,A))
            accountProfiles.sort((a, b) -> a.getAccountNumber().compareTo(b.getAccountNumber()));
            
            log.info("Account setup completed: {} records processed, {} filtered out", 
                processedCount, filteredCount);
            
            return AccountSetupResult.success(accountProfiles, processedCount);
            
        } catch (Exception e) {
            log.error("Account setup processing failed", e);
            return AccountSetupResult.failed("Account setup processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract ERP records from input data.
     * 
     * @param inputData Input data
     * @return List of ERP record strings
     */
    @SuppressWarnings("unchecked")
    private List<String> extractErpRecords(ReportData inputData) {
        // Handle different input data formats
        if (inputData.getDataContent() != null) {
            // Split by lines if single string
            return List.of(inputData.getDataContent().split("\n"));
        }
        
        // Check metadata for structured data
        Object recordsObj = inputData.getMetadata("erpRecords");
        if (recordsObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Extract account number from record (COBOL positions 17-26).
     * 
     * @param record The 200-character record
     * @return Account number or null if invalid
     */
    private String extractAccountNumber(String record) {
        try {
            if (record.length() < ACCOUNT_NUMBER_START + ACCOUNT_NUMBER_LENGTH) {
                return null;
            }
            
            return record.substring(ACCOUNT_NUMBER_START, ACCOUNT_NUMBER_START + ACCOUNT_NUMBER_LENGTH)
                .trim();
        } catch (Exception e) {
            log.warn("Failed to extract account number from record: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Create account profile from COBOL record.
     * 
     * @param record The 200-character record
     * @param accountNumber Extracted account number
     * @return AccountProfile instance
     */
    private AccountProfile createAccountProfile(String record, String accountNumber) {
        return AccountProfile.builder()
            .accountNumber(accountNumber)
            .rawRecord(record)
            .multicashEnabled(true) // Already filtered for 'Y'
            .recordLength(record.length())
            .metadata(Map.of(
                "source", "EBCMMC01",
                "recordType", "ERP_ACCOUNT",
                "processingTimestamp", System.currentTimeMillis()
            ))
            .build();
    }
}
