package com.scb.bizo.report.service.step.step3.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing an EPP statement detail from STMMCG03 processing.
 * 
 * This model represents the EPP detail data extracted and processed by STMMCG03,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - EPP detail record management
 * - Account and amount matching validation
 * - Processing flag management (DETL-EPP-PROCESS)
 * - Error handling for unmatched transactions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EppStatement {
    
    /**
     * Associated EPP record.
     */
    private EppRecord eppRecord;
    
    /**
     * Account number for this EPP statement.
     */
    private String accountNumber;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * EPP processing flag (DETL-EPP-PROCESS).
     */
    private boolean eppProcessFlag;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate EPP statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return eppRecord != null &&
               eppRecord.isValid() &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty();
    }
    
    /**
     * Check if EPP statement has been processed.
     * 
     * @return true if processed
     */
    public boolean isProcessed() {
        return processed && eppProcessFlag;
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
