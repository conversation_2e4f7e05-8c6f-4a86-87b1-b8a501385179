package com.scb.bizo.report.service.step.step1.result;

import com.scb.bizo.report.service.step.step1.model.AccountProfile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for account setup processing (EBCMMC01).
 * 
 * Contains the results of processing ERP account data including:
 * - List of processed account profiles
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountSetupResult {
    
    private boolean success;
    private String errorMessage;
    private int processedCount;
    private int filteredCount;
    
    @Builder.Default
    private List<AccountProfile> accountData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param accountData List of processed account profiles
     * @param processedCount Number of records processed
     * @return Successful AccountSetupResult
     */
    public static AccountSetupResult success(List<AccountProfile> accountData, int processedCount) {
        return AccountSetupResult.builder()
            .success(true)
            .accountData(accountData != null ? new ArrayList<>(accountData) : new ArrayList<>())
            .processedCount(processedCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed AccountSetupResult
     */
    public static AccountSetupResult failed(String errorMessage) {
        return AccountSetupResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .accountData(new ArrayList<>())
            .processedCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of account profiles.
     * 
     * @return Account count
     */
    public int getAccountCount() {
        return accountData != null ? accountData.size() : 0;
    }
    
    /**
     * Check if any accounts were processed.
     * 
     * @return true if accounts exist
     */
    public boolean hasAccounts() {
        return getAccountCount() > 0;
    }
}
