package com.scb.bizo.report.service.step.step3.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step3.model.EppRecord;
import com.scb.bizo.report.service.step.step3.model.EppStatement;
import com.scb.bizo.report.service.step.step3.model.ProcessedEppStatement;
import com.scb.bizo.report.service.step.step3.result.EppStatementResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * EPP Statement Processor implementing STMMCG03 business logic.
 * 
 * This processor migrates the COBOL STMMCG03 program functionality:
 * 
 * Original COBOL Logic (STMMCG03):
 * 1. Read account profiles with EBPP flag validation (ACCT-EBPP-FLG = 'Y')
 * 2. Process statement files (550 characters) and detail files (185 characters)
 * 3. Match EPP transactions by account and amount:
 *    - STMT-IN-ACCT-KEY = DETL-BILL-AC-KEY
 *    - STMT-IN-DR-AC6 = DETL-ACC-NO6-KEY
 *    - STMT-IN-AMT = DETL-PAID-AMT
 * 4. Filter EBPP channel transactions with credit code ('C ')
 * 5. Generate 700-character output records with embedded EPP details
 * 6. Handle EPP processing flag (DETL-EPP-PROCESS = 'Y')
 * 7. Create error records for unmatched transactions
 * 
 * Key Business Rules:
 * - Account must have ACCT-EBPP-FLG = 'Y' for EPP processing
 * - Only process STMT-IN-CHANNEL = 'EBPP' with STMT-IN-DC-CODE = 'C '
 * - Amount matching: STMT-IN-AMT must equal DETL-PAID-AMT exactly
 * - Prevent duplicate processing: DETL-EPP-PROCESS flag management
 * - Error handling: Generate error records for unmatched EPP credits
 * - Record structure: 700-character output with 150-character EPP detail section
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class EppStatementProcessor {
    
    // COBOL field positions for statement records (0-based in Java)
    private static final int STMT_ACCT_KEY_START = 0;   // STMT-IN-ACCT-KEY
    private static final int STMT_ACCT_KEY_LENGTH = 10;
    private static final int STMT_DR_AC6_START = 101;   // STMT-IN-DR-AC6 (position 102 in COBOL)
    private static final int STMT_DR_AC6_LENGTH = 6;
    private static final int STMT_AMT_START = 92;       // STMT-IN-AMT (position 93 in COBOL)
    private static final int STMT_AMT_LENGTH = 15;
    private static final int STMT_DC_CODE_START = 94;   // STMT-IN-DC-CODE (position 95 in COBOL)
    private static final int STMT_DC_CODE_LENGTH = 2;
    private static final int STMT_CHANNEL_START = 104;  // STMT-IN-CHANNEL (position 105 in COBOL)
    private static final int STMT_CHANNEL_LENGTH = 4;
    
    // COBOL field positions for detail records
    private static final int DETL_BILL_AC_START = 1;    // DETL-BILL-AC-KEY
    private static final int DETL_BILL_AC_LENGTH = 10;
    private static final int DETL_ACC_NO6_START = 11;   // DETL-ACC-NO6-KEY
    private static final int DETL_ACC_NO6_LENGTH = 6;
    private static final int DETL_PAID_AMT_START = 189;  // DETL-PAID-AMT
    private static final int DETL_PAID_AMT_LENGTH = 15;
    private static final int DETL_EPP_PROCESS_START = 192; // DETL-EPP-PROCESS
    
    // Business rule constants
    private static final String EBPP_CHANNEL = "EBPP";
    private static final String CREDIT_CODE = "C ";
    private static final String EPP_PROCESSED_FLAG = "Y";
    private static final String ERROR_MESSAGE = "ERP024:EPP Credit Adjust Not found Key";
    
    // Record length constants
    private static final int STMT_RECORD_LENGTH = 550;
    private static final int DETL_RECORD_LENGTH = 185;
    private static final int OUTPUT_RECORD_LENGTH = 700;
    
    /**
     * Process EPP statements with STMMCG03 business logic.
     * 
     * @param mergedEppData Merged EPP data from EBCMMC06
     * @param inputData Additional input data containing account profiles and statements
     * @return EPP statement processing result
     */
    public EppStatementResult processEppStatements(List<EppRecord> mergedEppData, ReportData inputData) {
        log.info("Starting EPP statement processing (STMMCG03)");
        
        try {
            // Extract account profiles and statements
            List<String> accountProfiles = extractAccountProfiles(inputData);
            List<String> statementRecords = extractStatementRecords(inputData);
            
            if (accountProfiles.isEmpty() || statementRecords.isEmpty()) {
                log.warn("No account profiles or statement records found for EPP processing");
                return EppStatementResult.success(new ArrayList<>(), new ArrayList<>(), 0, 0, 0);
            }
            
            log.debug("Processing {} account profiles and {} statement records with {} EPP records", 
                accountProfiles.size(), statementRecords.size(), mergedEppData.size());
            
            // Filter accounts with EBPP flag enabled
            List<String> ebppEnabledAccounts = filterEbppEnabledAccounts(accountProfiles);
            
            List<ProcessedEppStatement> processedStatements = new ArrayList<>();
            List<EppStatement> eppDetails = new ArrayList<>();
            int processedCount = 0;
            int eppDetailCount = 0;
            int errorCount = 0;
            
            // Process each EBPP-enabled account
            for (String accountProfile : ebppEnabledAccounts) {
                String accountNumber = extractAccountNumber(accountProfile);
                
                // Get statements for this account
                List<String> accountStatements = getStatementsForAccount(statementRecords, accountNumber);
                
                for (String statement : accountStatements) {
                    try {
                        // Check if statement is EBPP channel with credit code
                        if (isEbppCreditStatement(statement)) {
                            // Process EPP matching
                            EppMatchResult matchResult = processEppMatching(statement, mergedEppData);
                            
                            if (matchResult.isMatched()) {
                                // Create processed statement with EPP details
                                ProcessedEppStatement processed = createProcessedStatement(
                                    statement, matchResult.getMatchedEppRecord());
                                processedStatements.add(processed);
                                
                                // Create EPP detail record
                                EppStatement eppDetail = createEppDetail(matchResult.getMatchedEppRecord());
                                eppDetails.add(eppDetail);
                                
                                eppDetailCount++;
                            } else {
                                // Create error record for unmatched EPP credit
                                ProcessedEppStatement errorRecord = createErrorRecord(statement);
                                processedStatements.add(errorRecord);
                                errorCount++;
                            }
                            
                            processedCount++;
                        } else {
                            // Create regular statement record (no EPP processing)
                            ProcessedEppStatement regular = createRegularStatement(statement);
                            processedStatements.add(regular);
                            processedCount++;
                        }
                        
                    } catch (Exception e) {
                        log.error("Error processing statement for account {}: {}", accountNumber, e.getMessage());
                        errorCount++;
                    }
                }
            }
            
            log.info("EPP statement processing completed: {} processed, {} EPP details, {} errors", 
                processedCount, eppDetailCount, errorCount);
            
            return EppStatementResult.success(processedStatements, eppDetails, 
                processedCount, eppDetailCount, errorCount);
            
        } catch (Exception e) {
            log.error("EPP statement processing failed", e);
            return EppStatementResult.failed("EPP statement processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract account profiles from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractAccountProfiles(ReportData inputData) {
        Object accountObj = inputData.getMetadata("accountProfiles");
        if (accountObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        return new ArrayList<>();
    }
    
    /**
     * Extract statement records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractStatementRecords(ReportData inputData) {
        Object stmtObj = inputData.getMetadata("statementRecords");
        if (stmtObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        return new ArrayList<>();
    }
    
    /**
     * Filter accounts with ACCT-EBPP-FLG = 'Y'.
     */
    private List<String> filterEbppEnabledAccounts(List<String> accountProfiles) {
        return accountProfiles.stream()
            .filter(this::isEbppEnabled)
            .toList();
    }
    
    /**
     * Check if account has EBPP flag enabled (position 151 in COBOL).
     */
    private boolean isEbppEnabled(String accountProfile) {
        if (accountProfile.length() < 151) {
            return false;
        }
        return accountProfile.charAt(150) == 'Y'; // Position 151 in COBOL
    }
    
    /**
     * Extract account number from account profile.
     */
    private String extractAccountNumber(String accountProfile) {
        if (accountProfile.length() < 26) {
            return "";
        }
        return accountProfile.substring(16, 26).trim(); // Positions 17-26 in COBOL
    }
    
    /**
     * Get statements for specific account.
     */
    private List<String> getStatementsForAccount(List<String> statements, String accountNumber) {
        return statements.stream()
            .filter(stmt -> accountNumber.equals(extractStatementAccountNumber(stmt)))
            .toList();
    }
    
    /**
     * Extract account number from statement record.
     */
    private String extractStatementAccountNumber(String statement) {
        if (statement.length() < STMT_ACCT_KEY_START + STMT_ACCT_KEY_LENGTH) {
            return "";
        }
        return statement.substring(STMT_ACCT_KEY_START, STMT_ACCT_KEY_START + STMT_ACCT_KEY_LENGTH).trim();
    }
    
    /**
     * Check if statement is EBPP channel with credit code.
     */
    private boolean isEbppCreditStatement(String statement) {
        String channel = extractChannel(statement);
        String dcCode = extractDcCode(statement);
        return EBPP_CHANNEL.equals(channel) && CREDIT_CODE.equals(dcCode);
    }
    
    /**
     * Extract channel from statement.
     */
    private String extractChannel(String statement) {
        if (statement.length() < STMT_CHANNEL_START + STMT_CHANNEL_LENGTH) {
            return "";
        }
        return statement.substring(STMT_CHANNEL_START, STMT_CHANNEL_START + STMT_CHANNEL_LENGTH);
    }
    
    /**
     * Extract DC code from statement.
     */
    private String extractDcCode(String statement) {
        if (statement.length() < STMT_DC_CODE_START + STMT_DC_CODE_LENGTH) {
            return "";
        }
        return statement.substring(STMT_DC_CODE_START, STMT_DC_CODE_START + STMT_DC_CODE_LENGTH);
    }
    
    /**
     * Process EPP matching logic.
     */
    private EppMatchResult processEppMatching(String statement, List<EppRecord> eppRecords) {
        String stmtAccountKey = extractStatementAccountNumber(statement);
        String stmtDrAc6 = extractDrAc6(statement);
        BigDecimal stmtAmount = extractAmount(statement);
        
        for (EppRecord eppRecord : eppRecords) {
            if (isEppMatch(stmtAccountKey, stmtDrAc6, stmtAmount, eppRecord)) {
                return new EppMatchResult(true, eppRecord);
            }
        }
        
        return new EppMatchResult(false, null);
    }
    
    /**
     * Extract DR-AC6 from statement.
     */
    private String extractDrAc6(String statement) {
        if (statement.length() < STMT_DR_AC6_START + STMT_DR_AC6_LENGTH) {
            return "";
        }
        return statement.substring(STMT_DR_AC6_START, STMT_DR_AC6_START + STMT_DR_AC6_LENGTH);
    }
    
    /**
     * Extract amount from statement.
     */
    private BigDecimal extractAmount(String statement) {
        try {
            if (statement.length() < STMT_AMT_START + STMT_AMT_LENGTH) {
                return BigDecimal.ZERO;
            }
            String amtStr = statement.substring(STMT_AMT_START, STMT_AMT_START + STMT_AMT_LENGTH).trim();
            return new BigDecimal(amtStr);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * Check if EPP record matches statement.
     */
    private boolean isEppMatch(String stmtAccountKey, String stmtDrAc6, BigDecimal stmtAmount, EppRecord eppRecord) {
        // Implementation would check DETL-BILL-AC-KEY, DETL-ACC-NO6-KEY, and DETL-PAID-AMT
        // For now, simplified matching by account number
        return stmtAccountKey.equals(eppRecord.getAccountNumber());
    }
    
    /**
     * Create processed statement with EPP details.
     */
    private ProcessedEppStatement createProcessedStatement(String statement, EppRecord eppRecord) {
        return ProcessedEppStatement.builder()
            .originalStatement(statement)
            .eppRecord(eppRecord)
            .outputRecord(createOutputRecord(statement, eppRecord))
            .hasEppDetails(true)
            .processed(true)
            .build();
    }
    
    /**
     * Create EPP detail record.
     */
    private EppStatement createEppDetail(EppRecord eppRecord) {
        return EppStatement.builder()
            .eppRecord(eppRecord)
            .accountNumber(eppRecord.getAccountNumber())
            .processed(true)
            .build();
    }
    
    /**
     * Create error record for unmatched EPP credit.
     */
    private ProcessedEppStatement createErrorRecord(String statement) {
        return ProcessedEppStatement.builder()
            .originalStatement(statement)
            .outputRecord(createErrorOutputRecord(statement))
            .hasEppDetails(false)
            .processed(true)
            .errorRecord(true)
            .errorMessage(ERROR_MESSAGE)
            .build();
    }
    
    /**
     * Create regular statement record.
     */
    private ProcessedEppStatement createRegularStatement(String statement) {
        return ProcessedEppStatement.builder()
            .originalStatement(statement)
            .outputRecord(createRegularOutputRecord(statement))
            .hasEppDetails(false)
            .processed(true)
            .build();
    }
    
    /**
     * Create 700-character output record with EPP details.
     */
    private String createOutputRecord(String statement, EppRecord eppRecord) {
        // Create 700-character record: 550 (statement) + 150 (EPP detail)
        StringBuilder output = new StringBuilder(statement);
        output.append(eppRecord.getFormattedRecord().substring(0, Math.min(150, eppRecord.getFormattedRecord().length())));
        
        // Pad to 700 characters
        while (output.length() < OUTPUT_RECORD_LENGTH) {
            output.append(" ");
        }
        
        return output.toString();
    }
    
    /**
     * Create error output record.
     */
    private String createErrorOutputRecord(String statement) {
        StringBuilder output = new StringBuilder(statement);
        output.append(ERROR_MESSAGE);
        
        // Pad to 700 characters
        while (output.length() < OUTPUT_RECORD_LENGTH) {
            output.append(" ");
        }
        
        return output.toString();
    }
    
    /**
     * Create regular output record.
     */
    private String createRegularOutputRecord(String statement) {
        StringBuilder output = new StringBuilder(statement);
        
        // Pad to 700 characters
        while (output.length() < OUTPUT_RECORD_LENGTH) {
            output.append(" ");
        }
        
        return output.toString();
    }
    
    /**
     * Inner class for EPP match results.
     */
    private static class EppMatchResult {
        private final boolean matched;
        private final EppRecord matchedEppRecord;
        
        public EppMatchResult(boolean matched, EppRecord matchedEppRecord) {
            this.matched = matched;
            this.matchedEppRecord = matchedEppRecord;
        }
        
        public boolean isMatched() { return matched; }
        public EppRecord getMatchedEppRecord() { return matchedEppRecord; }
    }
}
