package com.scb.bizo.report.service.step.step3.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing an EPP record from EBCMMC06 processing.
 * 
 * This model represents the EPP data processed by EBCMMC06,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * COBOL Record Structure:
 * - Input: 150-character EPP/EPPD records
 * - Output: 185-character formatted records with OUTREC fields
 * - Sort fields: (1,1,A,124,10,A,64,6,A,27,16,A)
 *   * Position 1: Record type ('P' for payment)
 *   * Positions 124-133: Account number (10 digits)
 *   * Positions 64-69: Date field (6 digits)
 *   * Positions 27-42: Payment reference (16 characters)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EppRecord {
    
    /**
     * Record type from position 1 ('P' for payment).
     */
    private String recordType;
    
    /**
     * Account number from positions 124-133.
     */
    private String accountNumber;
    
    /**
     * Date field from positions 64-69.
     */
    private String dateField;
    
    /**
     * Payment reference from positions 27-42.
     */
    private String paymentReference;
    
    /**
     * Unique record key for duplicate detection.
     */
    private String recordKey;
    
    /**
     * Raw input record (150 characters).
     */
    private String rawRecord;
    
    /**
     * Formatted output record (185 characters) with OUTREC fields.
     */
    private String formattedRecord;
    
    /**
     * Record length validation.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate EPP record data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return recordType != null &&
               "P".equals(recordType) &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               dateField != null &&
               paymentReference != null &&
               recordLength == 185 &&
               formattedRecord != null &&
               formattedRecord.length() == 185;
    }
    
    /**
     * Check if record is payment type.
     * 
     * @return true if payment record
     */
    public boolean isPaymentRecord() {
        return "P".equals(recordType);
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from formatted record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractFormattedField(int startPos, int length) {
        if (formattedRecord == null || 
            startPos < 0 || 
            startPos + length > formattedRecord.length()) {
            return null;
        }
        
        return formattedRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
