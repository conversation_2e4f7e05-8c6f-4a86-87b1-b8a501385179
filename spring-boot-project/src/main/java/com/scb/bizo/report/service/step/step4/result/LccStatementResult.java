package com.scb.bizo.report.service.step.step4.result;

import com.scb.bizo.report.service.step.step4.model.LccStatement;
import com.scb.bizo.report.service.step.step4.model.ProcessedLccStatement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for LCC statement processing (STMMCG04).
 * 
 * Contains the results of processing LCC statements including:
 * - List of processed LCC statements
 * - List of LCC detail records
 * - Processing statistics including adjustment counts
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LccStatementResult {
    
    private boolean success;
    private String errorMessage;
    private int processedCount;
    private int lccDetailCount;
    private int adjustmentCount;
    private int errorCount;
    
    @Builder.Default
    private List<ProcessedLccStatement> processedStatements = new ArrayList<>();
    
    @Builder.Default
    private List<LccStatement> lccDetails = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param processedStatements List of processed LCC statements
     * @param lccDetails List of LCC detail records
     * @param processedCount Number of statements processed
     * @param lccDetailCount Number of LCC details
     * @param adjustmentCount Number of adjustment records
     * @param errorCount Number of error records
     * @return Successful LccStatementResult
     */
    public static LccStatementResult success(List<ProcessedLccStatement> processedStatements,
                                           List<LccStatement> lccDetails,
                                           int processedCount, 
                                           int lccDetailCount, 
                                           int adjustmentCount,
                                           int errorCount) {
        return LccStatementResult.builder()
            .success(true)
            .processedStatements(processedStatements != null ? new ArrayList<>(processedStatements) : new ArrayList<>())
            .lccDetails(lccDetails != null ? new ArrayList<>(lccDetails) : new ArrayList<>())
            .processedCount(processedCount)
            .lccDetailCount(lccDetailCount)
            .adjustmentCount(adjustmentCount)
            .errorCount(errorCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed LccStatementResult
     */
    public static LccStatementResult failed(String errorMessage) {
        return LccStatementResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .processedStatements(new ArrayList<>())
            .lccDetails(new ArrayList<>())
            .processedCount(0)
            .lccDetailCount(0)
            .adjustmentCount(0)
            .errorCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of processed statements.
     * 
     * @return Processed statement count
     */
    public int getProcessedStatementCount() {
        return processedStatements != null ? processedStatements.size() : 0;
    }
    
    /**
     * Get total number of LCC details.
     * 
     * @return LCC detail count
     */
    public int getLccDetailRecordCount() {
        return lccDetails != null ? lccDetails.size() : 0;
    }
    
    /**
     * Check if any statements were processed.
     * 
     * @return true if processed statements exist
     */
    public boolean hasProcessedStatements() {
        return getProcessedStatementCount() > 0;
    }
    
    /**
     * Check if any LCC details were created.
     * 
     * @return true if LCC details exist
     */
    public boolean hasLccDetails() {
        return getLccDetailRecordCount() > 0;
    }
    
    /**
     * Get success rate percentage.
     * 
     * @return Success rate percentage
     */
    public double getSuccessRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return ((processedCount - errorCount) * 100.0) / processedCount;
    }
    
    /**
     * Get LCC matching rate percentage.
     * 
     * @return LCC matching rate percentage
     */
    public double getLccMatchingRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (lccDetailCount * 100.0) / processedCount;
    }
    
    /**
     * Get adjustment rate percentage.
     * 
     * @return Adjustment rate percentage
     */
    public double getAdjustmentRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (adjustmentCount * 100.0) / processedCount;
    }
}
