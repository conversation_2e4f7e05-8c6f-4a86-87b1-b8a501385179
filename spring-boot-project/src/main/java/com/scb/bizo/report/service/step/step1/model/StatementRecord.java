package com.scb.bizo.report.service.step.step1.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a statement record from STMBDD06 processing.
 * 
 * This model represents the statement data processed by EBCMMC02 + STMBDD06,
 * maintaining the structure and business rules from the original COBOL programs.
 * 
 * COBOL Record Structure (350 characters):
 * - Positions 1-2: Bank code (must be '14')
 * - Position 3: Filler
 * - Positions 4-6: Currency code (must be '764')
 * - Position 7: Filler
 * - Positions 8-18: Account number (11 digits)
 * - Positions 19-350: Additional statement data
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatementRecord {
    
    /**
     * Bank code from positions 1-2.
     * Must be '14' for BCM statements.
     */
    private String bankCode;
    
    /**
     * Currency code from positions 4-6.
     * Must be '764' for Thai Baht.
     */
    private String currencyCode;
    
    /**
     * Account number from positions 8-18.
     * 11-digit account number.
     */
    private String accountNumber;
    
    /**
     * Matched account profile from EBCMMC01.
     */
    private AccountProfile matchedAccount;
    
    /**
     * Sequence number assigned during processing.
     */
    private int sequenceNumber;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * Raw COBOL record (350 characters).
     */
    private String rawRecord;
    
    /**
     * Record length validation.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate statement record data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return bankCode != null &&
               currencyCode != null &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               recordLength == 350 &&
               rawRecord != null &&
               rawRecord.length() == 350;
    }
    
    /**
     * Check if record meets BCM filtering criteria.
     * 
     * @return true if meets criteria
     */
    public boolean meetsBcmCriteria() {
        return "14".equals(bankCode) && "764".equals(currencyCode);
    }
    
    /**
     * Get normalized account number (10 digits).
     * Converts 11-digit to 10-digit as per STMBDD06 logic.
     * 
     * @return Normalized account number
     */
    public String getNormalizedAccountNumber() {
        if (accountNumber == null) {
            return null;
        }
        
        String trimmed = accountNumber.trim();
        if (trimmed.length() == 11) {
            // STMBDD06 logic: positions 1-3 and 5-11
            return trimmed.substring(0, 3) + trimmed.substring(4);
        }
        return trimmed;
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Get transaction date from record.
     * 
     * @return Transaction date or null
     */
    public String getTransactionDate() {
        // Extract from specific positions based on COBOL layout
        return extractField(19, 10); // Example positions
    }
    
    /**
     * Get transaction amount from record.
     * 
     * @return Transaction amount or null
     */
    public String getTransactionAmount() {
        // Extract from specific positions based on COBOL layout
        return extractField(80, 16); // Example positions
    }
    
    /**
     * Get transaction description from record.
     * 
     * @return Transaction description or null
     */
    public String getTransactionDescription() {
        // Extract from specific positions based on COBOL layout
        return extractField(150, 40); // Example positions
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
    
    /**
     * Check if statement has matched account.
     * 
     * @return true if has matched account
     */
    public boolean hasMatchedAccount() {
        return matchedAccount != null;
    }
}
