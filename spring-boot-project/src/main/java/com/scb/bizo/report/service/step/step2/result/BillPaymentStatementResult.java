package com.scb.bizo.report.service.step.step2.result;

import com.scb.bizo.report.service.step.step2.model.GeneratedStatement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for bill payment statement generation (STMMCG02).
 * 
 * Contains the results of generating bill payment statements including:
 * - List of generated statement records
 * - Processing statistics including PromptPay counts
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillPaymentStatementResult {
    
    private boolean success;
    private String errorMessage;
    private int generatedCount;
    private int promptPayCount;
    
    @Builder.Default
    private List<GeneratedStatement> generatedStatements = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param generatedStatements List of generated statement records
     * @param generatedCount Number of statements generated
     * @param promptPayCount Number of PromptPay transactions
     * @return Successful BillPaymentStatementResult
     */
    public static BillPaymentStatementResult success(List<GeneratedStatement> generatedStatements, 
                                                   int generatedCount, int promptPayCount) {
        return BillPaymentStatementResult.builder()
            .success(true)
            .generatedStatements(generatedStatements != null ? new ArrayList<>(generatedStatements) : new ArrayList<>())
            .generatedCount(generatedCount)
            .promptPayCount(promptPayCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed BillPaymentStatementResult
     */
    public static BillPaymentStatementResult failed(String errorMessage) {
        return BillPaymentStatementResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .generatedStatements(new ArrayList<>())
            .generatedCount(0)
            .promptPayCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of generated statements.
     * 
     * @return Generated statement count
     */
    public int getGeneratedStatementCount() {
        return generatedStatements != null ? generatedStatements.size() : 0;
    }
    
    /**
     * Check if any statements were generated.
     * 
     * @return true if generated statements exist
     */
    public boolean hasGeneratedStatements() {
        return getGeneratedStatementCount() > 0;
    }
    
    /**
     * Get percentage of PromptPay transactions.
     * 
     * @return PromptPay percentage
     */
    public double getPromptPayPercentage() {
        if (generatedCount == 0) {
            return 0.0;
        }
        return (promptPayCount * 100.0) / generatedCount;
    }
}
