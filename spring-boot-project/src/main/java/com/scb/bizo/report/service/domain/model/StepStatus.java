package com.scb.bizo.report.service.domain.model;

/**
 * Enumeration representing the status of an individual processing step.
 * 
 * Each step in the 8-step workflow can be in one of these states:
 * PENDING → PROCESSING → COMPLETED (or FAILED)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public enum StepStatus {
    
    /**
     * Step is waiting to be processed.
     * This is the initial state for all steps except step 1.
     */
    PENDING("Step is waiting to be processed"),
    
    /**
     * Step is currently being processed.
     * The step processor is actively working on this step.
     */
    PROCESSING("Step is currently being processed"),
    
    /**
     * Step completed successfully.
     * All business logic executed without errors.
     */
    COMPLETED("Step completed successfully"),
    
    /**
     * Step processing failed.
     * An error occurred during step execution.
     */
    FAILED("Step processing failed"),
    
    /**
     * Step was skipped.
     * May occur if step is not applicable for this report type.
     */
    SKIPPED("Step was skipped");
    
    private final String description;
    
    StepStatus(String description) {
        this.description = description;
    }
    
    /**
     * Get human-readable description of the status.
     * 
     * @return Status description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Check if the step is in a terminal state (completed, failed, or skipped).
     * 
     * @return true if terminal state
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == SKIPPED;
    }
    
    /**
     * Check if the step is actively being processed.
     * 
     * @return true if processing
     */
    public boolean isProcessing() {
        return this == PROCESSING;
    }
    
    /**
     * Check if the step can be processed (pending state).
     * 
     * @return true if can be processed
     */
    public boolean canProcess() {
        return this == PENDING;
    }
    
    /**
     * Check if the step completed successfully.
     * 
     * @return true if successful
     */
    public boolean isSuccessful() {
        return this == COMPLETED;
    }
}
