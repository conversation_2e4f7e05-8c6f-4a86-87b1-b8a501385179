package com.scb.bizo.report.service.step.step2.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a generated statement from STMMCG02 processing.
 * 
 * This model represents the statement data generated by STMMCG02,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - PromptPay BILLER-ID support (**********)
 * - Balance calculation with credit/debit handling
 * - Sequence number management
 * - Statement type classification
 * - Channel-based processing
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeneratedStatement {
    
    /**
     * Account number for this statement.
     */
    private String accountNumber;
    
    /**
     * Sequence number for this statement.
     */
    private int sequenceNumber;
    
    /**
     * Statement type (PROMPTPAY, REGULAR, ADJUSTMENT).
     */
    private String statementType;
    
    /**
     * Channel (BPAY for bill payments).
     */
    private String channel;
    
    /**
     * Current balance after this statement.
     */
    private BigDecimal balance;
    
    /**
     * PromptPay enabled flag.
     */
    private boolean promptPayEnabled;
    
    /**
     * Original filtered statement.
     */
    private FilteredStatement originalStatement;
    
    /**
     * Generation timestamp.
     */
    @Builder.Default
    private LocalDateTime generatedAt = LocalDateTime.now();
    
    /**
     * Generation status flag.
     */
    private boolean generated;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate generated statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               sequenceNumber > 0 &&
               statementType != null &&
               balance != null;
    }
    
    /**
     * Check if statement is PromptPay enabled.
     * 
     * @return true if PromptPay enabled
     */
    public boolean isPromptPayStatement() {
        return promptPayEnabled && "PROMPTPAY".equals(statementType);
    }
    
    /**
     * Check if statement is bill payment related.
     * 
     * @return true if bill payment
     */
    public boolean isBillPaymentStatement() {
        return "BPAY".equals(channel);
    }
    
    /**
     * Get balance as formatted string.
     * 
     * @return Formatted balance
     */
    public String getFormattedBalance() {
        if (balance == null) {
            return "0.00";
        }
        return String.format("%.2f", balance);
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
