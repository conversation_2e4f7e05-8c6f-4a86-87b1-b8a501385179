package com.scb.bizo.report.service.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Value object representing report data content.
 * 
 * This class encapsulates the data that flows through the 8-step processing workflow.
 * It can contain various types of data including:
 * - Input file content
 * - Intermediate processing results
 * - Final output data
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportData {
    
    private String dataContent;
    private DataFormat format;
    private long size;
    private String checksum;
    
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Create ReportData from string content.
     * 
     * @param content The data content
     * @return ReportData instance
     */
    public static ReportData fromString(String content) {
        return ReportData.builder()
            .dataContent(content)
            .format(DataFormat.TEXT)
            .size(content != null ? content.length() : 0)
            .build();
    }
    
    /**
     * Create ReportData from map data.
     * 
     * @param data The data map
     * @return ReportData instance
     */
    public static ReportData fromMap(Map<String, Object> data) {
        return ReportData.builder()
            .metadata(new HashMap<>(data))
            .format(DataFormat.STRUCTURED)
            .size(data.size())
            .build();
    }
    
    /**
     * Get data as map for structured access.
     * 
     * @return Data as map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getDataAsMap() {
        if (format == DataFormat.STRUCTURED) {
            return new HashMap<>(metadata);
        }
        
        // Convert string content to map if needed
        Map<String, Object> result = new HashMap<>();
        if (dataContent != null) {
            result.put("content", dataContent);
        }
        result.putAll(metadata);
        return result;
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key The metadata key
     * @param value The metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key The metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
    
    /**
     * Check if data is empty.
     * 
     * @return true if empty
     */
    public boolean isEmpty() {
        return (dataContent == null || dataContent.isEmpty()) && 
               (metadata == null || metadata.isEmpty());
    }
    
    /**
     * Validate data format and content.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        if (format == null) {
            return false;
        }
        
        return switch (format) {
            case TEXT -> dataContent != null && !dataContent.trim().isEmpty();
            case BINARY -> dataContent != null;
            case STRUCTURED -> metadata != null && !metadata.isEmpty();
            case JSON -> dataContent != null && isValidJson(dataContent);
        };
    }
    
    /**
     * Transform data to target format.
     * 
     * @param targetFormat The target format
     * @return Transformed ReportData
     */
    public ReportData transform(DataFormat targetFormat) {
        if (this.format == targetFormat) {
            return this;
        }
        
        // Implementation would depend on specific transformation requirements
        // For now, return a copy with updated format
        return ReportData.builder()
            .dataContent(this.dataContent)
            .format(targetFormat)
            .size(this.size)
            .checksum(this.checksum)
            .metadata(new HashMap<>(this.metadata))
            .build();
    }
    
    private boolean isValidJson(String content) {
        // Simple JSON validation - in real implementation would use Jackson
        return content.trim().startsWith("{") && content.trim().endsWith("}") ||
               content.trim().startsWith("[") && content.trim().endsWith("]");
    }
}
