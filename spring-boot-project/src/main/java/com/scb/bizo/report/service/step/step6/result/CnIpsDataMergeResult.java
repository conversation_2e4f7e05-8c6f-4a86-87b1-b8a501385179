package com.scb.bizo.report.service.step.step6.result;

import com.scb.bizo.report.service.step.step6.model.CnIpsRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for CN/IPS data merge processing (EBCMMC08).
 * 
 * Contains the results of merging statement and CN/IPS data including:
 * - List of merged CN/IPS records
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CnIpsDataMergeResult {
    
    private boolean success;
    private String errorMessage;
    private int mergedCount;
    private int invalidCount;
    
    @Builder.Default
    private List<CnIpsRecord> mergedCnIpsData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param mergedCnIpsData List of merged CN/IPS records
     * @param mergedCount Number of records merged
     * @return Successful CnIpsDataMergeResult
     */
    public static CnIpsDataMergeResult success(List<CnIpsRecord> mergedCnIpsData, int mergedCount) {
        return CnIpsDataMergeResult.builder()
            .success(true)
            .mergedCnIpsData(mergedCnIpsData != null ? new ArrayList<>(mergedCnIpsData) : new ArrayList<>())
            .mergedCount(mergedCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed CnIpsDataMergeResult
     */
    public static CnIpsDataMergeResult failed(String errorMessage) {
        return CnIpsDataMergeResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .mergedCnIpsData(new ArrayList<>())
            .mergedCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of merged CN/IPS records.
     * 
     * @return Merged CN/IPS count
     */
    public int getMergedCnIpsCount() {
        return mergedCnIpsData != null ? mergedCnIpsData.size() : 0;
    }
    
    /**
     * Check if any CN/IPS records were merged.
     * 
     * @return true if merged CN/IPS data exists
     */
    public boolean hasMergedCnIpsData() {
        return getMergedCnIpsCount() > 0;
    }
}
