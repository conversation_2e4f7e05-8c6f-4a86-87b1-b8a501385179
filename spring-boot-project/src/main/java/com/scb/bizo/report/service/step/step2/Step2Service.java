package com.scb.bizo.report.service.step.step2;

import com.scb.bizo.report.service.application.processor.StepProcessor;
import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step2.processor.BillPaymentDataProcessor;
import com.scb.bizo.report.service.step.step2.processor.BillPaymentFilterProcessor;
import com.scb.bizo.report.service.step.step2.processor.BillPaymentStatementGenerator;
import com.scb.bizo.report.service.step.step2.result.BillPaymentDataResult;
import com.scb.bizo.report.service.step.step2.result.BillPaymentFilterResult;
import com.scb.bizo.report.service.step.step2.result.BillPaymentStatementResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Step 2 processor handling bill payment processing.
 * 
 * This service orchestrates the three sub-processes that make up Step 2:
 * 1. EBCMMC05: Bill payment data sorting and preparation
 * 2. STMBDD08: Statement filtering and account matching for bill payments
 * 3. STMMCG02: Bill payment statement generation with PromptPay support
 * 
 * Business Logic Preserved from COBOL:
 * 
 * EBCMMC05 (JCL Job):
 * - Sorts bill payment data by account number (positions 7-30)
 * - Processes 285-character records from T951.DRDBFILE
 * - Creates indexed VSAM files for bill payment details
 * - Handles file allocation and space management
 * 
 * STMBDD08 (COBOL Program):
 * - Reads statement files (285 characters) and account files (200 characters)
 * - Filters statements by account matching logic
 * - Excludes CLQ and TRQ transaction codes
 * - Processes only 'D' record types (detail records)
 * - Maintains sequence numbering for output records
 * 
 * STMMCG02 (COBOL Program):
 * - Reads account profiles with bill payment flags
 * - Processes statement files (550 characters) and detail files (285 characters)
 * - Generates bill payment statements with PromptPay BILLER-ID support (**********)
 * - Handles balance calculations and amount adjustments
 * - Creates output files for statement generation
 * 
 * Key Business Rules:
 * - Account must have ACCT-BPAY-FLG = 'Y' for bill payment processing
 * - PromptPay BILLER-ID matching between STMT-IN-DESC15 and DETL-BILLER-ID
 * - Balance calculation with credit/debit handling
 * - Sequence number management across statement records
 * - Amount reconciliation with difference adjustment records
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class Step2Service implements StepProcessor {
    
    private final BillPaymentDataProcessor billPaymentDataProcessor;        // EBCMMC05
    private final BillPaymentFilterProcessor billPaymentFilterProcessor;    // STMBDD08
    private final BillPaymentStatementGenerator billPaymentStatementGenerator; // STMMCG02
    
    @Override
    public int getStepNumber() {
        return 2;
    }
    
    @Override
    public String getStepName() {
        return "Bill Payment Processing";
    }
    
    @Override
    public List<String> getCobolPrograms() {
        return List.of("EBCMMC05+STMBDD08+STMMCG02");
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 2 processing for report: {}", report.getReportId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Validate input data from Step 1
            if (!canProcess(report, inputData)) {
                return StepResult.failed("Step 2 cannot be processed - invalid input or report state");
            }
            
            // Sub-step 1: Bill Payment Data Processing (EBCMMC05)
            log.debug("Processing bill payment data (EBCMMC05) for report: {}", report.getReportId());
            BillPaymentDataResult dataResult = billPaymentDataProcessor.processBillPaymentData(inputData);
            if (!dataResult.isSuccess()) {
                return StepResult.failed("Bill payment data processing failed: " + dataResult.getErrorMessage());
            }
            
            // Sub-step 2: Statement Filtering (STMBDD08)
            log.debug("Filtering statements (STMBDD08) for report: {}", report.getReportId());
            BillPaymentFilterResult filterResult = billPaymentFilterProcessor.filterStatements(
                dataResult.getBillPaymentData(), inputData);
            if (!filterResult.isSuccess()) {
                return StepResult.failed("Statement filtering failed: " + filterResult.getErrorMessage());
            }
            
            // Sub-step 3: Bill Payment Statement Generation (STMMCG02)
            log.debug("Generating bill payment statements (STMMCG02) for report: {}", report.getReportId());
            BillPaymentStatementResult statementResult = billPaymentStatementGenerator.generateStatements(
                filterResult.getFilteredStatements(), dataResult.getBillPaymentData());
            if (!statementResult.isSuccess()) {
                return StepResult.failed("Statement generation failed: " + statementResult.getErrorMessage());
            }
            
            // Combine all results for next step
            Map<String, Object> outputData = Map.of(
                "billPaymentData", dataResult.getBillPaymentData(),
                "filteredStatements", filterResult.getFilteredStatements(),
                "generatedStatements", statementResult.getGeneratedStatements(),
                "processingMetadata", createStep2Metadata(),
                "recordCounts", Map.of(
                    "billPaymentRecords", dataResult.getProcessedCount(),
                    "filteredStatements", filterResult.getFilteredCount(),
                    "generatedStatements", statementResult.getGeneratedCount(),
                    "promptPayTransactions", statementResult.getPromptPayCount()
                )
            );
            
            long endTime = System.currentTimeMillis();
            
            StepResult result = StepResult.success("Step 2 completed successfully", outputData);
            result.setProcessingTime(startTime, endTime);
            
            log.info("Completed Step 2 processing for report: {} in {}ms", 
                report.getReportId(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("Step 2 processing failed for report: {}", report.getReportId(), e);
            
            long endTime = System.currentTimeMillis();
            StepResult result = StepResult.failed("Step 2 processing failed: " + e.getMessage());
            result.setProcessingTime(startTime, endTime);
            
            return result;
        }
    }
    
    /**
     * Create metadata for Step 2 processing.
     * 
     * @return Metadata map
     */
    private Map<String, Object> createStep2Metadata() {
        return Map.of(
            "stepNumber", 2,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "subStepsCompleted", List.of("EBCMMC05", "STMBDD08", "STMMCG02"),
            "businessLogicPreserved", true,
            "promptPaySupport", true,
            "cobolMigration", Map.of(
                "originalPrograms", getCobolPrograms(),
                "migrationDate", LocalDateTime.now().toLocalDate(),
                "migrationVersion", "1.0.0",
                "recordFormats", Map.of(
                    "billPaymentRecords", "285 characters",
                    "statementRecords", "550 characters",
                    "accountRecords", "200 characters"
                ),
                "businessFeatures", List.of(
                    "PromptPay BILLER-ID support (**********)",
                    "Account bill payment flag validation",
                    "Balance calculation with C/D handling",
                    "Sequence number management",
                    "Amount reconciliation"
                )
            )
        );
    }
}
