package com.scb.bizo.report.service.step.step1.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step1.model.AccountProfile;
import com.scb.bizo.report.service.step.step1.model.StatementRecord;
import com.scb.bizo.report.service.step.step1.result.StatementProcessingResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Statement Processor implementing EBCMMC02 + STMBDD06 business logic.
 * 
 * This processor migrates the combined functionality of:
 * 
 * EBCMMC02 (JCL Job):
 * - Sorts statement files by multiple fields
 * - Processes both current and saved statement data
 * - Creates indexed VSAM files for statement data
 * - Handles 350-character statement records
 * 
 * STMBDD06 (COBOL Program):
 * - Reads statement and account files
 * - Filters statements by bank code '14' and currency '764'
 * - Matches statements against account profiles
 * - Adds sequence numbers and processing markers
 * - Outputs filtered statement records
 * 
 * Key Business Rules Preserved:
 * - Bank code must be '14' (position 1-2)
 * - Currency code must be '764' (position 4-6)
 * - Account number matching with ERP accounts
 * - 350-character record structure
 * - Sequential processing with detailed counters
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class StatementProcessor {
    
    // COBOL field positions for STMBDD06 (0-based in Java)
    private static final int BANK_CODE_START = 0;      // Position 1-2 in COBOL
    private static final int BANK_CODE_LENGTH = 2;
    private static final int CURRENCY_CODE_START = 3;  // Position 4-6 in COBOL
    private static final int CURRENCY_CODE_LENGTH = 3;
    private static final int ACCOUNT_NUMBER_START = 7; // Position 8-18 in COBOL
    private static final int ACCOUNT_NUMBER_LENGTH = 11;
    private static final int STATEMENT_RECORD_LENGTH = 350;
    
    // Business rule constants from STMBDD06
    private static final String REQUIRED_BANK_CODE = "14";
    private static final String REQUIRED_CURRENCY_CODE = "764";
    
    /**
     * Process statements with EBCMMC02 + STMBDD06 business logic.
     * 
     * @param inputData Input statement data
     * @param accountProfiles Account profiles from EBCMMC01
     * @return Statement processing result
     */
    public StatementProcessingResult processStatements(ReportData inputData, List<AccountProfile> accountProfiles) {
        log.info("Starting statement processing (EBCMMC02+STMBDD06)");
        
        try {
            // Extract statement records from input data
            List<String> statementRecords = extractStatementRecords(inputData);
            if (statementRecords.isEmpty()) {
                log.warn("No statement records found in input data");
                return StatementProcessingResult.success(new ArrayList<>(), 0);
            }
            
            // Create account lookup map for efficient matching
            Map<String, AccountProfile> accountLookup = createAccountLookup(accountProfiles);
            
            log.debug("Processing {} statement records against {} account profiles", 
                statementRecords.size(), accountProfiles.size());
            
            // Process each statement record with STMBDD06 business logic
            List<StatementRecord> processedStatements = new ArrayList<>();
            int inputCount = 0;
            int outputCount = 0;
            int filteredCount = 0;
            
            for (String record : statementRecords) {
                inputCount++;
                
                try {
                    // Validate record length (COBOL requirement)
                    if (record.length() != STATEMENT_RECORD_LENGTH) {
                        log.warn("Invalid statement record length: {} (expected {}), skipping", 
                            record.length(), STATEMENT_RECORD_LENGTH);
                        filteredCount++;
                        continue;
                    }
                    
                    // Extract and validate bank code (STMBDD06 logic)
                    String bankCode = extractBankCode(record);
                    if (!REQUIRED_BANK_CODE.equals(bankCode)) {
                        filteredCount++;
                        continue;
                    }
                    
                    // Extract and validate currency code (STMBDD06 logic)
                    String currencyCode = extractCurrencyCode(record);
                    if (!REQUIRED_CURRENCY_CODE.equals(currencyCode)) {
                        filteredCount++;
                        continue;
                    }
                    
                    // Extract account number for matching
                    String accountNumber = extractAccountNumber(record);
                    if (accountNumber == null || accountNumber.trim().isEmpty()) {
                        log.warn("Invalid account number in statement record, skipping");
                        filteredCount++;
                        continue;
                    }
                    
                    // Match against account profiles (STMBDD06 account matching logic)
                    String accountKey = normalizeAccountNumber(accountNumber);
                    AccountProfile matchedAccount = accountLookup.get(accountKey);
                    if (matchedAccount == null) {
                        filteredCount++;
                        continue;
                    }
                    
                    // Create processed statement record
                    StatementRecord statementRecord = createStatementRecord(record, matchedAccount, outputCount + 1);
                    processedStatements.add(statementRecord);
                    outputCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing statement record: {}", e.getMessage());
                    filteredCount++;
                }
            }
            
            // Sort statements (EBCMMC02 sorting logic)
            sortStatements(processedStatements);
            
            log.info("Statement processing completed: {} input, {} output, {} filtered", 
                inputCount, outputCount, filteredCount);
            
            return StatementProcessingResult.success(processedStatements, outputCount);
            
        } catch (Exception e) {
            log.error("Statement processing failed", e);
            return StatementProcessingResult.failed("Statement processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract statement records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractStatementRecords(ReportData inputData) {
        if (inputData.getDataContent() != null) {
            return List.of(inputData.getDataContent().split("\n"));
        }
        
        Object recordsObj = inputData.getMetadata("statementRecords");
        if (recordsObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Create account lookup map for efficient matching.
     */
    private Map<String, AccountProfile> createAccountLookup(List<AccountProfile> accountProfiles) {
        return accountProfiles.stream()
            .collect(Collectors.toMap(
                account -> normalizeAccountNumber(account.getAccountNumber()),
                account -> account,
                (existing, replacement) -> existing // Keep first if duplicates
            ));
    }
    
    /**
     * Extract bank code from statement record (positions 1-2).
     */
    private String extractBankCode(String record) {
        if (record.length() < BANK_CODE_START + BANK_CODE_LENGTH) {
            return null;
        }
        return record.substring(BANK_CODE_START, BANK_CODE_START + BANK_CODE_LENGTH);
    }
    
    /**
     * Extract currency code from statement record (positions 4-6).
     */
    private String extractCurrencyCode(String record) {
        if (record.length() < CURRENCY_CODE_START + CURRENCY_CODE_LENGTH) {
            return null;
        }
        return record.substring(CURRENCY_CODE_START, CURRENCY_CODE_START + CURRENCY_CODE_LENGTH);
    }
    
    /**
     * Extract account number from statement record (positions 8-18).
     */
    private String extractAccountNumber(String record) {
        if (record.length() < ACCOUNT_NUMBER_START + ACCOUNT_NUMBER_LENGTH) {
            return null;
        }
        return record.substring(ACCOUNT_NUMBER_START, ACCOUNT_NUMBER_START + ACCOUNT_NUMBER_LENGTH).trim();
    }
    
    /**
     * Normalize account number for matching (STMBDD06 logic).
     */
    private String normalizeAccountNumber(String accountNumber) {
        if (accountNumber == null) {
            return "";
        }
        
        // STMBDD06 converts 11-digit to 10-digit: positions 1-3 and 5-11
        String trimmed = accountNumber.trim();
        if (trimmed.length() == 11) {
            return trimmed.substring(0, 3) + trimmed.substring(4);
        }
        return trimmed;
    }
    
    /**
     * Create statement record with STMBDD06 processing logic.
     */
    private StatementRecord createStatementRecord(String rawRecord, AccountProfile account, int sequenceNumber) {
        return StatementRecord.builder()
            .rawRecord(rawRecord)
            .bankCode(extractBankCode(rawRecord))
            .currencyCode(extractCurrencyCode(rawRecord))
            .accountNumber(extractAccountNumber(rawRecord))
            .matchedAccount(account)
            .sequenceNumber(sequenceNumber)
            .processed(true)
            .recordLength(rawRecord.length())
            .build();
    }
    
    /**
     * Sort statements according to EBCMMC02 sorting criteria.
     */
    private void sortStatements(List<StatementRecord> statements) {
        // EBCMMC02 SORT FIELDS=(8,11,A,1,2,A,4,3,A,20,10,A,31,9,A,41,10,A,52,10,A,63,5,A)
        statements.sort((a, b) -> {
            // Primary sort by account number
            int result = a.getAccountNumber().compareTo(b.getAccountNumber());
            if (result != 0) return result;
            
            // Secondary sort by bank code
            result = a.getBankCode().compareTo(b.getBankCode());
            if (result != 0) return result;
            
            // Tertiary sort by currency code
            return a.getCurrencyCode().compareTo(b.getCurrencyCode());
        });
    }
}
