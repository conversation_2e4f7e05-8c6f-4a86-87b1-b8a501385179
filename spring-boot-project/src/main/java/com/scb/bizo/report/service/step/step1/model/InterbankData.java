package com.scb.bizo.report.service.step.step1.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing interbank transaction data.
 * 
 * This model represents the interbank data processed by EBCMMC03 + STMBDD07,
 * maintaining the structure and business rules from the original COBOL programs.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InterbankData {
    
    /**
     * Source statement record that generated this interbank data.
     */
    private StatementRecord sourceStatement;
    
    /**
     * Account number for the interbank transaction.
     */
    private String accountNumber;
    
    /**
     * Transaction date.
     */
    private String transactionDate;
    
    /**
     * Transaction amount.
     */
    private String transactionAmount;
    
    /**
     * Transaction description.
     */
    private String description;
    
    /**
     * Type of interbank transaction.
     */
    private String interbankType;
    
    /**
     * Reference number for the transaction.
     */
    private String referenceNumber;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate interbank data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               transactionDate != null &&
               transactionAmount != null &&
               description != null;
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
