package com.scb.bizo.report.service.step.step6.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step6.model.CnIpsRecord;
import com.scb.bizo.report.service.step.step6.result.CnIpsDataMergeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * CN/IPS Data Merge Processor implementing EBCMMC08 business logic.
 * 
 * This processor migrates the COBOL EBCMMC08 JCL job functionality:
 * 
 * Original COBOL Logic (EBCMMC08):
 * 1. Sort statement data: SORT FIELDS=(1,16,A), FORMAT=CH, EQUALS
 *    - Positions 1-16: Statement key (16 characters)
 *    - Ascending order with stable sort
 * 2. Create VSAM indexed files:
 *    - PVBBCM.BCM.BCM.PS2100.GENSTMT.DETL (KEYS(16 0))
 *    - PVBBCM.BCM.BCM.P140.DRSTMT.MCASH.WORK (KEYS(71 0))
 *    - PVBBCM.BCM.BCM.P140.CRSTMT.MCASH.WORK (KEYS(71 0))
 * 3. Process CN/IPS debit and credit files with amount field editing:
 *    - PSPBCM.IPS.BCM.DEBITFL.&DAYDATE.V1.TM2 (900-character records)
 *    - PSPBCM.IPS.BCM.CREDITFL.&DAYDATE.V1.TM2 (900-character records)
 * 4. OUTREC amount field editing with decimal point insertion:
 *    - OUTREC FIELDS=(1,46,C'.',47,2,50,155,C'.',205,2,208,81,C'.',289,2,292,13,C'.',305,2,308,13,C'.',321,2,324,226,C'.',550,2,553,19,C'.',572,2,575,326)
 *    - Insert decimal points in amount fields at specific positions
 * 5. Sort CN/IPS data: SORT FIELDS=(127,12,A,147,3,A,158,10,A,1,12,A,51,24,A,245,6,A), FORMAT=CH, EQUALS
 *    - Multiple sort keys for customer reference, product code, account, etc.
 * 6. INREC conditional processing for EWT flag handling:
 *    - WHEN=(911,3,CH,EQ,C'EWT'), OVERLAY=(13:C'EWT')
 *    - WHEN=(911,3,CH,EQ,C'   '), OVERLAY=(17:C'   ')
 * 7. Final sort by 71-character key: SORT FIELDS=(1,71,A), FORMAT=CH, EQUALS
 * 8. Create 950-character output records with 71-character keys
 * 
 * Java Implementation:
 * - Preserves exact sorting and merging logic
 * - Maintains 2100/900/950-character record structure validation
 * - Implements VSAM key indexing equivalent
 * - Provides structured output for payment processing
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class CnIpsDataMergeProcessor {
    
    // COBOL field positions (0-based in Java)
    private static final int STMT_KEY_START = 0;        // Position 1 in COBOL
    private static final int STMT_KEY_LENGTH = 16;      // 16-character statement key
    
    // CN/IPS sort key positions
    private static final int CUST_REF_START = 126;      // Position 127 in COBOL
    private static final int CUST_REF_LENGTH = 12;      // 12-character customer reference
    private static final int PROD_CODE_START = 146;     // Position 147 in COBOL
    private static final int PROD_CODE_LENGTH = 3;      // 3-character product code
    private static final int ACCT_NO_START = 157;       // Position 158 in COBOL
    private static final int ACCT_NO_LENGTH = 10;       // 10-character account number
    
    // Amount field positions for decimal editing
    private static final int TRANS_AMT_START = 33;      // Position 34 in COBOL
    private static final int TRANS_AMT_LENGTH = 16;
    private static final int DEBIT_AMT_START = 191;     // Position 192 in COBOL
    private static final int DEBIT_AMT_LENGTH = 16;
    private static final int CREDIT_AMT_START = 275;    // Position 276 in COBOL
    private static final int CREDIT_AMT_LENGTH = 16;
    private static final int NET_CREDIT_AMT_START = 291; // Position 292 in COBOL
    private static final int NET_CREDIT_AMT_LENGTH = 16;
    private static final int BENE_FEE_START = 307;      // Position 308 in COBOL
    private static final int BENE_FEE_LENGTH = 16;
    private static final int WHT_AMT_START = 536;       // Position 537 in COBOL
    private static final int WHT_AMT_LENGTH = 16;
    private static final int INV_AMT_START = 558;       // Position 559 in COBOL
    private static final int INV_AMT_LENGTH = 16;
    
    // EWT flag position
    private static final int EWT_FLAG_START = 910;      // Position 911 in COBOL
    private static final int EWT_FLAG_LENGTH = 3;
    
    // Record length constants
    private static final int STMT_RECORD_LENGTH = 2100;
    private static final int CNIPS_RECORD_LENGTH = 900;
    private static final int OUTPUT_RECORD_LENGTH = 950;
    
    /**
     * Merge CN/IPS data with EBCMMC08 business logic.
     * 
     * @param inputData Input data containing statement and CN/IPS records
     * @return CN/IPS data merge processing result
     */
    public CnIpsDataMergeResult mergeCnIpsData(ReportData inputData) {
        log.info("Starting CN/IPS data merge processing (EBCMMC08)");
        
        try {
            // Extract statement and CN/IPS records
            List<String> statementRecords = extractStatementRecords(inputData);
            List<String> debitRecords = extractDebitRecords(inputData);
            List<String> creditRecords = extractCreditRecords(inputData);
            
            if (statementRecords.isEmpty() && debitRecords.isEmpty() && creditRecords.isEmpty()) {
                log.warn("No statement or CN/IPS records found in input data");
                return CnIpsDataMergeResult.success(new ArrayList<>(), 0);
            }
            
            log.debug("Processing {} statement records, {} debit records, {} credit records", 
                statementRecords.size(), debitRecords.size(), creditRecords.size());
            
            List<CnIpsRecord> processedRecords = new ArrayList<>();
            int processedCount = 0;
            int invalidCount = 0;
            
            // Process statement records (2100 characters)
            for (String record : statementRecords) {
                try {
                    if (record.length() != STMT_RECORD_LENGTH) {
                        log.warn("Invalid statement record length: {} (expected {}), skipping record", 
                            record.length(), STMT_RECORD_LENGTH);
                        invalidCount++;
                        continue;
                    }
                    
                    CnIpsRecord cnIpsRecord = createStatementCnIpsRecord(record);
                    processedRecords.add(cnIpsRecord);
                    processedCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing statement record: {}", e.getMessage());
                    invalidCount++;
                }
            }
            
            // Process debit records (900 characters)
            for (String record : debitRecords) {
                try {
                    if (record.length() != CNIPS_RECORD_LENGTH) {
                        log.warn("Invalid debit record length: {} (expected {}), skipping record", 
                            record.length(), CNIPS_RECORD_LENGTH);
                        invalidCount++;
                        continue;
                    }
                    
                    CnIpsRecord cnIpsRecord = createDebitCnIpsRecord(record);
                    processedRecords.add(cnIpsRecord);
                    processedCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing debit record: {}", e.getMessage());
                    invalidCount++;
                }
            }
            
            // Process credit records (900 characters)
            for (String record : creditRecords) {
                try {
                    if (record.length() != CNIPS_RECORD_LENGTH) {
                        log.warn("Invalid credit record length: {} (expected {}), skipping record", 
                            record.length(), CNIPS_RECORD_LENGTH);
                        invalidCount++;
                        continue;
                    }
                    
                    CnIpsRecord cnIpsRecord = createCreditCnIpsRecord(record);
                    processedRecords.add(cnIpsRecord);
                    processedCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing credit record: {}", e.getMessage());
                    invalidCount++;
                }
            }
            
            // Sort records according to COBOL sorting logic
            sortCnIpsRecords(processedRecords);
            
            log.info("CN/IPS data merge completed: {} processed, {} invalid", 
                processedCount, invalidCount);
            
            return CnIpsDataMergeResult.success(processedRecords, processedCount);
            
        } catch (Exception e) {
            log.error("CN/IPS data merge processing failed", e);
            return CnIpsDataMergeResult.failed("CN/IPS data merge processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract statement records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractStatementRecords(ReportData inputData) {
        // Check for processed statements from Step 5
        Object stmtObj = inputData.getMetadata("processedStatements");
        if (stmtObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        // Check for GENSTMT.DETL data
        Object genstmtObj = inputData.getMetadata("genstmtDetl");
        if (genstmtObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Extract debit records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractDebitRecords(ReportData inputData) {
        Object debitObj = inputData.getMetadata("cnIpsDebitRecords");
        if (debitObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        // Check for IPS DEBITFL data
        Object ipsDebitObj = inputData.getMetadata("ipsDebitfl");
        if (ipsDebitObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Extract credit records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractCreditRecords(ReportData inputData) {
        Object creditObj = inputData.getMetadata("cnIpsCreditRecords");
        if (creditObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        // Check for IPS CREDITFL data
        Object ipsCreditObj = inputData.getMetadata("ipsCreditfl");
        if (ipsCreditObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Create CN/IPS record from statement record (2100 characters).
     */
    private CnIpsRecord createStatementCnIpsRecord(String record) {
        String statementKey = extractStatementKey(record);
        String accountNumber = extractStatementAccountNumber(record);
        
        return CnIpsRecord.builder()
            .rawRecord(record)
            .recordType("STATEMENT")
            .statementKey(statementKey)
            .accountNumber(accountNumber)
            .recordLength(record.length())
            .metadata(Map.of(
                "source", "EBCMMC08",
                "recordType", "STATEMENT",
                "processingTimestamp", System.currentTimeMillis()
            ))
            .build();
    }
    
    /**
     * Create CN/IPS record from debit record (900 characters).
     */
    private CnIpsRecord createDebitCnIpsRecord(String record) {
        String editedRecord = editAmountFields(record);
        String customerRef = extractCustomerReference(editedRecord);
        String productCode = extractProductCode(editedRecord);
        String accountNumber = extractCnIpsAccountNumber(editedRecord);
        String ewtFlag = extractEwtFlag(editedRecord);
        String formattedRecord = formatCnIpsRecord(editedRecord, ewtFlag);
        
        return CnIpsRecord.builder()
            .rawRecord(record)
            .editedRecord(editedRecord)
            .formattedRecord(formattedRecord)
            .recordType("DEBIT")
            .customerReference(customerRef)
            .productCode(productCode)
            .accountNumber(accountNumber)
            .ewtFlag(ewtFlag)
            .recordLength(OUTPUT_RECORD_LENGTH)
            .metadata(Map.of(
                "source", "EBCMMC08",
                "recordType", "DEBIT",
                "processingTimestamp", System.currentTimeMillis()
            ))
            .build();
    }
    
    /**
     * Create CN/IPS record from credit record (900 characters).
     */
    private CnIpsRecord createCreditCnIpsRecord(String record) {
        String editedRecord = editAmountFields(record);
        String customerRef = extractCustomerReference(editedRecord);
        String productCode = extractProductCode(editedRecord);
        String accountNumber = extractCnIpsAccountNumber(editedRecord);
        String ewtFlag = extractEwtFlag(editedRecord);
        String formattedRecord = formatCnIpsRecord(editedRecord, ewtFlag);
        
        return CnIpsRecord.builder()
            .rawRecord(record)
            .editedRecord(editedRecord)
            .formattedRecord(formattedRecord)
            .recordType("CREDIT")
            .customerReference(customerRef)
            .productCode(productCode)
            .accountNumber(accountNumber)
            .ewtFlag(ewtFlag)
            .recordLength(OUTPUT_RECORD_LENGTH)
            .metadata(Map.of(
                "source", "EBCMMC08",
                "recordType", "CREDIT",
                "processingTimestamp", System.currentTimeMillis()
            ))
            .build();
    }
    
    /**
     * Extract statement key from statement record (positions 1-16).
     */
    private String extractStatementKey(String record) {
        try {
            if (record.length() < STMT_KEY_START + STMT_KEY_LENGTH) {
                return "";
            }
            return record.substring(STMT_KEY_START, STMT_KEY_START + STMT_KEY_LENGTH);
        } catch (Exception e) {
            log.warn("Failed to extract statement key from record: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract account number from statement record.
     */
    private String extractStatementAccountNumber(String record) {
        try {
            // STMT-IN-ACCT-NO at positions 1-10 (0-based: 0-9)
            if (record.length() >= 10) {
                return record.substring(0, 10).trim();
            }
            return "";
        } catch (Exception e) {
            log.warn("Failed to extract account number from statement: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Edit amount fields with decimal point insertion according to OUTREC specification.
     */
    private String editAmountFields(String inputRecord) {
        StringBuilder editedRecord = new StringBuilder(inputRecord);
        
        try {
            // Insert decimal points in amount fields
            insertDecimalPoint(editedRecord, TRANS_AMT_START + 13, TRANS_AMT_LENGTH);    // Position 47
            insertDecimalPoint(editedRecord, DEBIT_AMT_START + 13, DEBIT_AMT_LENGTH);    // Position 205
            insertDecimalPoint(editedRecord, CREDIT_AMT_START + 13, CREDIT_AMT_LENGTH);  // Position 289
            insertDecimalPoint(editedRecord, NET_CREDIT_AMT_START + 13, NET_CREDIT_AMT_LENGTH); // Position 305
            insertDecimalPoint(editedRecord, BENE_FEE_START + 13, BENE_FEE_LENGTH);      // Position 321
            insertDecimalPoint(editedRecord, WHT_AMT_START + 13, WHT_AMT_LENGTH);        // Position 550
            insertDecimalPoint(editedRecord, INV_AMT_START + 13, INV_AMT_LENGTH);        // Position 572
            
        } catch (Exception e) {
            log.warn("Failed to edit amount fields: {}", e.getMessage());
        }
        
        return editedRecord.toString();
    }
    
    /**
     * Insert decimal point in amount field.
     */
    private void insertDecimalPoint(StringBuilder record, int position, int fieldLength) {
        if (position > 0 && position < record.length() && 
            position + 2 < record.length()) {
            record.setCharAt(position, '.');
        }
    }
    
    /**
     * Extract customer reference from CN/IPS record.
     */
    private String extractCustomerReference(String record) {
        try {
            if (record.length() < CUST_REF_START + CUST_REF_LENGTH) {
                return "";
            }
            return record.substring(CUST_REF_START, CUST_REF_START + CUST_REF_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract customer reference: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract product code from CN/IPS record.
     */
    private String extractProductCode(String record) {
        try {
            if (record.length() < PROD_CODE_START + PROD_CODE_LENGTH) {
                return "";
            }
            return record.substring(PROD_CODE_START, PROD_CODE_START + PROD_CODE_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract product code: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract account number from CN/IPS record.
     */
    private String extractCnIpsAccountNumber(String record) {
        try {
            if (record.length() < ACCT_NO_START + ACCT_NO_LENGTH) {
                return "";
            }
            return record.substring(ACCT_NO_START, ACCT_NO_START + ACCT_NO_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract account number from CN/IPS: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract EWT flag from CN/IPS record.
     */
    private String extractEwtFlag(String record) {
        try {
            if (record.length() < EWT_FLAG_START + EWT_FLAG_LENGTH) {
                return "";
            }
            return record.substring(EWT_FLAG_START, EWT_FLAG_START + EWT_FLAG_LENGTH);
        } catch (Exception e) {
            log.warn("Failed to extract EWT flag: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Format CN/IPS record according to INREC conditional processing.
     */
    private String formatCnIpsRecord(String inputRecord, String ewtFlag) {
        StringBuilder output = new StringBuilder(inputRecord);
        
        // INREC conditional processing for EWT flag
        if ("EWT".equals(ewtFlag)) {
            // WHEN=(911,3,CH,EQ,C'EWT'), OVERLAY=(13:C'EWT')
            if (output.length() > 12) {
                output.replace(12, 15, "EWT");
            }
        } else if ("   ".equals(ewtFlag)) {
            // WHEN=(911,3,CH,EQ,C'   '), OVERLAY=(17:C'   ')
            if (output.length() > 16) {
                output.replace(16, 19, "   ");
            }
        }
        
        // Ensure output is 950 characters
        while (output.length() < OUTPUT_RECORD_LENGTH) {
            output.append(" ");
        }
        
        if (output.length() > OUTPUT_RECORD_LENGTH) {
            output.setLength(OUTPUT_RECORD_LENGTH);
        }
        
        return output.toString();
    }
    
    /**
     * Sort CN/IPS records according to COBOL sorting logic.
     */
    private void sortCnIpsRecords(List<CnIpsRecord> records) {
        records.sort((a, b) -> {
            // Primary sort by record type (statements first)
            int result = a.getRecordType().compareTo(b.getRecordType());
            if (result != 0) return result;
            
            if ("STATEMENT".equals(a.getRecordType())) {
                // For statements: sort by statement key (positions 1-16)
                return a.getStatementKey().compareTo(b.getStatementKey());
            } else {
                // For CN/IPS records: sort by multiple fields (127,12,A,147,3,A,158,10,A,1,12,A,51,24,A,245,6,A)
                result = a.getCustomerReference().compareTo(b.getCustomerReference());
                if (result != 0) return result;
                
                result = a.getProductCode().compareTo(b.getProductCode());
                if (result != 0) return result;
                
                return a.getAccountNumber().compareTo(b.getAccountNumber());
            }
        });
    }
}
