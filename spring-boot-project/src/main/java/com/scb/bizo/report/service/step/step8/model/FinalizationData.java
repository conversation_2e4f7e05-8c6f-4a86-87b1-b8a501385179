package com.scb.bizo.report.service.step.step8.model;

import com.scb.bizo.report.service.step.step8.processor.ReportFinalizer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Domain model representing finalization data from EBCMAFTB processing.
 * 
 * This model represents the finalization data processed by EBCMAFTB,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * Key Features:
 * - Report completion status and timestamps
 * - Audit trail for compliance
 * - Processing metrics and statistics
 * - Generated file information
 * - Cleanup and archival status
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinalizationData {
    
    /**
     * Report ID for tracking.
     */
    private String reportId;
    
    /**
     * Completion timestamp.
     */
    @Builder.Default
    private LocalDateTime completedAt = LocalDateTime.now();
    
    /**
     * Audit trail for compliance.
     */
    private ReportFinalizer.AuditTrail auditTrail;
    
    /**
     * Processing metrics and statistics.
     */
    private ReportFinalizer.ProcessingMetrics processingMetrics;
    
    /**
     * List of generated files.
     */
    @Builder.Default
    private List<GeneratedFile> generatedFiles = new ArrayList<>();
    
    /**
     * Final processing status.
     */
    private String status;
    
    /**
     * Additional metadata for finalization.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate finalization data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return reportId != null &&
               !reportId.trim().isEmpty() &&
               completedAt != null &&
               status != null &&
               !status.trim().isEmpty();
    }
    
    /**
     * Check if finalization completed successfully.
     * 
     * @return true if completed successfully
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }
    
    /**
     * Check if finalization failed.
     * 
     * @return true if failed
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }
    
    /**
     * Get total number of generated files.
     * 
     * @return Generated file count
     */
    public int getGeneratedFileCount() {
        return generatedFiles != null ? generatedFiles.size() : 0;
    }
    
    /**
     * Get total file size across all generated files.
     * 
     * @return Total file size in bytes
     */
    public long getTotalFileSize() {
        if (generatedFiles == null) {
            return 0;
        }
        
        return generatedFiles.stream()
            .mapToLong(GeneratedFile::getFileSize)
            .sum();
    }
    
    /**
     * Get total records across all generated files.
     * 
     * @return Total record count
     */
    public int getTotalRecords() {
        if (generatedFiles == null) {
            return 0;
        }
        
        return generatedFiles.stream()
            .mapToInt(GeneratedFile::getRecordCount)
            .max()
            .orElse(0); // Use max since files contain same data
    }
    
    /**
     * Add generated file.
     * 
     * @param generatedFile Generated file to add
     */
    public void addGeneratedFile(GeneratedFile generatedFile) {
        if (generatedFiles == null) {
            generatedFiles = new ArrayList<>();
        }
        generatedFiles.add(generatedFile);
    }
    
    /**
     * Get generated files by type.
     * 
     * @param fileType File type to filter by
     * @return List of generated files of specified type
     */
    public List<GeneratedFile> getGeneratedFilesByType(String fileType) {
        if (generatedFiles == null) {
            return new ArrayList<>();
        }
        
        return generatedFiles.stream()
            .filter(file -> fileType.equals(file.getFileType()))
            .toList();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
