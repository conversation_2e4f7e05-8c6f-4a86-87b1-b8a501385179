package com.scb.bizo.report.service.step.step6.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a processed CN/IPS payment from STMMCG05 processing.
 * 
 * This model represents the payment data processed by STMMCG05,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - 3200-character output records with embedded CN/IPS details
 * - Product code mapping and validation (DCP → BNT)
 * - EWT pattern validation and processing
 * - Status management ('C', 'J' handling)
 * - COBOL decimal format processing
 * - Exception handling for business rule violations
 * - 8-digit cheque number support
 * - WHT (Withholding Tax) processing
 * - Multi-currency support
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessedCnIpsPayment {
    
    /**
     * Original statement record (2100 characters).
     */
    private String originalStatement;
    
    /**
     * Matched CN/IPS record (if any).
     */
    private CnIpsRecord cnIpsRecord;
    
    /**
     * Generated output record (3200 characters).
     */
    private String outputRecord;
    
    /**
     * Flag indicating if payment has CN/IPS details.
     */
    private boolean hasCnIpsDetails;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * Product code (after mapping).
     */
    private String productCode;
    
    /**
     * EWT transaction flag.
     */
    private boolean ewtTransaction;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate processed CN/IPS payment data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return originalStatement != null &&
               originalStatement.length() == 2100 &&
               outputRecord != null &&
               outputRecord.length() == 3200;
    }
    
    /**
     * Check if payment has matched CN/IPS record.
     * 
     * @return true if has CN/IPS match
     */
    public boolean hasCnIpsMatch() {
        return hasCnIpsDetails && cnIpsRecord != null;
    }
    
    /**
     * Check if payment is EWT transaction.
     * 
     * @return true if EWT transaction
     */
    public boolean isEwtPayment() {
        return ewtTransaction;
    }
    
    /**
     * Get account number from original statement.
     * 
     * @return Account number
     */
    public String getAccountNumber() {
        if (originalStatement == null || originalStatement.length() < 10) {
            return "";
        }
        return originalStatement.substring(0, 10).trim(); // STMT-KEY-IN-ACCT-NO
    }
    
    /**
     * Get customer reference from CN/IPS record.
     * 
     * @return Customer reference
     */
    public String getCustomerReference() {
        if (cnIpsRecord != null) {
            return cnIpsRecord.getCustomerReference();
        }
        return "";
    }
    
    /**
     * Extract specific field from original statement by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractStatementField(int startPos, int length) {
        if (originalStatement == null || 
            startPos < 0 || 
            startPos + length > originalStatement.length()) {
            return null;
        }
        
        return originalStatement.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from output record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOutputField(int startPos, int length) {
        if (outputRecord == null || 
            startPos < 0 || 
            startPos + length > outputRecord.length()) {
            return null;
        }
        
        return outputRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
