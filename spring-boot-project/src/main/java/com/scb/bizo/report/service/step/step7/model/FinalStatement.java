package com.scb.bizo.report.service.step.step7.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a final statement from STMMCG07 processing.
 * 
 * This model represents the final statement data processed by STMMCG07,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - 2500-character output records with embedded outward details
 * - Outward detail matching and validation
 * - Date format conversion (DD/MM/YY)
 * - Status mapping (SC→I, CC→C, default→J)
 * - Currency and amount processing
 * - Exception handling with ERP033 adjustments
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinalStatement {
    
    /**
     * Original statement record (2500 characters).
     */
    private String originalStatement;
    
    /**
     * Matched outward detail record (if any).
     */
    private String outwardDetail;
    
    /**
     * Generated output record (2500 characters).
     */
    private String outputRecord;
    
    /**
     * Statement key (16 characters).
     */
    private String statementKey;
    
    /**
     * Account number.
     */
    private String accountNumber;
    
    /**
     * Flag indicating if statement has outward details.
     */
    private boolean hasOutwardDetails;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * Adjustment record flag.
     */
    private boolean adjustmentRecord;
    
    /**
     * Mapped status (SC→I, CC→C, default→J).
     */
    private String status;
    
    /**
     * Currency code.
     */
    private String currency;
    
    /**
     * Amount value.
     */
    private String amount;
    
    /**
     * Formatted date.
     */
    private String date;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate final statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return originalStatement != null &&
               originalStatement.length() == 2500 &&
               outputRecord != null &&
               outputRecord.length() == 2500 &&
               statementKey != null &&
               statementKey.length() == 16 &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty();
    }
    
    /**
     * Check if statement has matched outward details.
     * 
     * @return true if has outward match
     */
    public boolean hasOutwardMatch() {
        return hasOutwardDetails && outwardDetail != null;
    }
    
    /**
     * Check if statement is adjustment record.
     * 
     * @return true if adjustment record
     */
    public boolean isAdjustmentStatement() {
        return adjustmentRecord;
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Get status without leading/trailing spaces.
     * 
     * @return Trimmed status
     */
    public String getStatusTrimmed() {
        return status != null ? status.trim() : null;
    }
    
    /**
     * Get currency without leading/trailing spaces.
     * 
     * @return Trimmed currency
     */
    public String getCurrencyTrimmed() {
        return currency != null ? currency.trim() : null;
    }
    
    /**
     * Extract specific field from original statement by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOriginalField(int startPos, int length) {
        if (originalStatement == null || 
            startPos < 0 || 
            startPos + length > originalStatement.length()) {
            return null;
        }
        
        return originalStatement.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from output record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOutputField(int startPos, int length) {
        if (outputRecord == null || 
            startPos < 0 || 
            startPos + length > outputRecord.length()) {
            return null;
        }
        
        return outputRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
