package com.scb.bizo.report.service.step.step2.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a filtered statement record from STMBDD08 processing.
 * 
 * This model represents the statement data filtered by STMBDD08,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - Only 'D' record types (detail records)
 * - Excludes CLQ and TRQ transaction codes
 * - Account number matching validation
 * - Sequence number management
 * - Date processing and conversion
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilteredStatement {
    
    /**
     * Account number (normalized to 10 digits).
     */
    private String accountNumber;
    
    /**
     * Sequence number for this account.
     */
    private int sequenceNumber;
    
    /**
     * Processed date in readable format.
     */
    private String processedDate;
    
    /**
     * Record type (should be 'D' for detail).
     */
    private String recordType;
    
    /**
     * Original bill payment record.
     */
    private BillPaymentRecord originalBillPayment;
    
    /**
     * Filtering status flag.
     */
    private boolean filtered;
    
    /**
     * Raw statement record.
     */
    private String rawRecord;
    
    /**
     * Record length validation.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate filtered statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               sequenceNumber > 0 &&
               recordType != null &&
               "D".equals(recordType) &&
               rawRecord != null &&
               rawRecord.length() == recordLength;
    }
    
    /**
     * Check if statement is a detail record.
     * 
     * @return true if detail record
     */
    public boolean isDetailRecord() {
        return "D".equals(recordType);
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
