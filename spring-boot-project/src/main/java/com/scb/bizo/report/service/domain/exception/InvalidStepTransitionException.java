package com.scb.bizo.report.service.domain.exception;

/**
 * Exception thrown when an invalid step transition is attempted.
 * 
 * This exception is thrown when trying to process a step that cannot
 * be processed in the current report state, such as:
 * - Processing step 2 before step 1 is completed
 * - Processing a step when the report is in FAILED status
 * - Processing step numbers outside the valid range (1-8)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class InvalidStepTransitionException extends RuntimeException {
    
    /**
     * Constructs a new InvalidStepTransitionException with the specified detail message.
     * 
     * @param message the detail message
     */
    public InvalidStepTransitionException(String message) {
        super(message);
    }
    
    /**
     * Constructs a new InvalidStepTransitionException with the specified detail message and cause.
     * 
     * @param message the detail message
     * @param cause the cause
     */
    public InvalidStepTransitionException(String message, Throwable cause) {
        super(message, cause);
    }
}
