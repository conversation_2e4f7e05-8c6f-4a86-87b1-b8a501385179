package com.scb.bizo.report.service.step.step7.result;

import com.scb.bizo.report.service.step.step7.model.FinalStatement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for outward detail processing (STMMCG07).
 * 
 * Contains the results of processing outward detail data including:
 * - List of final statements with outward details
 * - List of report files generated
 * - Processing statistics including adjustment counts
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutwardDetailResult {
    
    private boolean success;
    private String errorMessage;
    private int finalStatementCount;
    private int reportRecordCount;
    private int adjustmentCount;
    private int errorCount;
    
    @Builder.Default
    private List<FinalStatement> finalStatements = new ArrayList<>();
    
    @Builder.Default
    private List<String> reportFiles = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param finalStatements List of final statements
     * @param reportFiles List of report files
     * @param finalStatementCount Number of final statements
     * @param reportRecordCount Number of report records
     * @param adjustmentCount Number of adjustment records
     * @param errorCount Number of error records
     * @return Successful OutwardDetailResult
     */
    public static OutwardDetailResult success(List<FinalStatement> finalStatements,
                                            List<String> reportFiles,
                                            int finalStatementCount, 
                                            int reportRecordCount, 
                                            int adjustmentCount,
                                            int errorCount) {
        return OutwardDetailResult.builder()
            .success(true)
            .finalStatements(finalStatements != null ? new ArrayList<>(finalStatements) : new ArrayList<>())
            .reportFiles(reportFiles != null ? new ArrayList<>(reportFiles) : new ArrayList<>())
            .finalStatementCount(finalStatementCount)
            .reportRecordCount(reportRecordCount)
            .adjustmentCount(adjustmentCount)
            .errorCount(errorCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed OutwardDetailResult
     */
    public static OutwardDetailResult failed(String errorMessage) {
        return OutwardDetailResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .finalStatements(new ArrayList<>())
            .reportFiles(new ArrayList<>())
            .finalStatementCount(0)
            .reportRecordCount(0)
            .adjustmentCount(0)
            .errorCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of final statements.
     * 
     * @return Final statement count
     */
    public int getFinalStatementRecordCount() {
        return finalStatements != null ? finalStatements.size() : 0;
    }
    
    /**
     * Get total number of report files.
     * 
     * @return Report file count
     */
    public int getReportFileCount() {
        return reportFiles != null ? reportFiles.size() : 0;
    }
    
    /**
     * Check if any final statements were generated.
     * 
     * @return true if final statements exist
     */
    public boolean hasFinalStatements() {
        return getFinalStatementRecordCount() > 0;
    }
    
    /**
     * Check if any report files were generated.
     * 
     * @return true if report files exist
     */
    public boolean hasReportFiles() {
        return getReportFileCount() > 0;
    }
    
    /**
     * Get success rate percentage.
     * 
     * @return Success rate percentage
     */
    public double getSuccessRate() {
        if (finalStatementCount == 0) {
            return 0.0;
        }
        return ((finalStatementCount - errorCount - adjustmentCount) * 100.0) / finalStatementCount;
    }
    
    /**
     * Get outward matching rate percentage.
     * 
     * @return Outward matching rate percentage
     */
    public double getOutwardMatchingRate() {
        if (finalStatementCount == 0) {
            return 0.0;
        }
        return ((finalStatementCount - adjustmentCount) * 100.0) / finalStatementCount;
    }
    
    /**
     * Get adjustment rate percentage.
     * 
     * @return Adjustment rate percentage
     */
    public double getAdjustmentRate() {
        if (finalStatementCount == 0) {
            return 0.0;
        }
        return (adjustmentCount * 100.0) / finalStatementCount;
    }
}
