package com.scb.bizo.report.service.step.step7.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step7.model.ProcessedStatement;
import com.scb.bizo.report.service.step.step7.model.OutwardDetailRecord;
import com.scb.bizo.report.service.step.step7.model.FinalStatement;
import com.scb.bizo.report.service.step.step7.result.OutwardDetailResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Outward Detail Processor implementing STMMCG07 business logic.
 * 
 * This processor migrates the COBOL STMMCG07 program functionality:
 * 
 * Original COBOL Logic (STMMCG07):
 * 1. Read account profiles with outward flag validation (ACCT-OR-FLG = 'Y')
 * 2. Process 2516-character statement files and 1100-character outward detail files
 * 3. Outward detail matching by reference key (STMT-IN-DESC-FOR = DETL-FOR-REF-KEY)
 * 4. Generate 2500-character output records with embedded outward details
 * 5. Create 132-character report files for processing summary
 * 6. Exception handling: ERP033 adjustment for unmatched transactions
 * 7. Date format conversion: DD/MM/YY format processing
 * 8. Status mapping: SC → I, CC → C, default → J
 * 9. Currency and amount processing with COBOL decimal format
 * 10. Comprehensive outward detail field mapping
 * 
 * Key Business Rules:
 * - Account must have ACCT-OR-FLG = 'Y' for outward processing
 * - Reference key matching: STMT-IN-DESC-FOR = DETL-FOR-REF-KEY
 * - Date format conversion: DD/MM/YY to standard format
 * - Status mapping: SC → I, CC → C, default → J
 * - Currency and amount processing with COBOL decimal format
 * - Exception handling: ERP033 adjustment for unmatched transactions
 * - 2500-character output with embedded outward details
 * - 132-character report generation
 * - Comprehensive field mapping and validation
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class OutwardDetailProcessor {
    
    // COBOL field positions for statement records (0-based in Java)
    private static final int STMT_DESC_FOR_START = 2450;  // STMT-IN-DESC-FOR position
    private static final int STMT_DESC_FOR_LENGTH = 16;
    
    // COBOL field positions for outward detail records
    private static final int DETL_FOR_REF_START = 0;      // DETL-FOR-REF-KEY position
    private static final int DETL_FOR_REF_LENGTH = 16;
    private static final int DETL_STATUS_START = 16;      // DETL-STATUS position
    private static final int DETL_STATUS_LENGTH = 2;
    private static final int DETL_CURRENCY_START = 18;    // DETL-CURRENCY position
    private static final int DETL_CURRENCY_LENGTH = 3;
    private static final int DETL_AMOUNT_START = 21;      // DETL-AMOUNT position
    private static final int DETL_AMOUNT_LENGTH = 15;
    private static final int DETL_DATE_START = 36;        // DETL-DATE position
    private static final int DETL_DATE_LENGTH = 8;
    
    // Record length constants
    private static final int STMT_RECORD_LENGTH = 2500;
    private static final int OUTWARD_RECORD_LENGTH = 1100;
    private static final int OUTPUT_RECORD_LENGTH = 2500;
    private static final int REPORT_RECORD_LENGTH = 132;
    
    // Business rule constants
    private static final String OUTWARD_FLAG = "Y";
    private static final String SC_STATUS = "SC";
    private static final String CC_STATUS = "CC";
    private static final String MAPPED_SC_STATUS = "I";
    private static final String MAPPED_CC_STATUS = "C";
    private static final String DEFAULT_STATUS = "J";
    private static final String ADJUSTMENT_MESSAGE = "ERP033:Outward Adjust Txn   ";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yy");
    
    /**
     * Process outward detail with STMMCG07 business logic.
     * 
     * @param processedStatements Processed statements from STMMCREF
     * @param outwardDetailData Outward detail data from EBCMMC09
     * @param inputData Additional input data containing account profiles
     * @return Outward detail processing result
     */
    public OutwardDetailResult processOutwardDetail(List<ProcessedStatement> processedStatements,
                                                   List<OutwardDetailRecord> outwardDetailData,
                                                   ReportData inputData) {
        log.info("Starting outward detail processing (STMMCG07)");
        
        try {
            // Extract account profiles
            List<String> accountProfiles = extractAccountProfiles(inputData);
            
            if (processedStatements.isEmpty()) {
                log.warn("No processed statements found for outward detail processing");
                return OutwardDetailResult.success(new ArrayList<>(), new ArrayList<>(), 0, 0, 0, 0);
            }
            
            log.debug("Processing {} statements with {} outward details and {} account profiles", 
                processedStatements.size(), outwardDetailData.size(), accountProfiles.size());
            
            // Filter accounts with outward flag enabled
            List<String> outwardEnabledAccounts = filterOutwardEnabledAccounts(accountProfiles);
            
            List<FinalStatement> finalStatements = new ArrayList<>();
            List<String> reportFiles = new ArrayList<>();
            int finalStatementCount = 0;
            int reportRecordCount = 0;
            int adjustmentCount = 0;
            int errorCount = 0;
            
            // Process each outward-enabled account
            for (String accountProfile : outwardEnabledAccounts) {
                String accountNumber = extractAccountNumber(accountProfile);
                
                // Get statements for this account
                List<ProcessedStatement> accountStatements = getStatementsForAccount(processedStatements, accountNumber);
                
                for (ProcessedStatement statement : accountStatements) {
                    try {
                        // Process outward detail matching
                        OutwardMatchResult matchResult = processOutwardMatching(statement, outwardDetailData);
                        
                        if (matchResult.isMatched()) {
                            // Create final statement with outward details
                            FinalStatement finalStatement = createFinalStatement(
                                statement, matchResult.getMatchedOutwardDetail());
                            finalStatements.add(finalStatement);
                            
                            // Create report record
                            String reportRecord = createReportRecord(finalStatement);
                            reportFiles.add(reportRecord);
                            
                            finalStatementCount++;
                            reportRecordCount++;
                        } else {
                            // Create adjustment record for unmatched outward transaction
                            FinalStatement adjustment = createAdjustmentStatement(statement);
                            finalStatements.add(adjustment);
                            
                            adjustmentCount++;
                            finalStatementCount++;
                        }
                        
                    } catch (Exception e) {
                        log.error("Error processing outward detail for account {}: {}", accountNumber, e.getMessage());
                        errorCount++;
                    }
                }
            }
            
            log.info("Outward detail processing completed: {} final statements, {} reports, {} adjustments, {} errors", 
                finalStatementCount, reportRecordCount, adjustmentCount, errorCount);
            
            return OutwardDetailResult.success(finalStatements, reportFiles, 
                finalStatementCount, reportRecordCount, adjustmentCount, errorCount);
            
        } catch (Exception e) {
            log.error("Outward detail processing failed", e);
            return OutwardDetailResult.failed("Outward detail processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract account profiles from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractAccountProfiles(ReportData inputData) {
        Object accountObj = inputData.getMetadata("accountProfiles");
        if (accountObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        return new ArrayList<>();
    }
    
    /**
     * Filter accounts with outward flag enabled.
     */
    private List<String> filterOutwardEnabledAccounts(List<String> accountProfiles) {
        return accountProfiles.stream()
            .filter(this::isOutwardEnabled)
            .toList();
    }
    
    /**
     * Check if account has outward flag enabled (ACCT-OR-FLG = 'Y').
     */
    private boolean isOutwardEnabled(String accountProfile) {
        // Check ACCT-OR-FLG at position 156 (0-based: 155)
        if (accountProfile.length() < 156) {
            return false;
        }
        return accountProfile.charAt(155) == 'Y';
    }
    
    /**
     * Extract account number from account profile.
     */
    private String extractAccountNumber(String accountProfile) {
        if (accountProfile.length() < 26) {
            return "";
        }
        return accountProfile.substring(16, 26).trim(); // Positions 17-26 in COBOL
    }
    
    /**
     * Get statements for specific account.
     */
    private List<ProcessedStatement> getStatementsForAccount(List<ProcessedStatement> statements, String accountNumber) {
        return statements.stream()
            .filter(statement -> accountNumber.equals(statement.getAccountNumber()))
            .toList();
    }
    
    /**
     * Process outward detail matching logic.
     */
    private OutwardMatchResult processOutwardMatching(ProcessedStatement statement, List<OutwardDetailRecord> outwardDetails) {
        String stmtDescFor = extractStatementDescFor(statement.getOutputRecord());
        
        for (OutwardDetailRecord outwardDetail : outwardDetails) {
            if (isOutwardMatch(stmtDescFor, outwardDetail)) {
                return new OutwardMatchResult(true, outwardDetail);
            }
        }
        
        return new OutwardMatchResult(false, null);
    }
    
    /**
     * Extract STMT-IN-DESC-FOR from statement.
     */
    private String extractStatementDescFor(String statement) {
        try {
            if (statement.length() < STMT_DESC_FOR_START + STMT_DESC_FOR_LENGTH) {
                return "";
            }
            return statement.substring(STMT_DESC_FOR_START, STMT_DESC_FOR_START + STMT_DESC_FOR_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract statement DESC-FOR: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Check if outward detail matches statement.
     */
    private boolean isOutwardMatch(String stmtDescFor, OutwardDetailRecord outwardDetail) {
        // Match by reference key (STMT-IN-DESC-FOR = DETL-FOR-REF-KEY)
        String detlForRefKey = extractDetailForRefKey(outwardDetail.getOutputRecord());
        return stmtDescFor.equals(detlForRefKey);
    }
    
    /**
     * Extract DETL-FOR-REF-KEY from outward detail.
     */
    private String extractDetailForRefKey(String outwardDetail) {
        try {
            if (outwardDetail.length() < DETL_FOR_REF_START + DETL_FOR_REF_LENGTH) {
                return "";
            }
            return outwardDetail.substring(DETL_FOR_REF_START, DETL_FOR_REF_START + DETL_FOR_REF_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract detail FOR-REF-KEY: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Create final statement with outward details.
     */
    private FinalStatement createFinalStatement(ProcessedStatement statement, OutwardDetailRecord outwardDetail) {
        String outputRecord = createFinalOutputRecord(statement.getOutputRecord(), outwardDetail.getOutputRecord());
        String status = mapOutwardStatus(extractOutwardStatus(outwardDetail.getOutputRecord()));
        String currency = extractOutwardCurrency(outwardDetail.getOutputRecord());
        String amount = extractOutwardAmount(outwardDetail.getOutputRecord());
        String date = formatOutwardDate(extractOutwardDate(outwardDetail.getOutputRecord()));
        
        return FinalStatement.builder()
            .originalStatement(statement.getOutputRecord())
            .outwardDetail(outwardDetail.getOutputRecord())
            .outputRecord(outputRecord)
            .statementKey(statement.getStatementKey())
            .accountNumber(statement.getAccountNumber())
            .hasOutwardDetails(true)
            .processed(true)
            .status(status)
            .currency(currency)
            .amount(amount)
            .date(date)
            .build();
    }
    
    /**
     * Create adjustment statement for unmatched outward transaction.
     */
    private FinalStatement createAdjustmentStatement(ProcessedStatement statement) {
        String outputRecord = createAdjustmentOutputRecord(statement.getOutputRecord());
        
        return FinalStatement.builder()
            .originalStatement(statement.getOutputRecord())
            .outputRecord(outputRecord)
            .statementKey(statement.getStatementKey())
            .accountNumber(statement.getAccountNumber())
            .hasOutwardDetails(false)
            .processed(true)
            .adjustmentRecord(true)
            .build();
    }
    
    /**
     * Extract outward status from outward detail.
     */
    private String extractOutwardStatus(String outwardDetail) {
        try {
            if (outwardDetail.length() < DETL_STATUS_START + DETL_STATUS_LENGTH) {
                return "";
            }
            return outwardDetail.substring(DETL_STATUS_START, DETL_STATUS_START + DETL_STATUS_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract outward status: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract outward currency from outward detail.
     */
    private String extractOutwardCurrency(String outwardDetail) {
        try {
            if (outwardDetail.length() < DETL_CURRENCY_START + DETL_CURRENCY_LENGTH) {
                return "";
            }
            return outwardDetail.substring(DETL_CURRENCY_START, DETL_CURRENCY_START + DETL_CURRENCY_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract outward currency: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract outward amount from outward detail.
     */
    private String extractOutwardAmount(String outwardDetail) {
        try {
            if (outwardDetail.length() < DETL_AMOUNT_START + DETL_AMOUNT_LENGTH) {
                return "";
            }
            return outwardDetail.substring(DETL_AMOUNT_START, DETL_AMOUNT_START + DETL_AMOUNT_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract outward amount: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract outward date from outward detail.
     */
    private String extractOutwardDate(String outwardDetail) {
        try {
            if (outwardDetail.length() < DETL_DATE_START + DETL_DATE_LENGTH) {
                return "";
            }
            return outwardDetail.substring(DETL_DATE_START, DETL_DATE_START + DETL_DATE_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract outward date: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Map outward status according to STMMCG07 business rules.
     */
    private String mapOutwardStatus(String originalStatus) {
        if (SC_STATUS.equals(originalStatus)) {
            return MAPPED_SC_STATUS;
        } else if (CC_STATUS.equals(originalStatus)) {
            return MAPPED_CC_STATUS;
        } else {
            return DEFAULT_STATUS;
        }
    }
    
    /**
     * Format outward date from DD/MM/YY to standard format.
     */
    private String formatOutwardDate(String originalDate) {
        try {
            if (originalDate.length() == 8) {
                // Parse DD/MM/YY format
                LocalDate date = LocalDate.parse(originalDate, DATE_FORMATTER);
                return date.toString(); // Return in YYYY-MM-DD format
            }
            return originalDate;
        } catch (Exception e) {
            log.warn("Failed to format outward date: {}", e.getMessage());
            return originalDate;
        }
    }
    
    /**
     * Create 2500-character final output record with outward details.
     */
    private String createFinalOutputRecord(String statement, String outwardDetail) {
        // Create 2500-character record with embedded outward details
        StringBuilder output = new StringBuilder(statement);
        
        // Ensure exactly 2500 characters
        if (output.length() > OUTPUT_RECORD_LENGTH) {
            output.setLength(OUTPUT_RECORD_LENGTH);
        } else if (output.length() < OUTPUT_RECORD_LENGTH) {
            while (output.length() < OUTPUT_RECORD_LENGTH) {
                output.append(" ");
            }
        }
        
        return output.toString();
    }
    
    /**
     * Create adjustment output record.
     */
    private String createAdjustmentOutputRecord(String statement) {
        StringBuilder output = new StringBuilder(statement);
        
        // Add adjustment message at the end
        if (output.length() + ADJUSTMENT_MESSAGE.length() <= OUTPUT_RECORD_LENGTH) {
            output.append(ADJUSTMENT_MESSAGE);
        }
        
        // Pad to 2500 characters
        while (output.length() < OUTPUT_RECORD_LENGTH) {
            output.append(" ");
        }
        
        return output.toString();
    }
    
    /**
     * Create 132-character report record.
     */
    private String createReportRecord(FinalStatement finalStatement) {
        StringBuilder report = new StringBuilder();
        
        // Format report record with key information
        report.append(String.format("%-10s", finalStatement.getAccountNumber()));
        report.append(String.format("%-16s", finalStatement.getStatementKey()));
        report.append(String.format("%-3s", finalStatement.getCurrency()));
        report.append(String.format("%-15s", finalStatement.getAmount()));
        report.append(String.format("%-10s", finalStatement.getDate()));
        report.append(String.format("%-2s", finalStatement.getStatus()));
        
        // Pad to 132 characters
        while (report.length() < REPORT_RECORD_LENGTH) {
            report.append(" ");
        }
        
        if (report.length() > REPORT_RECORD_LENGTH) {
            report.setLength(REPORT_RECORD_LENGTH);
        }
        
        return report.toString();
    }
    
    /**
     * Inner class for outward match results.
     */
    private static class OutwardMatchResult {
        private final boolean matched;
        private final OutwardDetailRecord matchedOutwardDetail;
        
        public OutwardMatchResult(boolean matched, OutwardDetailRecord matchedOutwardDetail) {
            this.matched = matched;
            this.matchedOutwardDetail = matchedOutwardDetail;
        }
        
        public boolean isMatched() { return matched; }
        public OutwardDetailRecord getMatchedOutwardDetail() { return matchedOutwardDetail; }
    }
}
