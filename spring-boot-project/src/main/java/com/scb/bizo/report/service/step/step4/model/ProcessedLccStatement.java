package com.scb.bizo.report.service.step.step4.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a processed LCC statement from STMMCG04 processing.
 * 
 * This model represents the statement data processed by STMMCG04,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - 1300-character output records with embedded LCC details
 * - LCC channel filtering (BC, OTCL, AIR, ATS)
 * - Account and reference key matching logic
 * - Adjustment record generation for unmatched transactions
 * - Transaction type and status validation
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessedLccStatement {
    
    /**
     * Original statement record (700 characters).
     */
    private String originalStatement;
    
    /**
     * Matched LCC record (if any).
     */
    private LccRecord lccRecord;
    
    /**
     * Generated output record (1300 characters).
     */
    private String outputRecord;
    
    /**
     * Flag indicating if statement has LCC details.
     */
    private boolean hasLccDetails;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * Adjustment record flag.
     */
    private boolean adjustmentRecord;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate processed LCC statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return originalStatement != null &&
               originalStatement.length() == 700 &&
               outputRecord != null &&
               outputRecord.length() == 1300;
    }
    
    /**
     * Check if statement has matched LCC record.
     * 
     * @return true if has LCC match
     */
    public boolean hasLccMatch() {
        return hasLccDetails && lccRecord != null;
    }
    
    /**
     * Check if statement is LCC channel.
     * 
     * @return true if LCC channel
     */
    public boolean isLccChannel() {
        if (originalStatement == null || originalStatement.length() < 110) {
            return false;
        }
        String channel = originalStatement.substring(106, 110); // STMT-IN-CHANNEL
        return java.util.List.of("BC  ", "OTCL", "AIR ", "ATS ").contains(channel);
    }
    
    /**
     * Get account number from original statement.
     * 
     * @return Account number
     */
    public String getAccountNumber() {
        if (originalStatement == null || originalStatement.length() < 10) {
            return "";
        }
        return originalStatement.substring(0, 10).trim(); // STMT-IN-ACCT-KEY
    }
    
    /**
     * Get statement amount.
     * 
     * @return Statement amount as string
     */
    public String getStatementAmount() {
        if (originalStatement == null || originalStatement.length() < 107) {
            return "";
        }
        return originalStatement.substring(92, 107).trim(); // STMT-IN-AMT
    }
    
    /**
     * Get channel from original statement.
     * 
     * @return Channel code
     */
    public String getChannel() {
        if (originalStatement == null || originalStatement.length() < 110) {
            return "";
        }
        return originalStatement.substring(106, 110); // STMT-IN-CHANNEL
    }
    
    /**
     * Extract specific field from original statement by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractStatementField(int startPos, int length) {
        if (originalStatement == null || 
            startPos < 0 || 
            startPos + length > originalStatement.length()) {
            return null;
        }
        
        return originalStatement.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from output record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOutputField(int startPos, int length) {
        if (outputRecord == null || 
            startPos < 0 || 
            startPos + length > outputRecord.length()) {
            return null;
        }
        
        return outputRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
