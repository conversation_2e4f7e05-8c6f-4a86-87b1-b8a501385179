package com.scb.bizo.report.service.step.step1.result;

import com.scb.bizo.report.service.step.step1.model.InterbankData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for interbank processing (EBCMMC03 + STMBDD07).
 * 
 * Contains the results of processing interbank data including:
 * - List of processed interbank records
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InterbankProcessingResult {
    
    private boolean success;
    private String errorMessage;
    private int processedCount;
    
    @Builder.Default
    private List<InterbankData> interbankData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param interbankData List of processed interbank records
     * @param processedCount Number of records processed
     * @return Successful InterbankProcessingResult
     */
    public static InterbankProcessingResult success(List<InterbankData> interbankData, int processedCount) {
        return InterbankProcessingResult.builder()
            .success(true)
            .interbankData(interbankData != null ? new ArrayList<>(interbankData) : new ArrayList<>())
            .processedCount(processedCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed InterbankProcessingResult
     */
    public static InterbankProcessingResult failed(String errorMessage) {
        return InterbankProcessingResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .interbankData(new ArrayList<>())
            .processedCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of interbank records.
     * 
     * @return Interbank count
     */
    public int getInterbankCount() {
        return interbankData != null ? interbankData.size() : 0;
    }
    
    /**
     * Check if any interbank records were processed.
     * 
     * @return true if interbank records exist
     */
    public boolean hasInterbankData() {
        return getInterbankCount() > 0;
    }
}
