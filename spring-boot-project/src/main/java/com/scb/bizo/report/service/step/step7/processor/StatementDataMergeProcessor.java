package com.scb.bizo.report.service.step.step7.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step7.model.StatementRecord;
import com.scb.bizo.report.service.step.step7.model.OutwardDetailRecord;
import com.scb.bizo.report.service.step.step7.result.StatementDataMergeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Statement Data Merge Processor implementing EBCMMC09 business logic.
 * 
 * This processor migrates the COBOL EBCMMC09 JCL job functionality:
 * 
 * Original COBOL Logic (EBCMMC09):
 * 1. Sort statement data: SORT FIELDS=(7,10,A,113,6,A), FORMAT=CH, EQUALS
 *    - Positions 7-16: Account number (10 characters)
 *    - Positions 113-118: Sequence number (6 characters)
 *    - Ascending order with stable sort
 * 2. Create VSAM indexed files:
 *    - PVBBCM.BCM.BCM.PS2516.MCASH.STMT (KEYS(16 0))
 *    - PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL (KEYS(16 0))
 * 3. Process outward detail files:
 *    - PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R1ST (1038-character records)
 *    - PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R2ND (1038-character records)
 * 4. OUTREC field extraction: OUTREC FIELDS=(106,16,1,1038,46X)
 *    - Extract 16-character key from positions 106-121
 *    - Include full 1038-character record
 *    - Pad with 46 spaces to create 1100-character output
 * 5. Sort outward details: SORT FIELDS=(106,16,A), FORMAT=CH, EQUALS
 *    - Sort by 16-character reference key
 * 6. Create 2516-character statement records with 16-character keys
 * 7. Create 1100-character outward detail records with 16-character keys
 * 8. Backup file creation with date stamps
 * 
 * Java Implementation:
 * - Preserves exact sorting and merging logic
 * - Maintains 3200/2516/1038/1100-character record structure validation
 * - Implements VSAM key indexing equivalent
 * - Provides structured output for reference processing
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class StatementDataMergeProcessor {
    
    // COBOL field positions (0-based in Java)
    private static final int STMT_ACCT_START = 6;       // Position 7 in COBOL
    private static final int STMT_ACCT_LENGTH = 10;     // 10-character account number
    private static final int STMT_SEQ_START = 112;      // Position 113 in COBOL
    private static final int STMT_SEQ_LENGTH = 6;       // 6-character sequence number
    
    // Outward detail field positions
    private static final int OUTWARD_REF_START = 105;   // Position 106 in COBOL
    private static final int OUTWARD_REF_LENGTH = 16;   // 16-character reference key
    
    // Record length constants
    private static final int INPUT_STMT_RECORD_LENGTH = 3200;
    private static final int OUTPUT_STMT_RECORD_LENGTH = 2516;
    private static final int OUTWARD_INPUT_RECORD_LENGTH = 1038;
    private static final int OUTWARD_OUTPUT_RECORD_LENGTH = 1100;
    
    /**
     * Merge statement data with EBCMMC09 business logic.
     * 
     * @param inputData Input data containing statement and outward detail records
     * @return Statement data merge processing result
     */
    public StatementDataMergeResult mergeStatementData(ReportData inputData) {
        log.info("Starting statement data merge processing (EBCMMC09)");
        
        try {
            // Extract statement and outward detail records
            List<String> statementRecords = extractStatementRecords(inputData);
            List<String> outwardDetailRecords = extractOutwardDetailRecords(inputData);
            
            if (statementRecords.isEmpty() && outwardDetailRecords.isEmpty()) {
                log.warn("No statement or outward detail records found in input data");
                return StatementDataMergeResult.success(new ArrayList<>(), new ArrayList<>(), 0, 0);
            }
            
            log.debug("Processing {} statement records, {} outward detail records", 
                statementRecords.size(), outwardDetailRecords.size());
            
            List<StatementRecord> processedStatements = new ArrayList<>();
            List<OutwardDetailRecord> processedOutwardDetails = new ArrayList<>();
            int processedCount = 0;
            int outwardDetailCount = 0;
            int invalidCount = 0;
            
            // Process statement records (3200 → 2516 characters)
            for (String record : statementRecords) {
                try {
                    if (record.length() != INPUT_STMT_RECORD_LENGTH) {
                        log.warn("Invalid statement record length: {} (expected {}), skipping record", 
                            record.length(), INPUT_STMT_RECORD_LENGTH);
                        invalidCount++;
                        continue;
                    }
                    
                    StatementRecord statementRecord = createStatementRecord(record);
                    processedStatements.add(statementRecord);
                    processedCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing statement record: {}", e.getMessage());
                    invalidCount++;
                }
            }
            
            // Process outward detail records (1038 → 1100 characters)
            for (String record : outwardDetailRecords) {
                try {
                    if (record.length() != OUTWARD_INPUT_RECORD_LENGTH) {
                        log.warn("Invalid outward detail record length: {} (expected {}), skipping record", 
                            record.length(), OUTWARD_INPUT_RECORD_LENGTH);
                        invalidCount++;
                        continue;
                    }
                    
                    OutwardDetailRecord outwardRecord = createOutwardDetailRecord(record);
                    processedOutwardDetails.add(outwardRecord);
                    outwardDetailCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing outward detail record: {}", e.getMessage());
                    invalidCount++;
                }
            }
            
            // Sort records according to COBOL sorting logic
            sortStatementRecords(processedStatements);
            sortOutwardDetailRecords(processedOutwardDetails);
            
            log.info("Statement data merge completed: {} statements, {} outward details, {} invalid", 
                processedCount, outwardDetailCount, invalidCount);
            
            return StatementDataMergeResult.success(processedStatements, processedOutwardDetails, 
                processedCount, outwardDetailCount);
            
        } catch (Exception e) {
            log.error("Statement data merge processing failed", e);
            return StatementDataMergeResult.failed("Statement data merge processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract statement records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractStatementRecords(ReportData inputData) {
        // Check for processed statements from Step 6
        Object stmtObj = inputData.getMetadata("processedPayments");
        if (stmtObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        // Check for CN/IPS processed statements
        Object cnIpsObj = inputData.getMetadata("processedStatements");
        if (cnIpsObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Extract outward detail records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractOutwardDetailRecords(ReportData inputData) {
        Object outwardObj = inputData.getMetadata("outwardDetailRecords");
        if (outwardObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        // Check for FOR outward data
        Object forObj = inputData.getMetadata("forOutwardR1st");
        if (forObj instanceof List<?> list) {
            List<String> result = new ArrayList<>(list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList());
            
            // Add R2ND data if available
            Object for2ndObj = inputData.getMetadata("forOutwardR2nd");
            if (for2ndObj instanceof List<?> list2) {
                result.addAll(list2.stream()
                    .filter(String.class::isInstance)
                    .map(String.class::cast)
                    .toList());
            }
            
            return result;
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Create statement record from input record (3200 → 2516 characters).
     */
    private StatementRecord createStatementRecord(String record) {
        String accountNumber = extractAccountNumber(record);
        String sequenceNumber = extractSequenceNumber(record);
        String statementKey = createStatementKey(accountNumber, sequenceNumber);
        String outputRecord = createOutputStatementRecord(record);
        
        return StatementRecord.builder()
            .rawRecord(record)
            .outputRecord(outputRecord)
            .statementKey(statementKey)
            .accountNumber(accountNumber)
            .sequenceNumber(sequenceNumber)
            .recordLength(OUTPUT_STMT_RECORD_LENGTH)
            .metadata(Map.of(
                "source", "EBCMMC09",
                "recordType", "STATEMENT",
                "processingTimestamp", System.currentTimeMillis()
            ))
            .build();
    }
    
    /**
     * Create outward detail record from input record (1038 → 1100 characters).
     */
    private OutwardDetailRecord createOutwardDetailRecord(String record) {
        String referenceKey = extractReferenceKey(record);
        String outputRecord = createOutputOutwardRecord(record);
        
        return OutwardDetailRecord.builder()
            .rawRecord(record)
            .outputRecord(outputRecord)
            .referenceKey(referenceKey)
            .recordLength(OUTWARD_OUTPUT_RECORD_LENGTH)
            .metadata(Map.of(
                "source", "EBCMMC09",
                "recordType", "OUTWARD_DETAIL",
                "processingTimestamp", System.currentTimeMillis()
            ))
            .build();
    }
    
    /**
     * Extract account number from statement record (positions 7-16).
     */
    private String extractAccountNumber(String record) {
        try {
            if (record.length() < STMT_ACCT_START + STMT_ACCT_LENGTH) {
                return "";
            }
            return record.substring(STMT_ACCT_START, STMT_ACCT_START + STMT_ACCT_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract account number from record: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract sequence number from statement record (positions 113-118).
     */
    private String extractSequenceNumber(String record) {
        try {
            if (record.length() < STMT_SEQ_START + STMT_SEQ_LENGTH) {
                return "";
            }
            return record.substring(STMT_SEQ_START, STMT_SEQ_START + STMT_SEQ_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract sequence number from record: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Extract reference key from outward detail record (positions 106-121).
     */
    private String extractReferenceKey(String record) {
        try {
            if (record.length() < OUTWARD_REF_START + OUTWARD_REF_LENGTH) {
                return "";
            }
            return record.substring(OUTWARD_REF_START, OUTWARD_REF_START + OUTWARD_REF_LENGTH).trim();
        } catch (Exception e) {
            log.warn("Failed to extract reference key from record: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Create 16-character statement key from account and sequence.
     */
    private String createStatementKey(String accountNumber, String sequenceNumber) {
        String key = String.format("%-10s%-6s", 
            accountNumber.length() > 10 ? accountNumber.substring(0, 10) : accountNumber,
            sequenceNumber.length() > 6 ? sequenceNumber.substring(0, 6) : sequenceNumber);
        
        // Ensure exactly 16 characters
        if (key.length() < 16) {
            key = String.format("%-16s", key);
        } else if (key.length() > 16) {
            key = key.substring(0, 16);
        }
        
        return key;
    }
    
    /**
     * Create 2516-character output statement record.
     */
    private String createOutputStatementRecord(String inputRecord) {
        // Extract first 2516 characters from 3200-character input
        if (inputRecord.length() >= OUTPUT_STMT_RECORD_LENGTH) {
            return inputRecord.substring(0, OUTPUT_STMT_RECORD_LENGTH);
        } else {
            // Pad with spaces if input is shorter
            return String.format("%-" + OUTPUT_STMT_RECORD_LENGTH + "s", inputRecord);
        }
    }
    
    /**
     * Create 1100-character output outward record according to OUTREC specification.
     */
    private String createOutputOutwardRecord(String inputRecord) {
        // OUTREC FIELDS=(106,16,1,1038,46X)
        StringBuilder output = new StringBuilder();
        
        // Extract 16-character key from positions 106-121
        String referenceKey = extractReferenceKey(inputRecord);
        output.append(String.format("%-16s", referenceKey));
        
        // Include full 1038-character record
        output.append(String.format("%-1038s", inputRecord));
        
        // Pad with 46 spaces
        output.append(" ".repeat(46));
        
        // Ensure exactly 1100 characters
        String result = output.toString();
        if (result.length() > OUTWARD_OUTPUT_RECORD_LENGTH) {
            result = result.substring(0, OUTWARD_OUTPUT_RECORD_LENGTH);
        } else if (result.length() < OUTWARD_OUTPUT_RECORD_LENGTH) {
            result = String.format("%-" + OUTWARD_OUTPUT_RECORD_LENGTH + "s", result);
        }
        
        return result;
    }
    
    /**
     * Sort statement records according to COBOL sorting logic.
     */
    private void sortStatementRecords(List<StatementRecord> records) {
        records.sort((a, b) -> {
            // Sort by account number (positions 7-16), then by sequence number (positions 113-118)
            int result = a.getAccountNumber().compareTo(b.getAccountNumber());
            if (result != 0) return result;
            
            return a.getSequenceNumber().compareTo(b.getSequenceNumber());
        });
    }
    
    /**
     * Sort outward detail records according to COBOL sorting logic.
     */
    private void sortOutwardDetailRecords(List<OutwardDetailRecord> records) {
        records.sort((a, b) -> {
            // Sort by 16-character reference key (positions 106-121)
            return a.getReferenceKey().compareTo(b.getReferenceKey());
        });
    }
}
