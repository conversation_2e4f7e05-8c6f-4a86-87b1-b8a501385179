package com.scb.bizo.report.service.step.step2.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a bill payment record from EBCMMC05 processing.
 * 
 * This model represents the bill payment data processed by EBCMMC05,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * COBOL Record Structure (285 characters):
 * - Positions 1-6: Statement code
 * - Positions 7-16: Account number (10 digits)
 * - Positions 17-22: Statement date (YYMMDD)
 * - Positions 23-285: Additional bill payment data
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillPaymentRecord {
    
    /**
     * Statement code from positions 1-6.
     */
    private String statementCode;
    
    /**
     * Account number from positions 7-16.
     * 10-digit account number.
     */
    private String accountNumber;
    
    /**
     * Statement date from positions 17-22.
     * Format: YYMMDD
     */
    private String statementDate;
    
    /**
     * Sort key for COBOL sorting (positions 7-30).
     */
    private String sortKey;
    
    /**
     * Raw COBOL record (285 characters).
     */
    private String rawRecord;
    
    /**
     * Record length validation.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate bill payment record data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return statementCode != null &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               statementDate != null &&
               recordLength == 285 &&
               rawRecord != null &&
               rawRecord.length() == 285;
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
