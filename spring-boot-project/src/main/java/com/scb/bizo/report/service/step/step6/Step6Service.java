package com.scb.bizo.report.service.step.step6;

import com.scb.bizo.report.service.application.processor.StepProcessor;
import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step6.processor.CnIpsDataMergeProcessor;
import com.scb.bizo.report.service.step.step6.processor.CnIpsPaymentProcessor;
import com.scb.bizo.report.service.step.step6.result.CnIpsDataMergeResult;
import com.scb.bizo.report.service.step.step6.result.CnIpsPaymentResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Step 6 processor handling CN/IPS (Core Payment) processing.
 * 
 * This service orchestrates the two sub-processes that make up Step 6:
 * 1. EBCMMC08: CN/IPS data merging, sorting, and amount field editing
 * 2. STMMCG05: Core CN/IPS payment processing and statement generation
 * 
 * Business Logic Preserved from COBOL:
 * 
 * EBCMMC08 (JCL Job):
 * - Sorts statement data by 16-character key: SORT FIELDS=(1,16,A), FORMAT=CH, EQUALS
 * - Creates VSAM indexed files:
 *   * PVBBCM.BCM.BCM.PS2100.GENSTMT.DETL (2100-character records, 16-character key)
 *   * PVBBCM.BCM.BCM.P140.DRSTMT.MCASH.WORK (950-character records, 71-character key)
 *   * PVBBCM.BCM.BCM.P140.CRSTMT.MCASH.WORK (950-character records, 71-character key)
 * - Processes CN/IPS debit and credit files with amount field editing:
 *   * PSPBCM.IPS.BCM.DEBITFL.&DAYDATE.V1.TM2 (900-character records)
 *   * PSPBCM.IPS.BCM.CREDITFL.&DAYDATE.V1.TM2 (900-character records)
 * - OUTREC amount field editing with decimal point insertion:
 *   * Transaction Amount (34-49): Insert decimal point between positions 47-48
 *   * Debit Amount (192-207): Insert decimal point between positions 205-206
 *   * Credit Amount (276-291): Insert decimal point between positions 289-290
 *   * Net Credit Amount (292-307): Insert decimal point between positions 305-306
 *   * Bene Fee Charge (308-323): Insert decimal point between positions 321-322
 *   * Total WHT Amount (537-552): Insert decimal point between positions 550-551
 *   * Total Invoice Amount (559-574): Insert decimal point between positions 572-573
 * - Sorts CN/IPS data by multiple fields:
 *   * SORT FIELDS=(127,12,A,147,3,A,158,10,A,1,12,A,51,24,A,245,6,A), FORMAT=CH, EQUALS
 * - INREC conditional processing for EWT flag handling:
 *   * WHEN=(911,3,CH,EQ,C'EWT'), OVERLAY=(13:C'EWT')
 *   * WHEN=(911,3,CH,EQ,C'   '), OVERLAY=(17:C'   ')
 * - Final sort by 71-character key: SORT FIELDS=(1,71,A), FORMAT=CH, EQUALS
 * 
 * STMMCG05 (COBOL Program - MOST CRITICAL):
 * - Reads account profiles with CN/IPS flag validation
 * - Processes statement files (2100 characters) and CN/IPS detail files (950 characters)
 * - Matches CN/IPS transactions by account and reference keys
 * - Handles product code mapping and validation:
 *   * DCP → BNT mapping (**********)
 *   * EWT pattern validation (**********, SR-22493)
 *   * Status validation: 'C' (Cancel Before Debit), 'J' (Cancel After Debit)
 * - Generates 3200-character output records with embedded CN/IPS details
 * - Creates exception reports for unmatched transactions
 * - Manages balance calculations with credit/debit processing
 * - Handles cheque number formatting (**********):
 *   * 8-digit cheque support: PDRJNNNNNNNN, PDSTNNNNNNNN, NNNNNNNNPDRJ, NNNNNNNNPDST
 * - Processes comprehensive CN/IPS fields:
 *   * Company information, transaction dates, amounts with decimal formatting
 *   * Product codes, value dates, debit/credit accounts
 *   * Payment amounts, beneficiary details, bank information
 *   * WHT (Withholding Tax) processing, invoice details
 *   * Customer references, transaction descriptions, processing status
 *   * Fee calculations, currency codes, branch information
 * 
 * Key Business Rules:
 * - Account must have appropriate flags for CN/IPS processing
 * - Product code validation and mapping (DCP → BNT)
 * - EWT pattern validation: EWT + 2 digits
 * - Status management: 'C' and 'J' status handling based on debit timing
 * - Amount processing: COBOL decimal format with implied decimals
 * - Exception handling: Business rule violations and data errors
 * - Record structure: 3200-character output with 950-character CN/IPS detail section
 * - Balance calculation: Complex credit/debit handling with fee processing
 * - Cheque number formatting: Support for 8-digit cheque numbers
 * - WHT processing: Withholding tax calculation and reporting
 * - Multi-currency support: Currency code validation and processing
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class Step6Service implements StepProcessor {
    
    private final CnIpsDataMergeProcessor cnIpsDataMergeProcessor;        // EBCMMC08
    private final CnIpsPaymentProcessor cnIpsPaymentProcessor;           // STMMCG05 - CRITICAL
    
    @Override
    public int getStepNumber() {
        return 6;
    }
    
    @Override
    public String getStepName() {
        return "CN/IPS Core Processing";
    }
    
    @Override
    public List<String> getCobolPrograms() {
        return List.of("EBCMMC08+STMMCG05");
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 6 processing (CRITICAL - CN/IPS Core) for report: {}", report.getReportId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Validate input data from Step 5
            if (!canProcess(report, inputData)) {
                return StepResult.failed("Step 6 cannot be processed - invalid input or report state");
            }
            
            // Sub-step 1: CN/IPS Data Merging and Sorting (EBCMMC08)
            log.debug("Processing CN/IPS data merge (EBCMMC08) for report: {}", report.getReportId());
            CnIpsDataMergeResult mergeResult = cnIpsDataMergeProcessor.mergeCnIpsData(inputData);
            if (!mergeResult.isSuccess()) {
                return StepResult.failed("CN/IPS data merge failed: " + mergeResult.getErrorMessage());
            }
            
            // Sub-step 2: Core CN/IPS Payment Processing (STMMCG05) - CRITICAL
            log.debug("Processing CN/IPS payments (STMMCG05 - CRITICAL) for report: {}", report.getReportId());
            CnIpsPaymentResult paymentResult = cnIpsPaymentProcessor.processCnIpsPayments(
                mergeResult.getMergedCnIpsData(), inputData);
            if (!paymentResult.isSuccess()) {
                return StepResult.failed("CN/IPS payment processing failed: " + paymentResult.getErrorMessage());
            }
            
            // Combine all results for next step
            Map<String, Object> outputData = Map.of(
                "mergedCnIpsData", mergeResult.getMergedCnIpsData(),
                "processedPayments", paymentResult.getProcessedPayments(),
                "cnIpsDetails", paymentResult.getCnIpsDetails(),
                "exceptionReports", paymentResult.getExceptionReports(),
                "processingMetadata", createStep6Metadata(),
                "recordCounts", Map.of(
                    "mergedCnIpsRecords", mergeResult.getMergedCount(),
                    "processedPayments", paymentResult.getProcessedCount(),
                    "cnIpsDetailRecords", paymentResult.getCnIpsDetailCount(),
                    "exceptionRecords", paymentResult.getExceptionCount(),
                    "ewtTransactions", paymentResult.getEwtCount(),
                    "productCodeMappings", paymentResult.getProductCodeMappingCount(),
                    "errorRecords", paymentResult.getErrorCount()
                )
            );
            
            long endTime = System.currentTimeMillis();
            
            StepResult result = StepResult.success("Step 6 completed successfully", outputData);
            result.setProcessingTime(startTime, endTime);
            
            log.info("Completed Step 6 processing (CRITICAL) for report: {} in {}ms", 
                report.getReportId(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("Step 6 processing failed for report: {}", report.getReportId(), e);
            
            long endTime = System.currentTimeMillis();
            StepResult result = StepResult.failed("Step 6 processing failed: " + e.getMessage());
            result.setProcessingTime(startTime, endTime);
            
            return result;
        }
    }
    
    /**
     * Create metadata for Step 6 processing.
     * 
     * @return Metadata map
     */
    private Map<String, Object> createStep6Metadata() {
        return Map.of(
            "stepNumber", 6,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "subStepsCompleted", List.of("EBCMMC08", "STMMCG05"),
            "businessLogicPreserved", true,
            "cnIpsProcessingEnabled", true,
            "criticalStep", true,
            "cobolMigration", Map.of(
                "originalPrograms", getCobolPrograms(),
                "migrationDate", LocalDateTime.now().toLocalDate(),
                "migrationVersion", "1.0.0",
                "recordFormats", Map.of(
                    "statementRecords", "2100 characters",
                    "cnIpsDetailRecords", "950 characters",
                    "debitCreditRecords", "900 characters",
                    "outputRecords", "3200 characters"
                ),
                "businessFeatures", List.of(
                    "CN/IPS data merging and sorting",
                    "Amount field editing with decimal point insertion",
                    "Product code validation and mapping (DCP→BNT)",
                    "EWT pattern validation (EWT##)",
                    "Status management ('C', 'J' handling)",
                    "COBOL decimal format processing",
                    "8-digit cheque number support",
                    "WHT (Withholding Tax) processing",
                    "Multi-currency support",
                    "3200-character output with CN/IPS details",
                    "Exception report generation",
                    "Complex balance calculations"
                )
            )
        );
    }
}
