package com.scb.bizo.report.service.step.step1;

import com.scb.bizo.report.service.application.processor.StepProcessor;
import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step1.processor.AccountSetupProcessor;
import com.scb.bizo.report.service.step.step1.processor.InterbankProcessor;
import com.scb.bizo.report.service.step.step1.processor.StatementMergeProcessor;
import com.scb.bizo.report.service.step.step1.processor.StatementProcessor;
import com.scb.bizo.report.service.step.step1.result.AccountSetupResult;
import com.scb.bizo.report.service.step.step1.result.InterbankProcessingResult;
import com.scb.bizo.report.service.step.step1.result.StatementMergeResult;
import com.scb.bizo.report.service.step.step1.result.StatementProcessingResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Step 1 processor handling initial report processing.
 * 
 * This service orchestrates the four sub-processes that make up Step 1:
 * 1. EBCMMC01: Account Setup - ERP account profile processing
 * 2. EBCMMC02 + STMBDD06: Statement Processing - Statement file processing with account filtering
 * 3. EBCMMC03 + STMBDD07: Interbank Processing - Interbank statement processing
 * 4. EBCMMC04 + STMMCG01: Statement Merging - Merge interbank and historical statements
 * 
 * Business Logic Preserved:
 * - Account number sorting and filtering (EBCMMC01)
 * - Bank code '14' and currency '764' filtering (STMBDD06)
 * - 350-character statement record processing (EBCMMC02)
 * - Interbank statement description handling (EBCMMC03 + STMBDD07)
 * - Statement merging and consolidation (EBCMMC04 + STMMCG01)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class Step1Service implements StepProcessor {
    
    private final AccountSetupProcessor accountSetupProcessor;           // EBCMMC01
    private final StatementProcessor statementProcessor;                  // EBCMMC02 + STMBDD06
    private final InterbankProcessor interbankProcessor;                  // EBCMMC03 + STMBDD07
    private final StatementMergeProcessor statementMergeProcessor;        // EBCMMC04 + STMMCG01
    
    @Override
    public int getStepNumber() {
        return 1;
    }
    
    @Override
    public String getStepName() {
        return "Initial Processing";
    }
    
    @Override
    public List<String> getCobolPrograms() {
        return List.of("EBCMMC01", "EBCMMC02+STMBDD06", "EBCMMC03+STMBDD07", "EBCMMC04+STMMCG01");
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 1 processing for report: {}", report.getReportId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Validate input data
            if (!canProcess(report, inputData)) {
                return StepResult.failed("Step 1 cannot be processed - invalid input or report state");
            }
            
            // Sub-step 1: Account Setup (EBCMMC01)
            log.debug("Processing account setup (EBCMMC01) for report: {}", report.getReportId());
            AccountSetupResult accountResult = accountSetupProcessor.processAccountSetup(inputData);
            if (!accountResult.isSuccess()) {
                return StepResult.failed("Account setup failed: " + accountResult.getErrorMessage());
            }
            
            // Sub-step 2: Statement Processing (EBCMMC02 + STMBDD06)
            log.debug("Processing statements (EBCMMC02+STMBDD06) for report: {}", report.getReportId());
            StatementProcessingResult statementResult = statementProcessor.processStatements(
                inputData, accountResult.getAccountData());
            if (!statementResult.isSuccess()) {
                return StepResult.failed("Statement processing failed: " + statementResult.getErrorMessage());
            }
            
            // Sub-step 3: Interbank Processing (EBCMMC03 + STMBDD07)
            log.debug("Processing interbank data (EBCMMC03+STMBDD07) for report: {}", report.getReportId());
            InterbankProcessingResult interbankResult = interbankProcessor.processInterbank(
                statementResult.getStatementData());
            if (!interbankResult.isSuccess()) {
                return StepResult.failed("Interbank processing failed: " + interbankResult.getErrorMessage());
            }
            
            // Sub-step 4: Statement Merging (EBCMMC04 + STMMCG01)
            log.debug("Merging statements (EBCMMC04+STMMCG01) for report: {}", report.getReportId());
            StatementMergeResult mergeResult = statementMergeProcessor.mergeStatements(
                statementResult.getStatementData(), interbankResult.getInterbankData());
            if (!mergeResult.isSuccess()) {
                return StepResult.failed("Statement merging failed: " + mergeResult.getErrorMessage());
            }
            
            // Combine all results for next step
            Map<String, Object> outputData = Map.of(
                "accountData", accountResult.getAccountData(),
                "statementData", mergeResult.getMergedData(),
                "interbankData", interbankResult.getInterbankData(),
                "processingMetadata", createStep1Metadata(),
                "recordCounts", Map.of(
                    "accountRecords", accountResult.getProcessedCount(),
                    "statementRecords", statementResult.getProcessedCount(),
                    "interbankRecords", interbankResult.getProcessedCount(),
                    "mergedRecords", mergeResult.getMergedCount()
                )
            );
            
            long endTime = System.currentTimeMillis();
            
            StepResult result = StepResult.success("Step 1 completed successfully", outputData);
            result.setProcessingTime(startTime, endTime);
            
            log.info("Completed Step 1 processing for report: {} in {}ms", 
                report.getReportId(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("Step 1 processing failed for report: {}", report.getReportId(), e);
            
            long endTime = System.currentTimeMillis();
            StepResult result = StepResult.failed("Step 1 processing failed: " + e.getMessage());
            result.setProcessingTime(startTime, endTime);
            
            return result;
        }
    }
    
    /**
     * Create metadata for Step 1 processing.
     * 
     * @return Metadata map
     */
    private Map<String, Object> createStep1Metadata() {
        return Map.of(
            "stepNumber", 1,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "subStepsCompleted", List.of("EBCMMC01", "EBCMMC02+STMBDD06", "EBCMMC03+STMBDD07", "EBCMMC04+STMMCG01"),
            "businessLogicPreserved", true,
            "cobolMigration", Map.of(
                "originalPrograms", getCobolPrograms(),
                "migrationDate", LocalDateTime.now().toLocalDate(),
                "migrationVersion", "1.0.0"
            )
        );
    }
}
