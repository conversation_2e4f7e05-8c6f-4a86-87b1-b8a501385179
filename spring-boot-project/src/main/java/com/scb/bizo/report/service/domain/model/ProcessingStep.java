package com.scb.bizo.report.service.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Entity representing individual processing step within the 8-step workflow.
 * 
 * Each ProcessingStep tracks the execution of one step in the multicast report
 * processing workflow, including timing, status, results, and metadata.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingStep {
    
    private int stepNumber;
    private StepStatus status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private StepResult result;
    
    @Builder.Default
    private Map<String, Object> stepData = new HashMap<>();
    
    /**
     * Get processing duration in seconds.
     * 
     * @return Duration in seconds, or 0 if not completed
     */
    public long getProcessingDurationSeconds() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return Duration.between(startTime, endTime).getSeconds();
    }
    
    /**
     * Get processing duration in milliseconds.
     * 
     * @return Duration in milliseconds, or 0 if not completed
     */
    public long getProcessingDurationMillis() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return Duration.between(startTime, endTime).toMillis();
    }
    
    /**
     * Check if step processing is in progress.
     * 
     * @return true if in progress
     */
    public boolean isInProgress() {
        return status == StepStatus.PROCESSING;
    }
    
    /**
     * Check if step completed successfully.
     * 
     * @return true if successful
     */
    public boolean isSuccessful() {
        return status == StepStatus.COMPLETED && result != null && result.isSuccess();
    }
    
    /**
     * Check if step failed.
     * 
     * @return true if failed
     */
    public boolean isFailed() {
        return status == StepStatus.FAILED;
    }
    
    /**
     * Get step description based on step number.
     * 
     * @return Human-readable step description
     */
    public String getStepDescription() {
        return switch (stepNumber) {
            case 1 -> "Initial Processing (EBCMMC01-04)";
            case 2 -> "Bill Payment Processing (EBCMMC05)";
            case 3 -> "EPP Processing (EBCMMC06)";
            case 4 -> "LCC Processing (EBCMMC07)";
            case 5 -> "RFT Processing (EBCMMC71)";
            case 6 -> "CN/IPS Core Processing (EBCMMC08)";
            case 7 -> "Statement Generation (EBCMMC09)";
            case 8 -> "Final Output (EBCMAFTB)";
            default -> "Unknown Step";
        };
    }
    
    /**
     * Get COBOL programs processed in this step.
     * 
     * @return List of COBOL program names
     */
    public List<String> getCobolPrograms() {
        return switch (stepNumber) {
            case 1 -> List.of("EBCMMC01", "EBCMMC02+STMBDD06", "EBCMMC03+STMBDD07", "EBCMMC04+STMMCG01");
            case 2 -> List.of("EBCMMC05+STMBDD08+STMMCG02");
            case 3 -> List.of("EBCMMC06+STMMCG03");
            case 4 -> List.of("EBCMMC07+STMMCG04");
            case 5 -> List.of("EBCMMC71+STMMCG06");
            case 6 -> List.of("EBCMMC08+STMMCG05");
            case 7 -> List.of("EBCMMC09+STMMCREF+STMMCG07");
            case 8 -> List.of("EBCMAFTB");
            default -> List.of();
        };
    }
    
    /**
     * Get step priority level.
     * 
     * @return Priority level (HIGH, MEDIUM, LOW)
     */
    public String getPriority() {
        return switch (stepNumber) {
            case 1, 6 -> "HIGH";    // Foundation and core payment processing
            case 2, 7, 8 -> "MEDIUM"; // Bill payments, statements, output
            case 3, 4, 5 -> "LOW";    // EPP, LCC, RFT
            default -> "UNKNOWN";
        };
    }
    
    /**
     * Add step-specific data.
     * 
     * @param key Data key
     * @param value Data value
     */
    public void addStepData(String key, Object value) {
        if (stepData == null) {
            stepData = new HashMap<>();
        }
        stepData.put(key, value);
    }
    
    /**
     * Get step-specific data.
     * 
     * @param key Data key
     * @return Data value or null
     */
    public Object getStepData(String key) {
        return stepData != null ? stepData.get(key) : null;
    }
    
    /**
     * Mark step as started.
     */
    public void markStarted() {
        this.status = StepStatus.PROCESSING;
        this.startTime = LocalDateTime.now();
    }
    
    /**
     * Mark step as completed.
     * 
     * @param result Step result
     */
    public void markCompleted(StepResult result) {
        this.status = result.isSuccess() ? StepStatus.COMPLETED : StepStatus.FAILED;
        this.endTime = LocalDateTime.now();
        this.result = result;
    }
}
