package com.scb.bizo.report.service.step.step6.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a CN/IPS record from EBCMMC08 processing.
 * 
 * This model represents the CN/IPS data processed by EBCMMC08,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * COBOL Record Structure:
 * - Statement records: 2100-character records from GENSTMT.DETL
 * - CN/IPS debit/credit records: 900-character records from IPS files
 * - Output records: 950-character formatted records with amount editing
 * - Sort fields: Multiple sort keys for customer reference, product code, account
 * - VSAM keys: 16-character for statements, 71-character for CN/IPS records
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CnIpsRecord {
    
    /**
     * Record type (STATEMENT, DEBIT, or CREDIT).
     */
    private String recordType;
    
    /**
     * Statement key from positions 1-16 (for statement records).
     */
    private String statementKey;
    
    /**
     * Account number extracted from record.
     */
    private String accountNumber;
    
    /**
     * Customer reference from positions 127-138 (for CN/IPS records).
     */
    private String customerReference;
    
    /**
     * Product code from positions 147-149 (for CN/IPS records).
     */
    private String productCode;
    
    /**
     * EWT flag from positions 911-913 (for CN/IPS records).
     */
    private String ewtFlag;
    
    /**
     * Raw record data (2100, 900, or 950 characters).
     */
    private String rawRecord;
    
    /**
     * Edited record with decimal points inserted in amount fields.
     */
    private String editedRecord;
    
    /**
     * Formatted record with INREC conditional processing (950 characters).
     */
    private String formattedRecord;
    
    /**
     * Record length validation.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate CN/IPS record data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return recordType != null &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               rawRecord != null &&
               (recordLength == 2100 || recordLength == 900 || recordLength == 950) &&
               rawRecord.length() == (formattedRecord != null ? 
                   (recordLength == 950 ? recordLength : rawRecord.length()) : recordLength);
    }
    
    /**
     * Check if record is statement type.
     * 
     * @return true if statement record
     */
    public boolean isStatementRecord() {
        return "STATEMENT".equals(recordType);
    }
    
    /**
     * Check if record is CN/IPS debit type.
     * 
     * @return true if debit record
     */
    public boolean isDebitRecord() {
        return "DEBIT".equals(recordType);
    }
    
    /**
     * Check if record is CN/IPS credit type.
     * 
     * @return true if credit record
     */
    public boolean isCreditRecord() {
        return "CREDIT".equals(recordType);
    }
    
    /**
     * Check if record has EWT flag set.
     * 
     * @return true if EWT transaction
     */
    public boolean isEwtTransaction() {
        return "EWT".equals(ewtFlag);
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Get customer reference without leading/trailing spaces.
     * 
     * @return Trimmed customer reference
     */
    public String getCustomerReferenceTrimmed() {
        return customerReference != null ? customerReference.trim() : null;
    }
    
    /**
     * Get product code without leading/trailing spaces.
     * 
     * @return Trimmed product code
     */
    public String getProductCodeTrimmed() {
        return productCode != null ? productCode.trim() : null;
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from edited record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractEditedField(int startPos, int length) {
        if (editedRecord == null || 
            startPos < 0 || 
            startPos + length > editedRecord.length()) {
            return null;
        }
        
        return editedRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from formatted record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractFormattedField(int startPos, int length) {
        if (formattedRecord == null || 
            startPos < 0 || 
            startPos + length > formattedRecord.length()) {
            return null;
        }
        
        return formattedRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
