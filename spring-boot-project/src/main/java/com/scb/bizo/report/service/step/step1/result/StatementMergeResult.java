package com.scb.bizo.report.service.step.step1.result;

import com.scb.bizo.report.service.step.step1.model.MergedStatementData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for statement merging (EBCMMC04 + STMMCG01).
 * 
 * Contains the results of merging statement and interbank data including:
 * - List of merged statement data by account
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatementMergeResult {
    
    private boolean success;
    private String errorMessage;
    private int mergedCount;
    
    @Builder.Default
    private List<MergedStatementData> mergedData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param mergedData List of merged statement data
     * @param mergedCount Number of accounts merged
     * @return Successful StatementMergeResult
     */
    public static StatementMergeResult success(List<MergedStatementData> mergedData, int mergedCount) {
        return StatementMergeResult.builder()
            .success(true)
            .mergedData(mergedData != null ? new ArrayList<>(mergedData) : new ArrayList<>())
            .mergedCount(mergedCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed StatementMergeResult
     */
    public static StatementMergeResult failed(String errorMessage) {
        return StatementMergeResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .mergedData(new ArrayList<>())
            .mergedCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of merged accounts.
     * 
     * @return Merged account count
     */
    public int getMergedAccountCount() {
        return mergedData != null ? mergedData.size() : 0;
    }
    
    /**
     * Check if any data was merged.
     * 
     * @return true if merged data exists
     */
    public boolean hasMergedData() {
        return getMergedAccountCount() > 0;
    }
}
