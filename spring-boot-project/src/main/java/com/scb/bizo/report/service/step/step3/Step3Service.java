package com.scb.bizo.report.service.step.step3;

import com.scb.bizo.report.service.application.processor.StepProcessor;
import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step3.processor.EppDataMergeProcessor;
import com.scb.bizo.report.service.step.step3.processor.EppStatementProcessor;
import com.scb.bizo.report.service.step.step3.result.EppDataMergeResult;
import com.scb.bizo.report.service.step.step3.result.EppStatementResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Step 3 processor handling EPP (Electronic Payment Processing).
 * 
 * This service orchestrates the two sub-processes that make up Step 3:
 * 1. EBCMMC06: EPP data merging and sorting
 * 2. STMMCG03: EPP statement processing and detail generation
 * 
 * Business Logic Preserved from COBOL:
 * 
 * EBCMMC06 (JCL Job):
 * - Merges EPP and EPPD (EPP Detail) data from two sources:
 *   * PSPBCM.EPP.BCM.PS150.BILLER.PAYTRN (EPP transactions)
 *   * PSPBCM.EPPD.BCM.PS150.BILLER.PAYTRN (EPP detail transactions)
 * - Sorts by multiple fields: (1,1,A,124,10,A,64,6,A,27,16,A)
 *   * Position 1: Record type ('P' for payment)
 *   * Positions 124-133: Account number (10 digits)
 *   * Positions 64-69: Date field (6 digits)
 *   * Positions 27-42: Payment reference (16 characters)
 * - Filters records with INCLUDE COND=(1,1,CH,EQ,C'P')
 * - Creates 185-character output records with OUTREC reformatting
 * - Handles duplicate records between EPP and EPPD sources
 * 
 * STMMCG03 (COBOL Program):
 * - Reads account profiles with EBPP flag validation (ACCT-EBPP-FLG = 'Y')
 * - Processes statement files (550 characters) and detail files (185 characters)
 * - Matches EPP transactions by account and amount:
 *   * STMT-IN-ACCT-KEY = DETL-BILL-AC-KEY
 *   * STMT-IN-DR-AC6 = DETL-ACC-NO6-KEY
 *   * STMT-IN-AMT = DETL-PAID-AMT
 * - Filters EBPP channel transactions with credit code ('C ')
 * - Generates 700-character output records with embedded EPP details
 * - Handles EPP processing flag (DETL-EPP-PROCESS = 'Y')
 * - Creates error records for unmatched transactions
 * 
 * Key Business Rules:
 * - Account must have ACCT-EBPP-FLG = 'Y' for EPP processing
 * - Only process STMT-IN-CHANNEL = 'EBPP' with STMT-IN-DC-CODE = 'C '
 * - Amount matching: STMT-IN-AMT must equal DETL-PAID-AMT exactly
 * - Prevent duplicate processing: DETL-EPP-PROCESS flag management
 * - Error handling: Generate error records for unmatched EPP credits
 * - Record structure: 700-character output with 150-character EPP detail section
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class Step3Service implements StepProcessor {
    
    private final EppDataMergeProcessor eppDataMergeProcessor;        // EBCMMC06
    private final EppStatementProcessor eppStatementProcessor;       // STMMCG03
    
    @Override
    public int getStepNumber() {
        return 3;
    }
    
    @Override
    public String getStepName() {
        return "EPP Processing";
    }
    
    @Override
    public List<String> getCobolPrograms() {
        return List.of("EBCMMC06+STMMCG03");
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 3 processing for report: {}", report.getReportId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Validate input data from Step 2
            if (!canProcess(report, inputData)) {
                return StepResult.failed("Step 3 cannot be processed - invalid input or report state");
            }
            
            // Sub-step 1: EPP Data Merging and Sorting (EBCMMC06)
            log.debug("Processing EPP data merge (EBCMMC06) for report: {}", report.getReportId());
            EppDataMergeResult mergeResult = eppDataMergeProcessor.mergeEppData(inputData);
            if (!mergeResult.isSuccess()) {
                return StepResult.failed("EPP data merge failed: " + mergeResult.getErrorMessage());
            }
            
            // Sub-step 2: EPP Statement Processing (STMMCG03)
            log.debug("Processing EPP statements (STMMCG03) for report: {}", report.getReportId());
            EppStatementResult statementResult = eppStatementProcessor.processEppStatements(
                mergeResult.getMergedEppData(), inputData);
            if (!statementResult.isSuccess()) {
                return StepResult.failed("EPP statement processing failed: " + statementResult.getErrorMessage());
            }
            
            // Combine all results for next step
            Map<String, Object> outputData = Map.of(
                "mergedEppData", mergeResult.getMergedEppData(),
                "processedStatements", statementResult.getProcessedStatements(),
                "eppDetails", statementResult.getEppDetails(),
                "processingMetadata", createStep3Metadata(),
                "recordCounts", Map.of(
                    "mergedEppRecords", mergeResult.getMergedCount(),
                    "processedStatements", statementResult.getProcessedCount(),
                    "eppDetailRecords", statementResult.getEppDetailCount(),
                    "errorRecords", statementResult.getErrorCount()
                )
            );
            
            long endTime = System.currentTimeMillis();
            
            StepResult result = StepResult.success("Step 3 completed successfully", outputData);
            result.setProcessingTime(startTime, endTime);
            
            log.info("Completed Step 3 processing for report: {} in {}ms", 
                report.getReportId(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("Step 3 processing failed for report: {}", report.getReportId(), e);
            
            long endTime = System.currentTimeMillis();
            StepResult result = StepResult.failed("Step 3 processing failed: " + e.getMessage());
            result.setProcessingTime(startTime, endTime);
            
            return result;
        }
    }
    
    /**
     * Create metadata for Step 3 processing.
     * 
     * @return Metadata map
     */
    private Map<String, Object> createStep3Metadata() {
        return Map.of(
            "stepNumber", 3,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "subStepsCompleted", List.of("EBCMMC06", "STMMCG03"),
            "businessLogicPreserved", true,
            "eppProcessingEnabled", true,
            "cobolMigration", Map.of(
                "originalPrograms", getCobolPrograms(),
                "migrationDate", LocalDateTime.now().toLocalDate(),
                "migrationVersion", "1.0.0",
                "recordFormats", Map.of(
                    "eppInputRecords", "150 characters",
                    "statementRecords", "550 characters",
                    "detailRecords", "185 characters",
                    "outputRecords", "700 characters"
                ),
                "businessFeatures", List.of(
                    "EPP and EPPD data merging",
                    "Account EBPP flag validation",
                    "Amount matching validation",
                    "Duplicate processing prevention",
                    "Error record generation",
                    "700-character output with EPP details"
                )
            )
        );
    }
}
