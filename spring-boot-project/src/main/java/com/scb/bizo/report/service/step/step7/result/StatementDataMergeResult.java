package com.scb.bizo.report.service.step.step7.result;

import com.scb.bizo.report.service.step.step7.model.StatementRecord;
import com.scb.bizo.report.service.step.step7.model.OutwardDetailRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for statement data merge processing (EBCMMC09).
 * 
 * Contains the results of merging statement and outward detail data including:
 * - List of merged statement records
 * - List of outward detail records
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatementDataMergeResult {
    
    private boolean success;
    private String errorMessage;
    private int mergedCount;
    private int outwardDetailCount;
    
    @Builder.Default
    private List<StatementRecord> mergedStatementData = new ArrayList<>();
    
    @Builder.Default
    private List<OutwardDetailRecord> outwardDetailData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param mergedStatementData List of merged statement records
     * @param outwardDetailData List of outward detail records
     * @param mergedCount Number of statements merged
     * @param outwardDetailCount Number of outward details
     * @return Successful StatementDataMergeResult
     */
    public static StatementDataMergeResult success(List<StatementRecord> mergedStatementData,
                                                  List<OutwardDetailRecord> outwardDetailData,
                                                  int mergedCount, 
                                                  int outwardDetailCount) {
        return StatementDataMergeResult.builder()
            .success(true)
            .mergedStatementData(mergedStatementData != null ? new ArrayList<>(mergedStatementData) : new ArrayList<>())
            .outwardDetailData(outwardDetailData != null ? new ArrayList<>(outwardDetailData) : new ArrayList<>())
            .mergedCount(mergedCount)
            .outwardDetailCount(outwardDetailCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed StatementDataMergeResult
     */
    public static StatementDataMergeResult failed(String errorMessage) {
        return StatementDataMergeResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .mergedStatementData(new ArrayList<>())
            .outwardDetailData(new ArrayList<>())
            .mergedCount(0)
            .outwardDetailCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of merged statement records.
     * 
     * @return Merged statement count
     */
    public int getMergedStatementCount() {
        return mergedStatementData != null ? mergedStatementData.size() : 0;
    }
    
    /**
     * Get total number of outward detail records.
     * 
     * @return Outward detail count
     */
    public int getOutwardDetailRecordCount() {
        return outwardDetailData != null ? outwardDetailData.size() : 0;
    }
    
    /**
     * Check if any statement records were merged.
     * 
     * @return true if merged statement data exists
     */
    public boolean hasMergedStatementData() {
        return getMergedStatementCount() > 0;
    }
    
    /**
     * Check if any outward detail records were processed.
     * 
     * @return true if outward detail data exists
     */
    public boolean hasOutwardDetailData() {
        return getOutwardDetailRecordCount() > 0;
    }
}
