package com.scb.bizo.report.service.step.step3.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step3.model.EppRecord;
import com.scb.bizo.report.service.step.step3.result.EppDataMergeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * EPP Data Merge Processor implementing EBCMMC06 business logic.
 * 
 * This processor migrates the COBOL EBCMMC06 JCL job functionality:
 * 
 * Original COBOL Logic (EBCMMC06):
 * 1. Merge two EPP data sources:
 *    - PSPBCM.EPP.BCM.PS150.BILLER.PAYTRN (EPP transactions)
 *    - PSPBCM.EPPD.BCM.PS150.BILLER.PAYTRN (EPP detail transactions)
 * 2. Sort by multiple fields: SORT FIELDS=(1,1,A,124,10,A,64,6,A,27,16,A)
 *    - Position 1: Record type ('P' for payment)
 *    - Positions 124-133: Account number (10 digits)
 *    - Positions 64-69: Date field (6 digits)
 *    - Positions 27-42: Payment reference (16 characters)
 * 3. Filter records: INCLUDE COND=(1,1,CH,EQ,C'P')
 * 4. Reformat output: OUTREC FIELDS=(1,1,124,10,64,6,27,16,1,150,2X)
 * 5. Create 185-character output records
 * 6. Handle duplicate records between EPP and EPPD sources
 * 
 * Java Implementation:
 * - Preserves exact sorting and filtering logic
 * - Maintains 150/185-character record structure validation
 * - Implements duplicate detection and handling
 * - Provides structured output for statement processing
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class EppDataMergeProcessor {
    
    // COBOL field positions (0-based in Java)
    private static final int RECORD_TYPE_START = 0;     // Position 1 in COBOL
    private static final int RECORD_TYPE_LENGTH = 1;
    private static final int ACCOUNT_NUMBER_START = 123; // Position 124 in COBOL
    private static final int ACCOUNT_NUMBER_LENGTH = 10;
    private static final int DATE_FIELD_START = 63;     // Position 64 in COBOL
    private static final int DATE_FIELD_LENGTH = 6;
    private static final int PAY_REF_START = 26;        // Position 27 in COBOL
    private static final int PAY_REF_LENGTH = 16;
    
    // Record length constants
    private static final int INPUT_RECORD_LENGTH = 150;
    private static final int OUTPUT_RECORD_LENGTH = 185;
    
    // Business rule constants
    private static final String PAYMENT_RECORD_TYPE = "P";
    
    /**
     * Merge EPP data with EBCMMC06 business logic.
     * 
     * @param inputData Input data containing EPP and EPPD records
     * @return EPP data merge processing result
     */
    public EppDataMergeResult mergeEppData(ReportData inputData) {
        log.info("Starting EPP data merge processing (EBCMMC06)");
        
        try {
            // Extract EPP records from both sources
            List<String> eppRecords = extractEppRecords(inputData);
            List<String> eppdRecords = extractEppdRecords(inputData);
            
            if (eppRecords.isEmpty() && eppdRecords.isEmpty()) {
                log.warn("No EPP or EPPD records found in input data");
                return EppDataMergeResult.success(new ArrayList<>(), 0);
            }
            
            log.debug("Processing {} EPP records and {} EPPD records", 
                eppRecords.size(), eppdRecords.size());
            
            // Combine and process all records
            List<String> allRecords = new ArrayList<>();
            allRecords.addAll(eppRecords);
            allRecords.addAll(eppdRecords);
            
            List<EppRecord> processedRecords = new ArrayList<>();
            int processedCount = 0;
            int filteredCount = 0;
            int duplicateCount = 0;
            
            for (String record : allRecords) {
                try {
                    // Validate record length (COBOL requirement)
                    if (record.length() != INPUT_RECORD_LENGTH) {
                        log.warn("Invalid record length: {} (expected {}), skipping record", 
                            record.length(), INPUT_RECORD_LENGTH);
                        filteredCount++;
                        continue;
                    }
                    
                    // Filter by record type (INCLUDE COND=(1,1,CH,EQ,C'P'))
                    String recordType = extractRecordType(record);
                    if (!PAYMENT_RECORD_TYPE.equals(recordType)) {
                        filteredCount++;
                        continue;
                    }
                    
                    // Extract key fields for sorting and duplicate detection
                    String accountNumber = extractAccountNumber(record);
                    String dateField = extractDateField(record);
                    String paymentReference = extractPaymentReference(record);
                    
                    // Validate required fields
                    if (accountNumber == null || accountNumber.trim().isEmpty()) {
                        log.warn("Invalid account number in EPP record, skipping");
                        filteredCount++;
                        continue;
                    }
                    
                    // Check for duplicates
                    String recordKey = createRecordKey(recordType, accountNumber, dateField, paymentReference);
                    if (isDuplicateRecord(processedRecords, recordKey)) {
                        duplicateCount++;
                        continue;
                    }
                    
                    // Create EPP record with OUTREC formatting
                    EppRecord eppRecord = createEppRecord(record, recordType, accountNumber, 
                        dateField, paymentReference, recordKey);
                    processedRecords.add(eppRecord);
                    processedCount++;
                    
                } catch (Exception e) {
                    log.error("Error processing EPP record: {}", e.getMessage());
                    filteredCount++;
                }
            }
            
            // Sort records according to COBOL SORT FIELDS=(1,1,A,124,10,A,64,6,A,27,16,A)
            sortEppRecords(processedRecords);
            
            log.info("EPP data merge completed: {} processed, {} filtered, {} duplicates", 
                processedCount, filteredCount, duplicateCount);
            
            return EppDataMergeResult.success(processedRecords, processedCount);
            
        } catch (Exception e) {
            log.error("EPP data merge processing failed", e);
            return EppDataMergeResult.failed("EPP data merge processing failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract EPP records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractEppRecords(ReportData inputData) {
        Object eppObj = inputData.getMetadata("eppRecords");
        if (eppObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        // Check for EPP BILLER PAYTRN data
        Object eppBillerObj = inputData.getMetadata("eppBillerPaytrn");
        if (eppBillerObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Extract EPPD records from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractEppdRecords(ReportData inputData) {
        Object eppdObj = inputData.getMetadata("eppdRecords");
        if (eppdObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        // Check for EPPD BILLER PAYTRN data
        Object eppdBillerObj = inputData.getMetadata("eppdBillerPaytrn");
        if (eppdBillerObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Extract record type from EPP record (position 1).
     */
    private String extractRecordType(String record) {
        if (record.length() <= RECORD_TYPE_START) {
            return null;
        }
        return record.substring(RECORD_TYPE_START, RECORD_TYPE_START + RECORD_TYPE_LENGTH);
    }
    
    /**
     * Extract account number from EPP record (positions 124-133).
     */
    private String extractAccountNumber(String record) {
        if (record.length() < ACCOUNT_NUMBER_START + ACCOUNT_NUMBER_LENGTH) {
            return null;
        }
        return record.substring(ACCOUNT_NUMBER_START, ACCOUNT_NUMBER_START + ACCOUNT_NUMBER_LENGTH).trim();
    }
    
    /**
     * Extract date field from EPP record (positions 64-69).
     */
    private String extractDateField(String record) {
        if (record.length() < DATE_FIELD_START + DATE_FIELD_LENGTH) {
            return null;
        }
        return record.substring(DATE_FIELD_START, DATE_FIELD_START + DATE_FIELD_LENGTH);
    }
    
    /**
     * Extract payment reference from EPP record (positions 27-42).
     */
    private String extractPaymentReference(String record) {
        if (record.length() < PAY_REF_START + PAY_REF_LENGTH) {
            return null;
        }
        return record.substring(PAY_REF_START, PAY_REF_START + PAY_REF_LENGTH);
    }
    
    /**
     * Create unique record key for duplicate detection.
     */
    private String createRecordKey(String recordType, String accountNumber, 
                                 String dateField, String paymentReference) {
        return recordType + "|" + accountNumber + "|" + dateField + "|" + paymentReference;
    }
    
    /**
     * Check if record is duplicate.
     */
    private boolean isDuplicateRecord(List<EppRecord> processedRecords, String recordKey) {
        return processedRecords.stream()
            .anyMatch(record -> recordKey.equals(record.getRecordKey()));
    }
    
    /**
     * Create EPP record with OUTREC formatting.
     */
    private EppRecord createEppRecord(String rawRecord, String recordType, String accountNumber,
                                    String dateField, String paymentReference, String recordKey) {
        // Apply OUTREC FIELDS=(1,1,124,10,64,6,27,16,1,150,2X) formatting
        String formattedRecord = formatOutputRecord(rawRecord);
        
        return EppRecord.builder()
            .rawRecord(rawRecord)
            .formattedRecord(formattedRecord)
            .recordType(recordType)
            .accountNumber(accountNumber)
            .dateField(dateField)
            .paymentReference(paymentReference)
            .recordKey(recordKey)
            .recordLength(OUTPUT_RECORD_LENGTH)
            .metadata(Map.of(
                "source", "EBCMMC06",
                "recordType", "EPP_MERGED",
                "processingTimestamp", System.currentTimeMillis()
            ))
            .build();
    }
    
    /**
     * Format output record according to OUTREC specification.
     */
    private String formatOutputRecord(String inputRecord) {
        StringBuilder output = new StringBuilder();
        
        // OUTREC FIELDS=(1,1,124,10,64,6,27,16,1,150,2X)
        // Field 1: Position 1, length 1 (record type)
        output.append(inputRecord.substring(0, 1));
        
        // Field 2: Positions 124-133, length 10 (account number)
        output.append(inputRecord.substring(123, 133));
        
        // Field 3: Positions 64-69, length 6 (date field)
        output.append(inputRecord.substring(63, 69));
        
        // Field 4: Positions 27-42, length 16 (payment reference)
        output.append(inputRecord.substring(26, 42));
        
        // Field 5: Positions 1-150, length 150 (entire original record)
        output.append(inputRecord);
        
        // Field 6: 2X (2 spaces)
        output.append("  ");
        
        return output.toString();
    }
    
    /**
     * Sort EPP records according to COBOL SORT FIELDS=(1,1,A,124,10,A,64,6,A,27,16,A).
     */
    private void sortEppRecords(List<EppRecord> records) {
        records.sort((a, b) -> {
            // Primary sort by record type (position 1)
            int result = a.getRecordType().compareTo(b.getRecordType());
            if (result != 0) return result;
            
            // Secondary sort by account number (positions 124-133)
            result = a.getAccountNumber().compareTo(b.getAccountNumber());
            if (result != 0) return result;
            
            // Tertiary sort by date field (positions 64-69)
            result = a.getDateField().compareTo(b.getDateField());
            if (result != 0) return result;
            
            // Quaternary sort by payment reference (positions 27-42)
            return a.getPaymentReference().compareTo(b.getPaymentReference());
        });
    }
}
