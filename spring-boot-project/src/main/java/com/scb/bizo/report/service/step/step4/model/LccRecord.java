package com.scb.bizo.report.service.step.step4.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing an LCC record from EBCMMC07 processing.
 * 
 * This model represents the LCC data processed by EBCMMC07,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * COBOL Record Structure:
 * - Statement records: 700-character records from GENSTMT.DETL
 * - LCC detail records: 600-character records from BCBCMFIO
 * - Sort fields: (1,29,A) - 29-character sort key
 * - VSAM keys: 16-character for statements, 29-character for LCC details
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LccRecord {
    
    /**
     * Record type (STATEMENT or LCC_DETAIL).
     */
    private String recordType;
    
    /**
     * Sort key from positions 1-29 for COBOL sorting.
     */
    private String sortKey;
    
    /**
     * Account number extracted from record.
     */
    private String accountNumber;
    
    /**
     * Sequence number (for statement records).
     */
    private String sequenceNumber;
    
    /**
     * Project code (for LCC detail records).
     */
    private String projectCode;
    
    /**
     * Report number (for LCC detail records).
     */
    private String reportNumber;
    
    /**
     * Raw record data (700 or 600 characters).
     */
    private String rawRecord;
    
    /**
     * Record length validation.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate LCC record data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return recordType != null &&
               sortKey != null &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               rawRecord != null &&
               (recordLength == 700 || recordLength == 600) &&
               rawRecord.length() == recordLength;
    }
    
    /**
     * Check if record is statement type.
     * 
     * @return true if statement record
     */
    public boolean isStatementRecord() {
        return "STATEMENT".equals(recordType);
    }
    
    /**
     * Check if record is LCC detail type.
     * 
     * @return true if LCC detail record
     */
    public boolean isLccDetailRecord() {
        return "LCC_DETAIL".equals(recordType);
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
