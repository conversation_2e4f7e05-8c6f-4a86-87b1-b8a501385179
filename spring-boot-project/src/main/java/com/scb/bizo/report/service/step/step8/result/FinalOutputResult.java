package com.scb.bizo.report.service.step.step8.result;

import com.scb.bizo.report.service.step.step8.model.FinalOutputData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for final output processing (EBCMAFTB).
 * 
 * Contains the results of final output processing including:
 * - Final output data aggregated from all steps
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinalOutputResult {
    
    private boolean success;
    private String errorMessage;
    private int totalRecords;
    
    private FinalOutputData finalOutputData;
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param finalOutputData Final output data
     * @param totalRecords Total number of records
     * @return Successful FinalOutputResult
     */
    public static FinalOutputResult success(FinalOutputData finalOutputData, int totalRecords) {
        return FinalOutputResult.builder()
            .success(true)
            .finalOutputData(finalOutputData)
            .totalRecords(totalRecords)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed FinalOutputResult
     */
    public static FinalOutputResult failed(String errorMessage) {
        return FinalOutputResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .totalRecords(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Check if final output data exists.
     * 
     * @return true if final output data exists
     */
    public boolean hasFinalOutputData() {
        return finalOutputData != null && !finalOutputData.isEmpty();
    }
}
