package com.scb.bizo.report.service.step.step4.result;

import com.scb.bizo.report.service.step.step4.model.LccRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for LCC data merge processing (EBCMMC07).
 * 
 * Contains the results of merging statement and LCC detail data including:
 * - List of merged LCC records
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LccDataMergeResult {
    
    private boolean success;
    private String errorMessage;
    private int mergedCount;
    private int invalidCount;
    
    @Builder.Default
    private List<LccRecord> mergedLccData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param mergedLccData List of merged LCC records
     * @param mergedCount Number of records merged
     * @return Successful LccDataMergeResult
     */
    public static LccDataMergeResult success(List<LccRecord> mergedLccData, int mergedCount) {
        return LccDataMergeResult.builder()
            .success(true)
            .mergedLccData(mergedLccData != null ? new ArrayList<>(mergedLccData) : new ArrayList<>())
            .mergedCount(mergedCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed LccDataMergeResult
     */
    public static LccDataMergeResult failed(String errorMessage) {
        return LccDataMergeResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .mergedLccData(new ArrayList<>())
            .mergedCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of merged LCC records.
     * 
     * @return Merged LCC count
     */
    public int getMergedLccCount() {
        return mergedLccData != null ? mergedLccData.size() : 0;
    }
    
    /**
     * Check if any LCC records were merged.
     * 
     * @return true if merged LCC data exists
     */
    public boolean hasMergedLccData() {
        return getMergedLccCount() > 0;
    }
}
