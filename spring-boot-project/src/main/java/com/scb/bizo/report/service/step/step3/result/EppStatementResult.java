package com.scb.bizo.report.service.step.step3.result;

import com.scb.bizo.report.service.step.step3.model.EppStatement;
import com.scb.bizo.report.service.step.step3.model.ProcessedEppStatement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for EPP statement processing (STMMCG03).
 * 
 * Contains the results of processing EPP statements including:
 * - List of processed EPP statements
 * - List of EPP detail records
 * - Processing statistics including error counts
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EppStatementResult {
    
    private boolean success;
    private String errorMessage;
    private int processedCount;
    private int eppDetailCount;
    private int errorCount;
    
    @Builder.Default
    private List<ProcessedEppStatement> processedStatements = new ArrayList<>();
    
    @Builder.Default
    private List<EppStatement> eppDetails = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param processedStatements List of processed EPP statements
     * @param eppDetails List of EPP detail records
     * @param processedCount Number of statements processed
     * @param eppDetailCount Number of EPP details
     * @param errorCount Number of error records
     * @return Successful EppStatementResult
     */
    public static EppStatementResult success(List<ProcessedEppStatement> processedStatements,
                                           List<EppStatement> eppDetails,
                                           int processedCount, 
                                           int eppDetailCount, 
                                           int errorCount) {
        return EppStatementResult.builder()
            .success(true)
            .processedStatements(processedStatements != null ? new ArrayList<>(processedStatements) : new ArrayList<>())
            .eppDetails(eppDetails != null ? new ArrayList<>(eppDetails) : new ArrayList<>())
            .processedCount(processedCount)
            .eppDetailCount(eppDetailCount)
            .errorCount(errorCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed EppStatementResult
     */
    public static EppStatementResult failed(String errorMessage) {
        return EppStatementResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .processedStatements(new ArrayList<>())
            .eppDetails(new ArrayList<>())
            .processedCount(0)
            .eppDetailCount(0)
            .errorCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of processed statements.
     * 
     * @return Processed statement count
     */
    public int getProcessedStatementCount() {
        return processedStatements != null ? processedStatements.size() : 0;
    }
    
    /**
     * Get total number of EPP details.
     * 
     * @return EPP detail count
     */
    public int getEppDetailRecordCount() {
        return eppDetails != null ? eppDetails.size() : 0;
    }
    
    /**
     * Check if any statements were processed.
     * 
     * @return true if processed statements exist
     */
    public boolean hasProcessedStatements() {
        return getProcessedStatementCount() > 0;
    }
    
    /**
     * Check if any EPP details were created.
     * 
     * @return true if EPP details exist
     */
    public boolean hasEppDetails() {
        return getEppDetailRecordCount() > 0;
    }
    
    /**
     * Get success rate percentage.
     * 
     * @return Success rate percentage
     */
    public double getSuccessRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return ((processedCount - errorCount) * 100.0) / processedCount;
    }
    
    /**
     * Get EPP matching rate percentage.
     * 
     * @return EPP matching rate percentage
     */
    public double getEppMatchingRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (eppDetailCount * 100.0) / processedCount;
    }
}
