package com.scb.bizo.report.service.step.step4.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing an LCC statement detail from STMMCG04 processing.
 * 
 * This model represents the LCC detail data extracted and processed by STMMCG04,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - LCC detail record management
 * - Account and reference key matching validation
 * - Transaction type and status validation
 * - Channel-specific business rule handling
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LccStatement {
    
    /**
     * Associated LCC record.
     */
    private LccRecord lccRecord;
    
    /**
     * Account number for this LCC statement.
     */
    private String accountNumber;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate LCC statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return lccRecord != null &&
               lccRecord.isValid() &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty();
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
