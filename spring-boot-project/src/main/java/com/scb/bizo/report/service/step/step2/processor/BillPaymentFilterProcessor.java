package com.scb.bizo.report.service.step.step2.processor;

import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.step.step2.model.BillPaymentRecord;
import com.scb.bizo.report.service.step.step2.model.FilteredStatement;
import com.scb.bizo.report.service.step.step2.result.BillPaymentFilterResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Bill Payment Filter Processor implementing STMBDD08 business logic.
 * 
 * This processor migrates the COBOL STMBDD08 program functionality:
 * 
 * Original COBOL Logic (STMBDD08):
 * 1. Read statement file (285 characters) and account file (200 characters)
 * 2. Filter statements by account matching logic
 * 3. Process only 'D' record types (detail records)
 * 4. Exclude CLQ and TRQ transaction codes
 * 5. Maintain sequence numbering for output records
 * 6. Convert 11-digit account numbers to 10-digit format
 * 7. Handle date conversion from integer format
 * 
 * Java Implementation:
 * - Preserves exact filtering and matching logic
 * - Maintains record type and transaction code validation
 * - Implements sequence number management
 * - Provides account number normalization
 * 
 * Key Business Rules:
 * - Only process records with STMT-REC-T = 'D'
 * - Exclude STMT-TRN-CODE = 'CLQ' or 'TRQ'
 * - Match statements against account profiles
 * - Maintain running sequence counter per account
 * - Convert dates using COBOL DATE-OF-INTEGER function equivalent
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
@Slf4j
public class BillPaymentFilterProcessor {
    
    // COBOL field positions for statement records (0-based in Java)
    private static final int STMT_ACCT_START = 51;     // Position 52 in COBOL (STMT-ACCT)
    private static final int STMT_ACCT_LENGTH = 10;
    private static final int STMT_DATE_START = 52;     // Position 53 in COBOL (STMT-DATE)
    private static final int STMT_DATE_LENGTH = 6;
    private static final int STMT_REC_T_START = 61;    // Position 62 in COBOL (STMT-REC-T)
    private static final int STMT_TRN_CODE_START = 217; // Position 218 in COBOL (STMT-TRN-CODE)
    private static final int STMT_TRN_CODE_LENGTH = 3;
    
    // Business rule constants
    private static final String DETAIL_RECORD_TYPE = "D";
    private static final String EXCLUDED_TRN_CODE_CLQ = "CLQ";
    private static final String EXCLUDED_TRN_CODE_TRQ = "TRQ";
    
    /**
     * Filter statements with STMBDD08 business logic.
     * 
     * @param billPaymentData Bill payment data from EBCMMC05
     * @param inputData Additional input data containing account profiles
     * @return Statement filtering result
     */
    public BillPaymentFilterResult filterStatements(List<BillPaymentRecord> billPaymentData, 
                                                   ReportData inputData) {
        log.info("Starting statement filtering (STMBDD08)");
        
        try {
            if (billPaymentData == null || billPaymentData.isEmpty()) {
                log.warn("No bill payment data provided for filtering");
                return BillPaymentFilterResult.success(new ArrayList<>(), 0);
            }
            
            // Extract account profiles for matching
            List<String> accountProfiles = extractAccountProfiles(inputData);
            Map<String, String> accountLookup = createAccountLookup(accountProfiles);
            
            log.debug("Filtering {} bill payment records against {} account profiles", 
                billPaymentData.size(), accountProfiles.size());
            
            List<FilteredStatement> filteredStatements = new ArrayList<>();
            int inputCount = 0;
            int outputCount = 0;
            int filteredCount = 0;
            Map<String, Integer> sequenceCounters = new java.util.HashMap<>();
            
            for (BillPaymentRecord billPayment : billPaymentData) {
                inputCount++;
                
                try {
                    String rawRecord = billPayment.getRawRecord();
                    
                    // Extract and validate record type (STMBDD08 logic)
                    String recordType = extractRecordType(rawRecord);
                    if (!DETAIL_RECORD_TYPE.equals(recordType)) {
                        filteredCount++;
                        continue;
                    }
                    
                    // Extract and validate transaction code (STMBDD08 logic)
                    String transactionCode = extractTransactionCode(rawRecord);
                    if (EXCLUDED_TRN_CODE_CLQ.equals(transactionCode) || 
                        EXCLUDED_TRN_CODE_TRQ.equals(transactionCode)) {
                        filteredCount++;
                        continue;
                    }
                    
                    // Extract account number for matching
                    String accountNumber = extractStatementAccountNumber(rawRecord);
                    if (accountNumber == null || accountNumber.trim().isEmpty()) {
                        log.warn("Invalid account number in statement record, skipping");
                        filteredCount++;
                        continue;
                    }
                    
                    // Normalize account number (11-digit to 10-digit conversion)
                    String normalizedAccount = normalizeAccountNumber(accountNumber);
                    
                    // Match against account profiles (STMBDD08 account matching logic)
                    if (!accountLookup.containsKey(normalizedAccount)) {
                        filteredCount++;
                        continue;
                    }
                    
                    // Generate sequence number for this account
                    int sequenceNumber = sequenceCounters.merge(normalizedAccount, 1, Integer::sum);
                    
                    // Process date conversion (STMBDD08 date handling)
                    String processedDate = processStatementDate(rawRecord);
                    
                    // Create filtered statement record
                    FilteredStatement filteredStatement = createFilteredStatement(
                        rawRecord, billPayment, normalizedAccount, sequenceNumber, processedDate);
                    filteredStatements.add(filteredStatement);
                    outputCount++;
                    
                } catch (Exception e) {
                    log.error("Error filtering statement record: {}", e.getMessage());
                    filteredCount++;
                }
            }
            
            log.info("Statement filtering completed: {} input, {} output, {} filtered", 
                inputCount, outputCount, filteredCount);
            
            return BillPaymentFilterResult.success(filteredStatements, outputCount);
            
        } catch (Exception e) {
            log.error("Statement filtering failed", e);
            return BillPaymentFilterResult.failed("Statement filtering failed: " + e.getMessage());
        }
    }
    
    /**
     * Extract account profiles from input data.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractAccountProfiles(ReportData inputData) {
        // Check for account data from Step 1
        Object accountDataObj = inputData.getMetadata("accountData");
        if (accountDataObj instanceof List<?> list) {
            return list.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toList();
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Create account lookup map for efficient matching.
     */
    private Map<String, String> createAccountLookup(List<String> accountProfiles) {
        return accountProfiles.stream()
            .filter(profile -> profile.length() >= 26) // Ensure minimum length for account number
            .collect(Collectors.toMap(
                profile -> profile.substring(16, 26).trim(), // Account number at positions 17-26
                profile -> profile,
                (existing, replacement) -> existing // Keep first if duplicates
            ));
    }
    
    /**
     * Extract record type from statement record.
     */
    private String extractRecordType(String record) {
        if (record.length() <= STMT_REC_T_START) {
            return null;
        }
        return String.valueOf(record.charAt(STMT_REC_T_START));
    }
    
    /**
     * Extract transaction code from statement record.
     */
    private String extractTransactionCode(String record) {
        if (record.length() < STMT_TRN_CODE_START + STMT_TRN_CODE_LENGTH) {
            return null;
        }
        return record.substring(STMT_TRN_CODE_START, STMT_TRN_CODE_START + STMT_TRN_CODE_LENGTH);
    }
    
    /**
     * Extract account number from statement record.
     */
    private String extractStatementAccountNumber(String record) {
        if (record.length() < STMT_ACCT_START + STMT_ACCT_LENGTH) {
            return null;
        }
        return record.substring(STMT_ACCT_START, STMT_ACCT_START + STMT_ACCT_LENGTH).trim();
    }
    
    /**
     * Normalize account number (STMBDD08 11-digit to 10-digit conversion).
     */
    private String normalizeAccountNumber(String accountNumber) {
        if (accountNumber == null) {
            return "";
        }
        
        String trimmed = accountNumber.trim();
        if (trimmed.length() == 11) {
            // STMBDD08 logic: positions 1-3 and 5-11
            return trimmed.substring(0, 3) + trimmed.substring(4);
        }
        return trimmed;
    }
    
    /**
     * Process statement date (STMBDD08 date conversion logic).
     */
    private String processStatementDate(String record) {
        try {
            if (record.length() < STMT_DATE_START + STMT_DATE_LENGTH) {
                return "";
            }
            
            String dateStr = record.substring(STMT_DATE_START, STMT_DATE_START + STMT_DATE_LENGTH);
            
            // Convert from COBOL integer date format (YYMMDD) to standard format
            if (dateStr.length() == 6 && dateStr.matches("\\d{6}")) {
                String year = "20" + dateStr.substring(0, 2);
                String month = dateStr.substring(2, 4);
                String day = dateStr.substring(4, 6);
                return day + "/" + month + "/" + year;
            }
            
            return dateStr;
        } catch (Exception e) {
            log.warn("Failed to process statement date: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Create filtered statement record.
     */
    private FilteredStatement createFilteredStatement(String rawRecord, BillPaymentRecord billPayment,
                                                    String accountNumber, int sequenceNumber, 
                                                    String processedDate) {
        return FilteredStatement.builder()
            .rawRecord(rawRecord)
            .originalBillPayment(billPayment)
            .accountNumber(accountNumber)
            .sequenceNumber(sequenceNumber)
            .processedDate(processedDate)
            .recordType(DETAIL_RECORD_TYPE)
            .filtered(true)
            .recordLength(rawRecord.length())
            .build();
    }
}
