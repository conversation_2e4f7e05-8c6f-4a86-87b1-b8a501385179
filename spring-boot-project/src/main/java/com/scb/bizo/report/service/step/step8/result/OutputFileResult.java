package com.scb.bizo.report.service.step.step8.result;

import com.scb.bizo.report.service.step.step8.model.GeneratedFile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for output file generation (EBCMAFTB).
 * 
 * Contains the results of output file generation including:
 * - List of generated files
 * - File information (names, paths, sizes)
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutputFileResult {
    
    private boolean success;
    private String errorMessage;
    private String dailyFileName;
    private String ftpFileName;
    private String dailyFilePath;
    private String ftpFilePath;
    private long dailyFileSize;
    private long ftpFileSize;
    private int fileCount;
    
    @Builder.Default
    private List<GeneratedFile> generatedFiles = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param generatedFiles List of generated files
     * @param dailyFileName Daily file name
     * @param ftpFileName FTP file name
     * @param dailyFilePath Daily file path
     * @param ftpFilePath FTP file path
     * @param dailyFileSize Daily file size
     * @param ftpFileSize FTP file size
     * @param fileCount Total file count
     * @return Successful OutputFileResult
     */
    public static OutputFileResult success(List<GeneratedFile> generatedFiles,
                                         String dailyFileName,
                                         String ftpFileName,
                                         String dailyFilePath,
                                         String ftpFilePath,
                                         long dailyFileSize,
                                         long ftpFileSize,
                                         int fileCount) {
        return OutputFileResult.builder()
            .success(true)
            .generatedFiles(generatedFiles != null ? new ArrayList<>(generatedFiles) : new ArrayList<>())
            .dailyFileName(dailyFileName)
            .ftpFileName(ftpFileName)
            .dailyFilePath(dailyFilePath)
            .ftpFilePath(ftpFilePath)
            .dailyFileSize(dailyFileSize)
            .ftpFileSize(ftpFileSize)
            .fileCount(fileCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed OutputFileResult
     */
    public static OutputFileResult failed(String errorMessage) {
        return OutputFileResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .generatedFiles(new ArrayList<>())
            .fileCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of generated files.
     * 
     * @return Generated file count
     */
    public int getGeneratedFileCount() {
        return generatedFiles != null ? generatedFiles.size() : 0;
    }
    
    /**
     * Get total file size across all generated files.
     * 
     * @return Total file size in bytes
     */
    public long getTotalFileSize() {
        if (generatedFiles == null) {
            return 0;
        }
        
        return generatedFiles.stream()
            .mapToLong(GeneratedFile::getFileSize)
            .sum();
    }
    
    /**
     * Check if any files were generated.
     * 
     * @return true if files were generated
     */
    public boolean hasGeneratedFiles() {
        return getGeneratedFileCount() > 0;
    }
    
    /**
     * Get generated files by type.
     * 
     * @param fileType File type to filter by
     * @return List of generated files of specified type
     */
    public List<GeneratedFile> getGeneratedFilesByType(String fileType) {
        if (generatedFiles == null) {
            return new ArrayList<>();
        }
        
        return generatedFiles.stream()
            .filter(file -> fileType.equals(file.getFileType()))
            .toList();
    }
}
