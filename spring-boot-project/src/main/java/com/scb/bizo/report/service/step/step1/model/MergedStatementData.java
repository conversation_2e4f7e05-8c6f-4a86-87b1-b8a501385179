package com.scb.bizo.report.service.step.step1.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Domain model representing merged statement and interbank data.
 * 
 * This model represents the consolidated data from EBCMMC04 + STMMCG01,
 * combining regular statements and interbank transactions for an account.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MergedStatementData {
    
    /**
     * Account number for this merged data.
     */
    private String accountNumber;
    
    /**
     * Regular statement records for the account.
     */
    @Builder.Default
    private List<StatementRecord> statementRecords = new ArrayList<>();
    
    /**
     * Interbank records for the account.
     */
    @Builder.Default
    private List<InterbankData> interbankRecords = new ArrayList<>();
    
    /**
     * Total number of statement records.
     */
    private int totalStatements;
    
    /**
     * Total number of interbank records.
     */
    private int totalInterbankRecords;
    
    /**
     * Timestamp when data was merged.
     */
    private LocalDateTime mergedAt;
    
    /**
     * Merge status flag.
     */
    private boolean merged;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate merged statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               (totalStatements > 0 || totalInterbankRecords > 0);
    }
    
    /**
     * Get total number of all records (statements + interbank).
     * 
     * @return Total record count
     */
    public int getTotalRecords() {
        return totalStatements + totalInterbankRecords;
    }
    
    /**
     * Check if account has statement records.
     * 
     * @return true if has statements
     */
    public boolean hasStatements() {
        return totalStatements > 0;
    }
    
    /**
     * Check if account has interbank records.
     * 
     * @return true if has interbank records
     */
    public boolean hasInterbankRecords() {
        return totalInterbankRecords > 0;
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
