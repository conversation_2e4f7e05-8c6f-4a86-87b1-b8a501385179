package com.scb.bizo.report.service.step.step7.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a processed statement from STMMCREF processing.
 * 
 * This model represents the statement data processed by STMMCREF,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - 2500-character output records with embedded reference data
 * - IM/ST branch name lookup integration
 * - EWT flag processing and product code mapping
 * - Adjustment status processing (ERP02/ERP03)
 * - COBOL decimal format conversion
 * - Balance calculation and validation
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessedStatement {
    
    /**
     * Original statement record (2516 characters).
     */
    private String originalRecord;
    
    /**
     * Generated output record (2500 characters).
     */
    private String outputRecord;
    
    /**
     * Statement key (16 characters).
     */
    private String statementKey;
    
    /**
     * Account number.
     */
    private String accountNumber;
    
    /**
     * Bank ID.
     */
    private String bankId;
    
    /**
     * Branch number.
     */
    private String branchNumber;
    
    /**
     * Branch name from IM/ST lookup.
     */
    private String branchName;
    
    /**
     * Account type (IM or ST).
     */
    private String accountType;
    
    /**
     * Statement balance.
     */
    private BigDecimal balance;
    
    /**
     * Statement amount.
     */
    private BigDecimal amount;
    
    /**
     * Debit/Credit code.
     */
    private String dcCode;
    
    /**
     * EWT transaction flag.
     */
    private boolean ewtTransaction;
    
    /**
     * Adjustment record flag.
     */
    private boolean adjustmentRecord;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate processed statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return originalRecord != null &&
               originalRecord.length() == 2516 &&
               outputRecord != null &&
               outputRecord.length() == 2500 &&
               statementKey != null &&
               statementKey.length() == 16 &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty();
    }
    
    /**
     * Check if statement is EWT transaction.
     * 
     * @return true if EWT transaction
     */
    public boolean isEwtStatement() {
        return ewtTransaction;
    }
    
    /**
     * Check if statement is adjustment record.
     * 
     * @return true if adjustment record
     */
    public boolean isAdjustmentStatement() {
        return adjustmentRecord;
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Get branch name without leading/trailing spaces.
     * 
     * @return Trimmed branch name
     */
    public String getBranchNameTrimmed() {
        return branchName != null ? branchName.trim() : null;
    }
    
    /**
     * Extract specific field from original record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOriginalField(int startPos, int length) {
        if (originalRecord == null || 
            startPos < 0 || 
            startPos + length > originalRecord.length()) {
            return null;
        }
        
        return originalRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from output record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOutputField(int startPos, int length) {
        if (outputRecord == null || 
            startPos < 0 || 
            startPos + length > outputRecord.length()) {
            return null;
        }
        
        return outputRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
