package com.scb.bizo.report.service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Main Spring Boot application class for Multicast Report Service.
 * 
 * This application migrates the COBOL multicast report processing system
 * to a modern Java Spring Boot 3.x library with virtual threads support.
 * 
 * Features:
 * - 8-step sequential workflow processing
 * - Virtual threads for scalable concurrent processing
 * - Platform-agnostic library design
 * - Complete business logic preservation from COBOL
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication
@EnableAsync
public class MulticastReportApplication {

    public static void main(String[] args) {
        SpringApplication.run(MulticastReportApplication.class, args);
    }
}
