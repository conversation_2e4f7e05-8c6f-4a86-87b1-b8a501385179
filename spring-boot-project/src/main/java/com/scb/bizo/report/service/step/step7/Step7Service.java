package com.scb.bizo.report.service.step.step7;

import com.scb.bizo.report.service.application.processor.StepProcessor;
import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step7.processor.StatementDataMergeProcessor;
import com.scb.bizo.report.service.step.step7.processor.StatementReferenceProcessor;
import com.scb.bizo.report.service.step.step7.processor.OutwardDetailProcessor;
import com.scb.bizo.report.service.step.step7.result.StatementDataMergeResult;
import com.scb.bizo.report.service.step.step7.result.StatementReferenceResult;
import com.scb.bizo.report.service.step.step7.result.OutwardDetailResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Step 7 processor handling Statement Generation.
 * 
 * This service orchestrates the three sub-processes that make up Step 7:
 * 1. EBCMMC09: Statement data merging, sorting, and VSAM file creation
 * 2. STMMCREF: Statement reference processing with IM/ST name lookup
 * 3. STMMCG07: Outward detail processing and statement generation
 * 
 * Business Logic Preserved from COBOL:
 * 
 * EBCMMC09 (JCL Job):
 * - Sorts statement data by account and sequence: SORT FIELDS=(7,10,A,113,6,A), FORMAT=CH, EQUALS
 * - Creates VSAM indexed files:
 *   * PVBBCM.BCM.BCM.PS2516.MCASH.STMT (2516-character records, 16-character key)
 *   * PVBBCM.BCM.BCM.PS1100.OUTWARD.DETL (1100-character records, 16-character key)
 * - Processes outward detail files with sorting:
 *   * PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R1ST (1038-character records)
 *   * PSPBCM.FOR.BCM.P140.PGENFERP.OUTWARD.R2ND (1038-character records)
 * - OUTREC field extraction: OUTREC FIELDS=(106,16,1,1038,46X)
 * - Sort by 16-character key: SORT FIELDS=(106,16,A), FORMAT=CH, EQUALS
 * - Creates 1100-character output records with 16-character keys
 * - Backup file creation with date stamps
 * 
 * STMMCREF (COBOL Program):
 * - Reads 3200-character statement files and processes reference data
 * - IM/ST name lookup integration:
 *   * GVBFNF.IM.IM.P140.IMLKPM.V (100-character records)
 *   * GVBFNF.ST.ST.P140.STLKPM.V (122-character records)
 * - Branch name lookup by account type (IM vs ST)
 * - EWT flag processing (CB64010022): I-PMT-EWT-FLG = 'EWT' → O-RFT-PROD-CODE
 * - Generates 2500-character output records with embedded reference data
 * - Creates 132-character report files for reconciliation
 * - Adjustment status processing: ERP02/ERP03 pattern detection
 * - RFT data processing with COBOL decimal format conversion
 * - Balance calculation and validation
 * 
 * STMMCG07 (COBOL Program):
 * - Reads account profiles with outward flag validation (ACCT-OR-FLG = 'Y')
 * - Processes 2516-character statement files and 1100-character outward detail files
 * - Outward detail matching by reference key (STMT-IN-DESC-FOR = DETL-FOR-REF-KEY)
 * - Generates 2500-character output records with embedded outward details
 * - Creates 132-character report files for processing summary
 * - Exception handling: ERP033 adjustment for unmatched transactions
 * - Date format conversion: DD/MM/YY format processing
 * - Status mapping: SC → I, CC → C, default → J
 * - Currency and amount processing with COBOL decimal format
 * - Comprehensive outward detail field mapping
 * 
 * Key Business Rules:
 * - Account must have appropriate flags for statement generation
 * - Reference data lookup and validation (IM/ST branch names)
 * - EWT flag processing and product code mapping
 * - Outward detail matching and validation
 * - Date format standardization and conversion
 * - Status management and mapping
 * - Amount processing: COBOL decimal format with implied decimals
 * - Exception handling: Business rule violations and data errors
 * - Record structure: 2500-character output with embedded details
 * - Balance calculation: Complex credit/debit handling
 * - Adjustment record generation for unmatched transactions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class Step7Service implements StepProcessor {
    
    private final StatementDataMergeProcessor statementDataMergeProcessor;    // EBCMMC09
    private final StatementReferenceProcessor statementReferenceProcessor;   // STMMCREF
    private final OutwardDetailProcessor outwardDetailProcessor;             // STMMCG07
    
    @Override
    public int getStepNumber() {
        return 7;
    }
    
    @Override
    public String getStepName() {
        return "Statement Generation";
    }
    
    @Override
    public List<String> getCobolPrograms() {
        return List.of("EBCMMC09+STMMCREF+STMMCG07");
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 7 processing (Statement Generation) for report: {}", report.getReportId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Validate input data from Step 6
            if (!canProcess(report, inputData)) {
                return StepResult.failed("Step 7 cannot be processed - invalid input or report state");
            }
            
            // Sub-step 1: Statement Data Merging and Sorting (EBCMMC09)
            log.debug("Processing statement data merge (EBCMMC09) for report: {}", report.getReportId());
            StatementDataMergeResult mergeResult = statementDataMergeProcessor.mergeStatementData(inputData);
            if (!mergeResult.isSuccess()) {
                return StepResult.failed("Statement data merge failed: " + mergeResult.getErrorMessage());
            }
            
            // Sub-step 2: Statement Reference Processing (STMMCREF)
            log.debug("Processing statement reference (STMMCREF) for report: {}", report.getReportId());
            StatementReferenceResult referenceResult = statementReferenceProcessor.processStatementReference(
                mergeResult.getMergedStatementData(), inputData);
            if (!referenceResult.isSuccess()) {
                return StepResult.failed("Statement reference processing failed: " + referenceResult.getErrorMessage());
            }
            
            // Sub-step 3: Outward Detail Processing (STMMCG07)
            log.debug("Processing outward detail (STMMCG07) for report: {}", report.getReportId());
            OutwardDetailResult outwardResult = outwardDetailProcessor.processOutwardDetail(
                referenceResult.getProcessedStatements(), mergeResult.getOutwardDetailData(), inputData);
            if (!outwardResult.isSuccess()) {
                return StepResult.failed("Outward detail processing failed: " + outwardResult.getErrorMessage());
            }
            
            // Combine all results for next step
            Map<String, Object> outputData = Map.of(
                "mergedStatementData", mergeResult.getMergedStatementData(),
                "outwardDetailData", mergeResult.getOutwardDetailData(),
                "processedStatements", referenceResult.getProcessedStatements(),
                "finalStatements", outwardResult.getFinalStatements(),
                "reportFiles", outwardResult.getReportFiles(),
                "processingMetadata", createStep7Metadata(),
                "recordCounts", Map.of(
                    "mergedStatements", mergeResult.getMergedCount(),
                    "outwardDetails", mergeResult.getOutwardDetailCount(),
                    "processedStatements", referenceResult.getProcessedCount(),
                    "finalStatements", outwardResult.getFinalStatementCount(),
                    "reportRecords", outwardResult.getReportRecordCount(),
                    "adjustmentRecords", outwardResult.getAdjustmentCount(),
                    "errorRecords", outwardResult.getErrorCount()
                )
            );
            
            long endTime = System.currentTimeMillis();
            
            StepResult result = StepResult.success("Step 7 completed successfully", outputData);
            result.setProcessingTime(startTime, endTime);
            
            log.info("Completed Step 7 processing for report: {} in {}ms", 
                report.getReportId(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("Step 7 processing failed for report: {}", report.getReportId(), e);
            
            long endTime = System.currentTimeMillis();
            StepResult result = StepResult.failed("Step 7 processing failed: " + e.getMessage());
            result.setProcessingTime(startTime, endTime);
            
            return result;
        }
    }
    
    /**
     * Create metadata for Step 7 processing.
     * 
     * @return Metadata map
     */
    private Map<String, Object> createStep7Metadata() {
        return Map.of(
            "stepNumber", 7,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "subStepsCompleted", List.of("EBCMMC09", "STMMCREF", "STMMCG07"),
            "businessLogicPreserved", true,
            "statementGenerationEnabled", true,
            "cobolMigration", Map.of(
                "originalPrograms", getCobolPrograms(),
                "migrationDate", LocalDateTime.now().toLocalDate(),
                "migrationVersion", "1.0.0",
                "recordFormats", Map.of(
                    "inputStatements", "3200 characters",
                    "mergedStatements", "2516 characters",
                    "outwardDetails", "1100 characters",
                    "processedStatements", "2500 characters",
                    "reportRecords", "132 characters"
                ),
                "businessFeatures", List.of(
                    "Statement data merging and sorting",
                    "VSAM indexed file creation (16-character keys)",
                    "IM/ST name lookup integration",
                    "EWT flag processing and product code mapping",
                    "Outward detail matching and validation",
                    "Date format conversion (DD/MM/YY)",
                    "Status mapping (SC→I, CC→C, default→J)",
                    "COBOL decimal format processing",
                    "2500-character output with embedded details",
                    "Exception handling with ERP033 adjustments",
                    "Balance calculation and validation",
                    "Comprehensive field mapping and validation"
                )
            )
        );
    }
}
