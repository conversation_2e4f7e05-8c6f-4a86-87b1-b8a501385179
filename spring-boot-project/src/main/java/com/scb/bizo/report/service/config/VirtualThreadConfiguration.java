package com.scb.bizo.report.service.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Configuration for Virtual Threads support in Spring Boot 3.x.
 * 
 * Virtual threads provide lightweight concurrency for processing
 * multicast reports without the overhead of traditional threads.
 * This is particularly beneficial for the 8-step sequential workflow
 * where each step can process multiple records concurrently.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration
@EnableAsync
public class VirtualThreadConfiguration {

    /**
     * Primary task executor using virtual threads.
     * Used for async processing of report steps.
     */
    @Bean("virtualThreadTaskExecutor")
    public Executor virtualThreadTaskExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }

    /**
     * Thread pool task executor with virtual thread factory.
     * Provides more control over thread pool configuration while
     * still using virtual threads.
     */
    @Bean("virtualThreadPoolExecutor")
    public ThreadPoolTaskExecutor virtualThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadFactory(Thread.ofVirtual().factory());
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(100);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("VirtualThread-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * Dedicated executor for step processing.
     * Each step in the 8-step workflow uses this executor
     * for internal concurrent processing.
     */
    @Bean("stepProcessingExecutor")
    public Executor stepProcessingExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }

    /**
     * Executor for file I/O operations.
     * Virtual threads are ideal for I/O-bound operations
     * like reading input files and writing output files.
     */
    @Bean("fileProcessingExecutor")
    public Executor fileProcessingExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }
}
