package com.scb.bizo.report.service.step.step3.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a processed EPP statement from STMMCG03 processing.
 * 
 * This model represents the statement data processed by STMMCG03,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - 700-character output records with embedded EPP details
 * - EBPP channel filtering with credit code validation
 * - Account and amount matching logic
 * - Error record generation for unmatched transactions
 * - EPP processing flag management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessedEppStatement {
    
    /**
     * Original statement record (550 characters).
     */
    private String originalStatement;
    
    /**
     * Matched EPP record (if any).
     */
    private EppRecord eppRecord;
    
    /**
     * Generated output record (700 characters).
     */
    private String outputRecord;
    
    /**
     * Flag indicating if statement has EPP details.
     */
    private boolean hasEppDetails;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * Error record flag.
     */
    private boolean errorRecord;
    
    /**
     * Error message for unmatched transactions.
     */
    private String errorMessage;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate processed EPP statement data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return originalStatement != null &&
               originalStatement.length() == 550 &&
               outputRecord != null &&
               outputRecord.length() == 700;
    }
    
    /**
     * Check if statement has matched EPP record.
     * 
     * @return true if has EPP match
     */
    public boolean hasEppMatch() {
        return hasEppDetails && eppRecord != null;
    }
    
    /**
     * Check if statement is EBPP channel.
     * 
     * @return true if EBPP channel
     */
    public boolean isEbppChannel() {
        if (originalStatement == null || originalStatement.length() < 108) {
            return false;
        }
        String channel = originalStatement.substring(104, 108); // STMT-IN-CHANNEL
        return "EBPP".equals(channel);
    }
    
    /**
     * Check if statement has credit code.
     * 
     * @return true if credit code
     */
    public boolean isCreditCode() {
        if (originalStatement == null || originalStatement.length() < 96) {
            return false;
        }
        String dcCode = originalStatement.substring(94, 96); // STMT-IN-DC-CODE
        return "C ".equals(dcCode);
    }
    
    /**
     * Get account number from original statement.
     * 
     * @return Account number
     */
    public String getAccountNumber() {
        if (originalStatement == null || originalStatement.length() < 10) {
            return "";
        }
        return originalStatement.substring(0, 10).trim(); // STMT-IN-ACCT-KEY
    }
    
    /**
     * Get statement amount.
     * 
     * @return Statement amount as string
     */
    public String getStatementAmount() {
        if (originalStatement == null || originalStatement.length() < 107) {
            return "";
        }
        return originalStatement.substring(92, 107).trim(); // STMT-IN-AMT
    }
    
    /**
     * Extract specific field from original statement by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractStatementField(int startPos, int length) {
        if (originalStatement == null || 
            startPos < 0 || 
            startPos + length > originalStatement.length()) {
            return null;
        }
        
        return originalStatement.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from output record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOutputField(int startPos, int length) {
        if (outputRecord == null || 
            startPos < 0 || 
            startPos + length > outputRecord.length()) {
            return null;
        }
        
        return outputRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
