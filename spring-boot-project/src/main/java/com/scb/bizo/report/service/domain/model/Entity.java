package com.scb.bizo.report.service.domain.model;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Marker annotation for domain entities.
 * 
 * This annotation is used to mark classes as domain entities in the
 * multicast report processing system. It helps distinguish between
 * entities, value objects, and other domain components.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Entity {
    
    /**
     * Optional entity name.
     * 
     * @return Entity name
     */
    String value() default "";
}
