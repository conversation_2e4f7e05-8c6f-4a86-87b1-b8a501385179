package com.scb.bizo.report.service.step.step1.result;

import com.scb.bizo.report.service.step.step1.model.StatementRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for statement processing (EBCMMC02 + STMBDD06).
 * 
 * Contains the results of processing statement data including:
 * - List of processed statement records
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatementProcessingResult {
    
    private boolean success;
    private String errorMessage;
    private int processedCount;
    private int filteredCount;
    
    @Builder.Default
    private List<StatementRecord> statementData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param statementData List of processed statement records
     * @param processedCount Number of records processed
     * @return Successful StatementProcessingResult
     */
    public static StatementProcessingResult success(List<StatementRecord> statementData, int processedCount) {
        return StatementProcessingResult.builder()
            .success(true)
            .statementData(statementData != null ? new ArrayList<>(statementData) : new ArrayList<>())
            .processedCount(processedCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed StatementProcessingResult
     */
    public static StatementProcessingResult failed(String errorMessage) {
        return StatementProcessingResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .statementData(new ArrayList<>())
            .processedCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of statement records.
     * 
     * @return Statement count
     */
    public int getStatementCount() {
        return statementData != null ? statementData.size() : 0;
    }
    
    /**
     * Check if any statements were processed.
     * 
     * @return true if statements exist
     */
    public boolean hasStatements() {
        return getStatementCount() > 0;
    }
}
