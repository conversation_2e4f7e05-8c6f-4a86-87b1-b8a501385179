package com.scb.bizo.report.service.step.step2.result;

import com.scb.bizo.report.service.step.step2.model.FilteredStatement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for bill payment statement filtering (STMBDD08).
 * 
 * Contains the results of filtering statement data including:
 * - List of filtered statement records
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillPaymentFilterResult {
    
    private boolean success;
    private String errorMessage;
    private int filteredCount;
    private int excludedCount;
    
    @Builder.Default
    private List<FilteredStatement> filteredStatements = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param filteredStatements List of filtered statement records
     * @param filteredCount Number of records filtered
     * @return Successful BillPaymentFilterResult
     */
    public static BillPaymentFilterResult success(List<FilteredStatement> filteredStatements, int filteredCount) {
        return BillPaymentFilterResult.builder()
            .success(true)
            .filteredStatements(filteredStatements != null ? new ArrayList<>(filteredStatements) : new ArrayList<>())
            .filteredCount(filteredCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed BillPaymentFilterResult
     */
    public static BillPaymentFilterResult failed(String errorMessage) {
        return BillPaymentFilterResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .filteredStatements(new ArrayList<>())
            .filteredCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of filtered statements.
     * 
     * @return Filtered statement count
     */
    public int getFilteredStatementCount() {
        return filteredStatements != null ? filteredStatements.size() : 0;
    }
    
    /**
     * Check if any statements were filtered.
     * 
     * @return true if filtered statements exist
     */
    public boolean hasFilteredStatements() {
        return getFilteredStatementCount() > 0;
    }
}
