package com.scb.bizo.report.service.domain.model;

/**
 * Enumeration representing different data formats used in report processing.
 * 
 * The multicast report system handles various data formats throughout
 * the 8-step processing workflow, from input files to final output.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public enum DataFormat {
    
    /**
     * Plain text format - used for COBOL fixed-length records.
     * This is the primary format for input files and intermediate processing.
     */
    TEXT("Plain text format"),
    
    /**
     * Binary format - used for binary data files.
     * Less common but may be needed for certain file types.
     */
    BINARY("Binary format"),
    
    /**
     * Structured format - used for Java objects and maps.
     * Primary format for internal processing between steps.
     */
    STRUCTURED("Structured object format"),
    
    /**
     * JSON format - used for API responses and configuration.
     * Used for external interfaces and configuration data.
     */
    JSON("JSON format");
    
    private final String description;
    
    DataFormat(String description) {
        this.description = description;
    }
    
    /**
     * Get human-readable description of the format.
     * 
     * @return Format description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Check if format is text-based.
     * 
     * @return true if text-based
     */
    public boolean isTextBased() {
        return this == TEXT || this == JSON;
    }
    
    /**
     * Check if format is binary.
     * 
     * @return true if binary
     */
    public boolean isBinary() {
        return this == BINARY;
    }
    
    /**
     * Check if format is structured (object-based).
     * 
     * @return true if structured
     */
    public boolean isStructured() {
        return this == STRUCTURED;
    }
}
