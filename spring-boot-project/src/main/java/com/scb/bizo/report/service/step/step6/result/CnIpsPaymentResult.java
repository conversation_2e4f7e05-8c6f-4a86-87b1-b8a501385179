package com.scb.bizo.report.service.step.step6.result;

import com.scb.bizo.report.service.step.step6.model.CnIpsPayment;
import com.scb.bizo.report.service.step.step6.model.ProcessedCnIpsPayment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for CN/IPS payment processing (STMMCG05).
 * 
 * Contains the results of processing CN/IPS payments including:
 * - List of processed CN/IPS payments
 * - List of CN/IPS detail records
 * - Exception reports for unmatched/invalid transactions
 * - Processing statistics including EWT and product mapping counts
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CnIpsPaymentResult {
    
    private boolean success;
    private String errorMessage;
    private int processedCount;
    private int cnIpsDetailCount;
    private int exceptionCount;
    private int ewtCount;
    private int productCodeMappingCount;
    private int errorCount;
    
    @Builder.Default
    private List<ProcessedCnIpsPayment> processedPayments = new ArrayList<>();
    
    @Builder.Default
    private List<CnIpsPayment> cnIpsDetails = new ArrayList<>();
    
    @Builder.Default
    private List<String> exceptionReports = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param processedPayments List of processed CN/IPS payments
     * @param cnIpsDetails List of CN/IPS detail records
     * @param exceptionReports List of exception reports
     * @param processedCount Number of payments processed
     * @param cnIpsDetailCount Number of CN/IPS details
     * @param exceptionCount Number of exception records
     * @param ewtCount Number of EWT transactions
     * @param productCodeMappingCount Number of product code mappings
     * @param errorCount Number of error records
     * @return Successful CnIpsPaymentResult
     */
    public static CnIpsPaymentResult success(List<ProcessedCnIpsPayment> processedPayments,
                                           List<CnIpsPayment> cnIpsDetails,
                                           List<String> exceptionReports,
                                           int processedCount, 
                                           int cnIpsDetailCount, 
                                           int exceptionCount,
                                           int ewtCount,
                                           int productCodeMappingCount,
                                           int errorCount) {
        return CnIpsPaymentResult.builder()
            .success(true)
            .processedPayments(processedPayments != null ? new ArrayList<>(processedPayments) : new ArrayList<>())
            .cnIpsDetails(cnIpsDetails != null ? new ArrayList<>(cnIpsDetails) : new ArrayList<>())
            .exceptionReports(exceptionReports != null ? new ArrayList<>(exceptionReports) : new ArrayList<>())
            .processedCount(processedCount)
            .cnIpsDetailCount(cnIpsDetailCount)
            .exceptionCount(exceptionCount)
            .ewtCount(ewtCount)
            .productCodeMappingCount(productCodeMappingCount)
            .errorCount(errorCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed CnIpsPaymentResult
     */
    public static CnIpsPaymentResult failed(String errorMessage) {
        return CnIpsPaymentResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .processedPayments(new ArrayList<>())
            .cnIpsDetails(new ArrayList<>())
            .exceptionReports(new ArrayList<>())
            .processedCount(0)
            .cnIpsDetailCount(0)
            .exceptionCount(0)
            .ewtCount(0)
            .productCodeMappingCount(0)
            .errorCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of processed payments.
     * 
     * @return Processed payment count
     */
    public int getProcessedPaymentCount() {
        return processedPayments != null ? processedPayments.size() : 0;
    }
    
    /**
     * Get total number of CN/IPS details.
     * 
     * @return CN/IPS detail count
     */
    public int getCnIpsDetailRecordCount() {
        return cnIpsDetails != null ? cnIpsDetails.size() : 0;
    }
    
    /**
     * Get total number of exception reports.
     * 
     * @return Exception report count
     */
    public int getExceptionReportCount() {
        return exceptionReports != null ? exceptionReports.size() : 0;
    }
    
    /**
     * Check if any payments were processed.
     * 
     * @return true if processed payments exist
     */
    public boolean hasProcessedPayments() {
        return getProcessedPaymentCount() > 0;
    }
    
    /**
     * Check if any CN/IPS details were created.
     * 
     * @return true if CN/IPS details exist
     */
    public boolean hasCnIpsDetails() {
        return getCnIpsDetailRecordCount() > 0;
    }
    
    /**
     * Check if any exception reports were generated.
     * 
     * @return true if exception reports exist
     */
    public boolean hasExceptionReports() {
        return getExceptionReportCount() > 0;
    }
    
    /**
     * Get success rate percentage.
     * 
     * @return Success rate percentage
     */
    public double getSuccessRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return ((processedCount - errorCount - exceptionCount) * 100.0) / processedCount;
    }
    
    /**
     * Get CN/IPS matching rate percentage.
     * 
     * @return CN/IPS matching rate percentage
     */
    public double getCnIpsMatchingRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (cnIpsDetailCount * 100.0) / processedCount;
    }
    
    /**
     * Get exception rate percentage.
     * 
     * @return Exception rate percentage
     */
    public double getExceptionRate() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (exceptionCount * 100.0) / processedCount;
    }
    
    /**
     * Get EWT percentage.
     * 
     * @return EWT percentage
     */
    public double getEwtPercentage() {
        if (cnIpsDetailCount == 0) {
            return 0.0;
        }
        return (ewtCount * 100.0) / cnIpsDetailCount;
    }
    
    /**
     * Get product code mapping percentage.
     * 
     * @return Product code mapping percentage
     */
    public double getProductCodeMappingPercentage() {
        if (cnIpsDetailCount == 0) {
            return 0.0;
        }
        return (productCodeMappingCount * 100.0) / cnIpsDetailCount;
    }
}
