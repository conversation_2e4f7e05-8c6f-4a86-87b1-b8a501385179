package com.scb.bizo.report.service.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Value object representing the result of a processing step.
 * 
 * Contains all information about step execution including success/failure status,
 * output data, error messages, and processing metrics.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StepResult {
    
    private boolean success;
    private String message;
    private long processingTime;
    
    @Builder.Default
    private Map<String, Object> outputData = new HashMap<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Create a successful step result.
     * 
     * @param message Success message
     * @param outputData Output data from step processing
     * @return Successful StepResult
     */
    public static StepResult success(String message, Map<String, Object> outputData) {
        return StepResult.builder()
            .success(true)
            .message(message)
            .outputData(outputData != null ? new HashMap<>(outputData) : new HashMap<>())
            .build();
    }
    
    /**
     * Create a successful step result with simple message.
     * 
     * @param message Success message
     * @return Successful StepResult
     */
    public static StepResult success(String message) {
        return success(message, new HashMap<>());
    }
    
    /**
     * Create a failed step result.
     * 
     * @param message Error message
     * @return Failed StepResult
     */
    public static StepResult failed(String message) {
        return StepResult.builder()
            .success(false)
            .message(message)
            .errors(List.of(message))
            .build();
    }
    
    /**
     * Create a failed step result with multiple errors.
     * 
     * @param message Primary error message
     * @param errors List of detailed error messages
     * @return Failed StepResult
     */
    public static StepResult failed(String message, List<String> errors) {
        return StepResult.builder()
            .success(false)
            .message(message)
            .errors(new ArrayList<>(errors))
            .build();
    }
    
    /**
     * Check if step result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Add output data entry.
     * 
     * @param key Data key
     * @param value Data value
     */
    public void addOutputData(String key, Object value) {
        if (outputData == null) {
            outputData = new HashMap<>();
        }
        outputData.put(key, value);
    }
    
    /**
     * Get output data value.
     * 
     * @param key Data key
     * @return Data value or null
     */
    public Object getOutputData(String key) {
        return outputData != null ? outputData.get(key) : null;
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
    
    /**
     * Get error message summary.
     * 
     * @return Combined error messages
     */
    public String getErrorMessage() {
        if (!hasErrors()) {
            return null;
        }
        return String.join("; ", errors);
    }
    
    /**
     * Set processing time in milliseconds.
     * 
     * @param startTime Start time in milliseconds
     * @param endTime End time in milliseconds
     */
    public void setProcessingTime(long startTime, long endTime) {
        this.processingTime = endTime - startTime;
    }
    
    /**
     * Get processing time in seconds.
     * 
     * @return Processing time in seconds
     */
    public double getProcessingTimeSeconds() {
        return processingTime / 1000.0;
    }
    
    /**
     * Create a copy of this result with additional data.
     * 
     * @param additionalData Additional output data
     * @return New StepResult with combined data
     */
    public StepResult withAdditionalData(Map<String, Object> additionalData) {
        Map<String, Object> combinedData = new HashMap<>(this.outputData);
        if (additionalData != null) {
            combinedData.putAll(additionalData);
        }
        
        return StepResult.builder()
            .success(this.success)
            .message(this.message)
            .processingTime(this.processingTime)
            .outputData(combinedData)
            .errors(new ArrayList<>(this.errors))
            .metadata(new HashMap<>(this.metadata))
            .build();
    }
}
