package com.scb.bizo.report.service.step.step7.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a statement record from EBCMMC09 processing.
 * 
 * This model represents the statement data processed by EBCMMC09,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * COBOL Record Structure:
 * - Input records: 3200-character records from previous steps
 * - Output records: 2516-character records for statement processing
 * - Sort fields: (7,10,A,113,6,A) - account and sequence sorting
 * - VSAM keys: 16-character keys for indexed access
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatementRecord {
    
    /**
     * Raw input record (3200 characters).
     */
    private String rawRecord;
    
    /**
     * Output record (2516 characters).
     */
    private String outputRecord;
    
    /**
     * Statement key (16 characters) - account + sequence.
     */
    private String statementKey;
    
    /**
     * Account number extracted from record (positions 7-16).
     */
    private String accountNumber;
    
    /**
     * Sequence number extracted from record (positions 113-118).
     */
    private String sequenceNumber;
    
    /**
     * Record length validation.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate statement record data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return rawRecord != null &&
               outputRecord != null &&
               statementKey != null &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               outputRecord.length() == 2516 &&
               statementKey.length() == 16;
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Get sequence number without leading/trailing spaces.
     * 
     * @return Trimmed sequence number
     */
    public String getSequenceNumberTrimmed() {
        return sequenceNumber != null ? sequenceNumber.trim() : null;
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractRawField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from output record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOutputField(int startPos, int length) {
        if (outputRecord == null || 
            startPos < 0 || 
            startPos + length > outputRecord.length()) {
            return null;
        }
        
        return outputRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
