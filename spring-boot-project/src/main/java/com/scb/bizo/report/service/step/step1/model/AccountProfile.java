package com.scb.bizo.report.service.step.step1.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing an account profile from ERP system.
 * 
 * This model represents the account data processed by EBCMMC01,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * COBOL Record Structure (200 characters):
 * - Positions 1-16: Various account fields
 * - Positions 17-26: Account number (key field)
 * - Positions 27-150: Additional account data
 * - Position 151: Multicash flag ('Y' or 'N')
 * - Positions 152-200: Additional fields
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountProfile {
    
    /**
     * Account number - primary key field.
     * Extracted from positions 17-26 in COBOL record.
     */
    private String accountNumber;
    
    /**
     * Multicash enabled flag.
     * Derived from position 151 in COBOL record.
     */
    private boolean multicashEnabled;
    
    /**
     * Raw COBOL record (200 characters).
     * Preserved for exact business logic compatibility.
     */
    private String rawRecord;
    
    /**
     * Record length validation.
     * Must be exactly 200 characters per COBOL specification.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate account profile data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return accountNumber != null && 
               !accountNumber.trim().isEmpty() &&
               recordLength == 200 &&
               rawRecord != null &&
               rawRecord.length() == 200;
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Check if account is eligible for multicash processing.
     * 
     * @return true if eligible
     */
    public boolean isMulticashEligible() {
        return multicashEnabled && isValid();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Get account type from record if available.
     * This would be extracted from specific positions in the COBOL record.
     * 
     * @return Account type or null
     */
    public String getAccountType() {
        // Implementation would depend on specific COBOL record layout
        // For now, return from metadata if available
        return (String) getMetadata("accountType");
    }
    
    /**
     * Get branch code from record if available.
     * 
     * @return Branch code or null
     */
    public String getBranchCode() {
        // Implementation would depend on specific COBOL record layout
        return (String) getMetadata("branchCode");
    }
}
