package com.scb.bizo.report.service.step.step6.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing a CN/IPS payment detail from STMMCG05 processing.
 * 
 * This model represents the CN/IPS detail data extracted and processed by STMMCG05,
 * maintaining the structure and business rules from the original COBOL program.
 * 
 * Key Features:
 * - CN/IPS detail record management (950 characters)
 * - Product code validation and mapping (DCP → BNT)
 * - EWT pattern validation and processing
 * - Status management ('C', 'J' handling)
 * - COBOL decimal format processing
 * - 8-digit cheque number support
 * - WHT (Withholding Tax) processing
 * - Multi-currency support
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CnIpsPayment {
    
    /**
     * Associated CN/IPS record.
     */
    private CnIpsRecord cnIpsRecord;
    
    /**
     * Account number for this CN/IPS payment.
     */
    private String accountNumber;
    
    /**
     * Product code (after mapping).
     */
    private String productCode;
    
    /**
     * Processing status flag.
     */
    private boolean processed;
    
    /**
     * EWT enabled flag.
     */
    private boolean ewtEnabled;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate CN/IPS payment data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return cnIpsRecord != null &&
               cnIpsRecord.isValid() &&
               accountNumber != null &&
               !accountNumber.trim().isEmpty() &&
               productCode != null &&
               !productCode.trim().isEmpty();
    }
    
    /**
     * Check if CN/IPS payment is EWT enabled.
     * 
     * @return true if EWT enabled
     */
    public boolean isEwtPayment() {
        return ewtEnabled;
    }
    
    /**
     * Get account number without leading/trailing spaces.
     * 
     * @return Trimmed account number
     */
    public String getAccountNumberTrimmed() {
        return accountNumber != null ? accountNumber.trim() : null;
    }
    
    /**
     * Get product code without leading/trailing spaces.
     * 
     * @return Trimmed product code
     */
    public String getProductCodeTrimmed() {
        return productCode != null ? productCode.trim() : null;
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
