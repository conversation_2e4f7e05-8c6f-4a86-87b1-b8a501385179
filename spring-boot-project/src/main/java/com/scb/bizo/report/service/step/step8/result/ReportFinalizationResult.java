package com.scb.bizo.report.service.step.step8.result;

import com.scb.bizo.report.service.step.step8.model.FinalizationData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for report finalization (EBCMAFTB).
 * 
 * Contains the results of report finalization including:
 * - Finalization data with audit trail and metrics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportFinalizationResult {
    
    private boolean success;
    private String errorMessage;
    
    private FinalizationData finalizationData;
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param finalizationData Finalization data
     * @return Successful ReportFinalizationResult
     */
    public static ReportFinalizationResult success(FinalizationData finalizationData) {
        return ReportFinalizationResult.builder()
            .success(true)
            .finalizationData(finalizationData)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed ReportFinalizationResult
     */
    public static ReportFinalizationResult failed(String errorMessage) {
        return ReportFinalizationResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Check if finalization data exists.
     * 
     * @return true if finalization data exists
     */
    public boolean hasFinalizationData() {
        return finalizationData != null;
    }
    
    /**
     * Check if report was finalized successfully.
     * 
     * @return true if report finalized successfully
     */
    public boolean isReportFinalized() {
        return success && finalizationData != null && finalizationData.isCompleted();
    }
}
