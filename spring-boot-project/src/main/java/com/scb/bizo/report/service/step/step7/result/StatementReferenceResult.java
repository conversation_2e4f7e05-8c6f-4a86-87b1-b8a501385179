package com.scb.bizo.report.service.step.step7.result;

import com.scb.bizo.report.service.step.step7.model.ProcessedStatement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for statement reference processing (STMMCREF).
 * 
 * Contains the results of processing statement reference data including:
 * - List of processed statements with reference data
 * - Processing statistics including EWT and adjustment counts
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatementReferenceResult {
    
    private boolean success;
    private String errorMessage;
    private int processedCount;
    private int ewtCount;
    private int adjustmentCount;
    
    @Builder.Default
    private List<ProcessedStatement> processedStatements = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param processedStatements List of processed statements
     * @param processedCount Number of statements processed
     * @param ewtCount Number of EWT transactions
     * @param adjustmentCount Number of adjustment records
     * @return Successful StatementReferenceResult
     */
    public static StatementReferenceResult success(List<ProcessedStatement> processedStatements,
                                                  int processedCount, 
                                                  int ewtCount, 
                                                  int adjustmentCount) {
        return StatementReferenceResult.builder()
            .success(true)
            .processedStatements(processedStatements != null ? new ArrayList<>(processedStatements) : new ArrayList<>())
            .processedCount(processedCount)
            .ewtCount(ewtCount)
            .adjustmentCount(adjustmentCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed StatementReferenceResult
     */
    public static StatementReferenceResult failed(String errorMessage) {
        return StatementReferenceResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .processedStatements(new ArrayList<>())
            .processedCount(0)
            .ewtCount(0)
            .adjustmentCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of processed statements.
     * 
     * @return Processed statement count
     */
    public int getProcessedStatementCount() {
        return processedStatements != null ? processedStatements.size() : 0;
    }
    
    /**
     * Check if any statements were processed.
     * 
     * @return true if processed statements exist
     */
    public boolean hasProcessedStatements() {
        return getProcessedStatementCount() > 0;
    }
    
    /**
     * Get EWT percentage.
     * 
     * @return EWT percentage
     */
    public double getEwtPercentage() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (ewtCount * 100.0) / processedCount;
    }
    
    /**
     * Get adjustment percentage.
     * 
     * @return Adjustment percentage
     */
    public double getAdjustmentPercentage() {
        if (processedCount == 0) {
            return 0.0;
        }
        return (adjustmentCount * 100.0) / processedCount;
    }
}
