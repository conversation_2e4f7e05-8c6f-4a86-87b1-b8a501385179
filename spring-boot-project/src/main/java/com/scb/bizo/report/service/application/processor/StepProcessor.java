package com.scb.bizo.report.service.application.processor;

import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.StepResult;

/**
 * Interface for processing individual steps in the 8-step multicast report workflow.
 * 
 * Each step processor implements the business logic for one step of the workflow,
 * preserving the original COBOL business rules while providing a modern Java interface.
 * 
 * Implementation Guidelines:
 * - Preserve all COBOL business logic exactly
 * - Handle errors gracefully with detailed error messages
 * - Provide comprehensive logging for debugging
 * - Validate input data before processing
 * - Return structured output data for next step
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface StepProcessor {
    
    /**
     * Get the step number this processor handles.
     * 
     * @return Step number (1-8)
     */
    int getStepNumber();
    
    /**
     * Process the step with the given input data.
     * 
     * @param report The multicast report being processed
     * @param inputData Input data from previous step (or initial input for step 1)
     * @return Step processing result
     */
    StepResult processStep(MulticastReport report, ReportData inputData);
    
    /**
     * Validate that the step can be processed with the given input.
     * 
     * @param report The multicast report
     * @param inputData Input data to validate
     * @return true if step can be processed
     */
    default boolean canProcess(MulticastReport report, ReportData inputData) {
        return report != null && 
               report.canProcessStep(getStepNumber()) && 
               inputData != null && 
               inputData.isValid();
    }
    
    /**
     * Get the step name for logging and display purposes.
     * 
     * @return Human-readable step name
     */
    default String getStepName() {
        return "Step " + getStepNumber();
    }
    
    /**
     * Get the COBOL programs that this step replaces.
     * 
     * @return List of COBOL program names
     */
    default java.util.List<String> getCobolPrograms() {
        return java.util.List.of();
    }
}
