package com.scb.bizo.report.service.step.step8.model;

import com.scb.bizo.report.service.step.step8.processor.FinalOutputProcessor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Domain model representing final output data from EBCMAFTB processing.
 * 
 * This model represents the final aggregated data processed by EBCMAFTB,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * Key Features:
 * - Aggregated data from all 7 processing steps
 * - Final statement records ready for output
 * - Processing summary and statistics
 * - Data validation and integrity checks
 * - Preparation for file generation
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinalOutputData {
    
    /**
     * Report ID for tracking.
     */
    private String reportId;
    
    /**
     * Final statement records (2500 characters each).
     */
    @Builder.Default
    private List<String> finalStatements = new ArrayList<>();
    
    /**
     * Aggregated data from all processing steps.
     */
    @Builder.Default
    private Map<String, Object> aggregatedStepData = new HashMap<>();
    
    /**
     * Total number of records processed.
     */
    private int totalRecords;
    
    /**
     * Processing timestamp.
     */
    @Builder.Default
    private LocalDateTime processedAt = LocalDateTime.now();
    
    /**
     * Processing summary.
     */
    private FinalOutputProcessor.ProcessingSummary processingSummary;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Create empty final output data.
     * 
     * @return Empty FinalOutputData
     */
    public static FinalOutputData empty() {
        return FinalOutputData.builder()
            .finalStatements(new ArrayList<>())
            .aggregatedStepData(new HashMap<>())
            .totalRecords(0)
            .build();
    }
    
    /**
     * Validate final output data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return reportId != null &&
               !reportId.trim().isEmpty() &&
               finalStatements != null &&
               totalRecords >= 0 &&
               totalRecords == finalStatements.size();
    }
    
    /**
     * Check if final output data is empty.
     * 
     * @return true if empty
     */
    public boolean isEmpty() {
        return finalStatements == null || finalStatements.isEmpty();
    }
    
    /**
     * Get total number of final statements.
     * 
     * @return Final statement count
     */
    public int getFinalStatementCount() {
        return finalStatements != null ? finalStatements.size() : 0;
    }
    
    /**
     * Add final statement.
     * 
     * @param statement Final statement to add
     */
    public void addFinalStatement(String statement) {
        if (finalStatements == null) {
            finalStatements = new ArrayList<>();
        }
        finalStatements.add(statement);
        totalRecords = finalStatements.size();
    }
    
    /**
     * Add aggregated step data.
     * 
     * @param stepNumber Step number
     * @param stepData Step data
     */
    public void addStepData(int stepNumber, Object stepData) {
        if (aggregatedStepData == null) {
            aggregatedStepData = new HashMap<>();
        }
        aggregatedStepData.put("step" + stepNumber + "Data", stepData);
    }
    
    /**
     * Get aggregated step data.
     * 
     * @param stepNumber Step number
     * @return Step data or null
     */
    public Object getStepData(int stepNumber) {
        if (aggregatedStepData == null) {
            return null;
        }
        return aggregatedStepData.get("step" + stepNumber + "Data");
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
