package com.scb.bizo.report.service.step.step3.result;

import com.scb.bizo.report.service.step.step3.model.EppRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result object for EPP data merge processing (EBCMMC06).
 * 
 * Contains the results of merging EPP and EPPD data including:
 * - List of merged EPP records
 * - Processing statistics
 * - Success/failure status
 * - Error messages if any
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EppDataMergeResult {
    
    private boolean success;
    private String errorMessage;
    private int mergedCount;
    private int filteredCount;
    private int duplicateCount;
    
    @Builder.Default
    private List<EppRecord> mergedEppData = new ArrayList<>();
    
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * Create successful result.
     * 
     * @param mergedEppData List of merged EPP records
     * @param mergedCount Number of records merged
     * @return Successful EppDataMergeResult
     */
    public static EppDataMergeResult success(List<EppRecord> mergedEppData, int mergedCount) {
        return EppDataMergeResult.builder()
            .success(true)
            .mergedEppData(mergedEppData != null ? new ArrayList<>(mergedEppData) : new ArrayList<>())
            .mergedCount(mergedCount)
            .build();
    }
    
    /**
     * Create failed result.
     * 
     * @param errorMessage Error message
     * @return Failed EppDataMergeResult
     */
    public static EppDataMergeResult failed(String errorMessage) {
        return EppDataMergeResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .errors(List.of(errorMessage))
            .mergedEppData(new ArrayList<>())
            .mergedCount(0)
            .build();
    }
    
    /**
     * Add error message.
     * 
     * @param error Error message
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.success = false;
    }
    
    /**
     * Check if result has errors.
     * 
     * @return true if has errors
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * Get total number of merged EPP records.
     * 
     * @return Merged EPP count
     */
    public int getMergedEppCount() {
        return mergedEppData != null ? mergedEppData.size() : 0;
    }
    
    /**
     * Check if any EPP records were merged.
     * 
     * @return true if merged EPP data exists
     */
    public boolean hasMergedEppData() {
        return getMergedEppCount() > 0;
    }
}
