package com.scb.bizo.report.service.step.step4;

import com.scb.bizo.report.service.application.processor.StepProcessor;
import com.scb.bizo.report.service.domain.model.MulticastReport;
import com.scb.bizo.report.service.domain.model.ReportData;
import com.scb.bizo.report.service.domain.model.StepResult;
import com.scb.bizo.report.service.step.step4.processor.LccDataMergeProcessor;
import com.scb.bizo.report.service.step.step4.processor.LccStatementProcessor;
import com.scb.bizo.report.service.step.step4.result.LccDataMergeResult;
import com.scb.bizo.report.service.step.step4.result.LccStatementResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Step 4 processor handling LCC (Local Clearing Collection) processing.
 * 
 * This service orchestrates the two sub-processes that make up Step 4:
 * 1. EBCMMC07: LCC data merging and sorting
 * 2. STMMCG04: LCC statement processing and detail generation
 * 
 * Business Logic Preserved from COBOL:
 * 
 * EBCMMC07 (JCL Job):
 * - Merges statement and LCC detail data from multiple sources:
 *   * PVBBCM.BCM.BCM.PS700.GENSTMT.DETL (700-character statement records)
 *   * PSPBCM.BC01.BCM.BCBCMFIO.&DAYDATE.V1.TM2 (600-character LCC detail records)
 * - Sorts LCC detail data by account number (positions 1-29)
 *   * SORT FIELDS=(1,29,A), FORMAT=CH, EQUALS
 * - Creates indexed VSAM files:
 *   * PVBBCM.BCM.BCM.PS700.GENSTMT.DETL (700-character records, 16-character key)
 *   * PVBBCM.BCM.BCM.P140.BCDATINF.MCASH (600-character records, 29-character key)
 * - Handles file space allocation and management
 * 
 * STMMCG04 (COBOL Program):
 * - Reads account profiles with LCC flag validation (ACCT-LCC-FLG = 'Y')
 * - Processes statement files (700 characters) and detail files (600 characters)
 * - Matches LCC transactions by account and reference:
 *   * STMT-IN-ACCT-KEY = BC-ACC-NO
 *   * BC-REF-KEY = STMT-IN-DESC40(1:16)
 * - Filters by channel types: 'BC  ', 'OTCL', 'AIR ', 'ATS '
 * - Generates 1300-character output records with embedded LCC details
 * - Handles LCC transaction types: BC, CSH, ABC, CBD, BR, LCC
 * - Creates difference adjustment records for amount reconciliation
 * - Manages balance calculations with credit/debit processing
 * 
 * Key Business Rules:
 * - Account must have ACCT-LCC-FLG = 'Y' for LCC processing
 * - Channel filtering: BC, OTCL, AIR, ATS channels only
 * - Transaction type validation: BC, CSH, ABC, CBD, BR, LCC
 * - Status validation: S, C, P, A, R, O status codes
 * - Amount matching and reconciliation with difference adjustments
 * - Reference key matching: BC-REF-KEY = STMT-IN-DESC40(1:16)
 * - Balance calculation: WK-NEW-STMT-IN-BAL with C/D handling
 * - Record structure: 1300-character output with 600-character LCC detail section
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class Step4Service implements StepProcessor {
    
    private final LccDataMergeProcessor lccDataMergeProcessor;        // EBCMMC07
    private final LccStatementProcessor lccStatementProcessor;       // STMMCG04
    
    @Override
    public int getStepNumber() {
        return 4;
    }
    
    @Override
    public String getStepName() {
        return "LCC Processing";
    }
    
    @Override
    public List<String> getCobolPrograms() {
        return List.of("EBCMMC07+STMMCG04");
    }
    
    @Override
    public StepResult processStep(MulticastReport report, ReportData inputData) {
        log.info("Starting Step 4 processing for report: {}", report.getReportId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Validate input data from Step 3
            if (!canProcess(report, inputData)) {
                return StepResult.failed("Step 4 cannot be processed - invalid input or report state");
            }
            
            // Sub-step 1: LCC Data Merging and Sorting (EBCMMC07)
            log.debug("Processing LCC data merge (EBCMMC07) for report: {}", report.getReportId());
            LccDataMergeResult mergeResult = lccDataMergeProcessor.mergeLccData(inputData);
            if (!mergeResult.isSuccess()) {
                return StepResult.failed("LCC data merge failed: " + mergeResult.getErrorMessage());
            }
            
            // Sub-step 2: LCC Statement Processing (STMMCG04)
            log.debug("Processing LCC statements (STMMCG04) for report: {}", report.getReportId());
            LccStatementResult statementResult = lccStatementProcessor.processLccStatements(
                mergeResult.getMergedLccData(), inputData);
            if (!statementResult.isSuccess()) {
                return StepResult.failed("LCC statement processing failed: " + statementResult.getErrorMessage());
            }
            
            // Combine all results for next step
            Map<String, Object> outputData = Map.of(
                "mergedLccData", mergeResult.getMergedLccData(),
                "processedStatements", statementResult.getProcessedStatements(),
                "lccDetails", statementResult.getLccDetails(),
                "processingMetadata", createStep4Metadata(),
                "recordCounts", Map.of(
                    "mergedLccRecords", mergeResult.getMergedCount(),
                    "processedStatements", statementResult.getProcessedCount(),
                    "lccDetailRecords", statementResult.getLccDetailCount(),
                    "adjustmentRecords", statementResult.getAdjustmentCount(),
                    "errorRecords", statementResult.getErrorCount()
                )
            );
            
            long endTime = System.currentTimeMillis();
            
            StepResult result = StepResult.success("Step 4 completed successfully", outputData);
            result.setProcessingTime(startTime, endTime);
            
            log.info("Completed Step 4 processing for report: {} in {}ms", 
                report.getReportId(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("Step 4 processing failed for report: {}", report.getReportId(), e);
            
            long endTime = System.currentTimeMillis();
            StepResult result = StepResult.failed("Step 4 processing failed: " + e.getMessage());
            result.setProcessingTime(startTime, endTime);
            
            return result;
        }
    }
    
    /**
     * Create metadata for Step 4 processing.
     * 
     * @return Metadata map
     */
    private Map<String, Object> createStep4Metadata() {
        return Map.of(
            "stepNumber", 4,
            "processedAt", LocalDateTime.now(),
            "processorVersion", "1.0.0",
            "subStepsCompleted", List.of("EBCMMC07", "STMMCG04"),
            "businessLogicPreserved", true,
            "lccProcessingEnabled", true,
            "cobolMigration", Map.of(
                "originalPrograms", getCobolPrograms(),
                "migrationDate", LocalDateTime.now().toLocalDate(),
                "migrationVersion", "1.0.0",
                "recordFormats", Map.of(
                    "statementRecords", "700 characters",
                    "lccDetailRecords", "600 characters",
                    "outputRecords", "1300 characters"
                ),
                "businessFeatures", List.of(
                    "LCC data merging and sorting",
                    "Account LCC flag validation",
                    "Channel filtering (BC, OTCL, AIR, ATS)",
                    "Transaction type validation",
                    "Reference key matching",
                    "Amount reconciliation with adjustments",
                    "1300-character output with LCC details"
                )
            )
        );
    }
}
