package com.scb.bizo.report.service.step.step7.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Domain model representing an outward detail record from EBCMMC09 processing.
 * 
 * This model represents the outward detail data processed by EBCMMC09,
 * maintaining the structure and business rules from the original COBOL job.
 * 
 * COBOL Record Structure:
 * - Input records: 1038-character records from FOR outward files
 * - Output records: 1100-character records with OUTREC formatting
 * - Sort fields: (106,16,A) - reference key sorting
 * - VSAM keys: 16-character keys for indexed access
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutwardDetailRecord {
    
    /**
     * Raw input record (1038 characters).
     */
    private String rawRecord;
    
    /**
     * Output record (1100 characters) with OUTREC formatting.
     */
    private String outputRecord;
    
    /**
     * Reference key (16 characters) from positions 106-121.
     */
    private String referenceKey;
    
    /**
     * Record length validation.
     */
    private int recordLength;
    
    /**
     * Additional metadata for processing.
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate outward detail record data.
     * 
     * @return true if valid
     */
    public boolean isValid() {
        return rawRecord != null &&
               outputRecord != null &&
               referenceKey != null &&
               !referenceKey.trim().isEmpty() &&
               outputRecord.length() == 1100 &&
               referenceKey.length() == 16;
    }
    
    /**
     * Get reference key without leading/trailing spaces.
     * 
     * @return Trimmed reference key
     */
    public String getReferenceKeyTrimmed() {
        return referenceKey != null ? referenceKey.trim() : null;
    }
    
    /**
     * Extract specific field from raw record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractRawField(int startPos, int length) {
        if (rawRecord == null || 
            startPos < 0 || 
            startPos + length > rawRecord.length()) {
            return null;
        }
        
        return rawRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Extract specific field from output record by position.
     * 
     * @param startPos Start position (0-based)
     * @param length Field length
     * @return Extracted field value
     */
    public String extractOutputField(int startPos, int length) {
        if (outputRecord == null || 
            startPos < 0 || 
            startPos + length > outputRecord.length()) {
            return null;
        }
        
        return outputRecord.substring(startPos, startPos + length).trim();
    }
    
    /**
     * Add metadata entry.
     * 
     * @param key Metadata key
     * @param value Metadata value
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value.
     * 
     * @param key Metadata key
     * @return Metadata value or null
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
